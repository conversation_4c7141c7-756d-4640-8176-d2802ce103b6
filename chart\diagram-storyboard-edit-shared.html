<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 修改分镜（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>🎬 共享业务流程: 修改分镜（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>这是一个独立的、可复用的分镜修改业务流程，专门设计为供其他业务流程引用的标准化组件。任何需要分镜编辑功能的业务流程（如项目编辑、分镜管理等）都可以通过参数化调用这个统一的分镜修改流程。所有UI交互统一由"Py视频创作工具前端"处理，确保用户体验的一致性。支持多种调用模式：快速编辑、高级编辑、预览模式等，通过不同参数实现不同的业务表现。此流程不涉及AI生成，无积分消费，主要是数据库操作。
        </div>

        <div class="reference-box">
            <h4>📋 引用说明</h4>
            <p><strong>本流程可被以下业务流程引用：</strong></p>
            <ul>
                <li>• 项目分镜编辑流程（项目管理中的分镜修改）</li>
                <li>• 分镜库管理流程（分镜模板的编辑和更新）</li>
                <li>• 任何其他需要分镜修改功能的业务流程</li>
            </ul>
            <p><strong>调用方式：</strong>通过标准化的参数接口调用，支持不同的编辑模式和权限控制。</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant F as Py视频创作工具前端
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over F: 🎬 用户发起分镜修改功能

    F->>F: 启动分镜修改流程<br/>参数: {storyboard_id, mode, permissions}

    Note over F: 📋 参数化配置处理
    alt mode=quick（快速编辑）
        Note over F: 显示简化的动作选择界面，预设常用动作
    else mode=advanced（高级编辑）
        Note over F: 提供完整的编辑功能，包含所有动作库
    else mode=preview（预览模式）
        Note over F: 只读模式，仅用于预览分镜效果
    end

    Note over F: 🎨 统一UI处理
    F->>F: 弹出分镜编辑罩层界面
    F->>A: 获取分镜编辑初始数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
    else Token验证通过
        A->>DB: 查询用户状态和权限
        
        Note over A: 🎬 分镜权限验证
        F->>A: GET /py-api/storyboards/{id}/edit-permission
        A->>DB: 验证分镜存在性和用户权限
        
        alt 分镜不存在或无权限
            A->>F: 返回权限错误<br/>{<br/>  code: 403,<br/>  message: "无权限修改该分镜",<br/>  data: null,<br/>  timestamp, request_id<br/>}
            F->>F: 显示权限错误提示
        else 权限验证通过
            DB->>A: 返回分镜详情和权限信息
            A->>F: 返回分镜编辑配置数据
            
            Note over F: 🎭 加载动作库（智能推荐）
            F->>A: GET /py-api/storyboard-actions?storyboard_id={id}
            A->>DB: 查询分镜的ai_prompt字段
            A->>DB: 基于TAG匹配查询推荐动作
            A->>DB: 查询用户上传的分镜动作
            A->>R: 获取缓存的热门动作

            Note over A: 🤖 智能推荐算法
            A->>A: 分析分镜ai_prompt中的关键词
            A->>A: 匹配动作库tags字段
            A->>A: 推荐动作排在前面并标记为推荐

            DB->>A: 返回智能排序的动作库数据
            A->>F: 返回分镜动作列表（含推荐标记）
            F->>F: 渲染分镜编辑界面（推荐动作优先显示）

            Note over F: 🎬 分镜编辑模式选择
            F->>F: 根据参数显示对应的编辑选项

            alt 快速编辑模式
                F->>F: 显示预设的常用动作选择
                Note over F: 用户从推荐动作中快速选择
            else 高级编辑模式
                F->>F: 显示完整的动作库界面
                Note over F: 用户可浏览所有动作分类，支持搜索筛选
            end

            F->>F: 用户浏览动作库(推荐分镜 + 已上传分镜)
            F->>F: 用户选择合适的动作图
            F->>F: 实时预览选择效果

            Note over F: ✅ 确认修改操作
            F->>F: 用户点击"确定"按钮
            F->>A: PUT /py-api/storyboards/{id}/action<br/>body: {action_id, position_data}
            
            A->>DB: 开始事务
            A->>DB: 验证动作存在性和可用性
            A->>DB: 更新分镜动作数据
            A->>R: 更新分镜缓存
            A->>DB: 记录修改日志
            A->>DB: 提交事务

            alt 修改失败
                A->>F: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "分镜修改失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
                F->>F: 显示修改失败提示
            else 修改成功
                A->>F: 返回成功结果<br/>{<br/>  code: 200,<br/>  message: "分镜修改成功",<br/>  data: {<br/>    storyboard_id, action_id,<br/>    updated_data, preview_url<br/>  },<br/>  timestamp, request_id<br/>}
                F->>F: 渲染新的动作图到编辑区域
                F->>F: 显示修改成功提示
                F->>F: 关闭分镜编辑罩层
            end
        end
    end

    Note over F: 🎯 分镜修改完成，继续后续业务流程
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li><strong>🔄 标准化调用接口：</strong>统一的参数化调用方式，支持不同业务场景的需求</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>🎨 UI统一处理：</strong>所有分镜编辑的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性</li>
                <li><strong>📋 多模式支持：</strong>支持快速编辑、高级编辑、预览模式等多种调用模式</li>
                <li><strong>🎬 权限控制：</strong>完整的分镜权限验证机制，确保用户只能修改有权限的分镜</li>
                <li><strong>🎭 智能动作推荐：</strong>基于分镜ai_prompt与动作库tags的智能匹配推荐系统</li>
                <li><strong>🏷️ 推荐标记系统：</strong>自动标记推荐动作，优先显示匹配度高的动作</li>
                <li><strong>⚡ 快速响应：</strong>纯数据库操作，无AI生成，响应速度快</li>
                <li><strong>💰 无积分消费：</strong>不涉及AI生成，无积分扣费，用户可自由编辑</li>
                <li><strong>🔄 实时预览：</strong>提供实时的编辑预览效果</li>
                <li><strong>⚠️ 错误处理机制：</strong>完善的错误处理和用户提示机制</li>
                <li><strong>🔄 状态管理：</strong>完整的业务状态跟踪和数据同步</li>
                <li><strong>📊 操作日志：</strong>记录所有修改操作，支持审计和回滚</li>
                <li><strong>🎯 结果回调：</strong>标准化的成功/失败结果返回机制</li>
                <li><strong>🔧 可扩展配置：</strong>支持灵活的参数配置和功能扩展</li>
            </ul>

            <h3>📋 调用参数规范</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 调用接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 调用分镜修改流程
StoryboardEdit.start({
    storyboard_id: 123,                    // 必需：分镜ID
    mode: 'quick|advanced|preview',        // 编辑模式
    permissions: {
        edit: true,                        // 编辑权限
        delete: false,                     // 删除权限
        publish: true                      // 发布权限
    },
    config: {
        showActionLibrary: true,           // 显示动作库
        showPreview: true,                 // 显示预览
        showRecommendations: true,         // 显示推荐动作
        maxSelections: 1,                  // 最大选择数量
        allowCustomActions: true           // 允许自定义动作
    },
    filters: {
        camera_shot: ['close_up', 'medium', 'wide'], // 镜头类型筛选
        difficulty_level: ['easy', 'medium'],         // 难度等级筛选
        tags: ['action', 'emotion', 'pose']           // 标签筛选
    },
    callbacks: {
        onSuccess: (result) => { /* 成功回调 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onPreview: (preview) => { /* 预览回调 */ }
    }
});

// API接口调用示例
// 1. 获取分镜编辑权限
GET /py-api/storyboards/{id}/edit-permission
Headers: {
    "Authorization": "Bearer {token}"
}

// 2. 获取动作库列表（智能推荐）
GET /py-api/storyboard-actions?storyboard_id=123&camera_shot=close_up&difficulty_level=easy
Headers: {
    "Authorization": "Bearer {token}"
}
// 注：storyboard_id参数用于智能推荐，系统会分析分镜的ai_prompt字段
// 与动作库的tags字段进行匹配，推荐动作排在前面并标记为推荐

// 3. 修改分镜动作
PUT /py-api/storyboards/{id}/action
Headers: {
    "Authorization": "Bearer {token}",
    "Content-Type": "application/json"
}
Body: {
    "action_id": 456,                      // 选择的动作ID
    "position_data": {                     // 位置数据
        "x": 100,
        "y": 200,
        "scale": 1.0,
        "rotation": 0
    },
    "custom_settings": {                   // 自定义设置
        "mirror": false,
        "opacity": 1.0
    }
}
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果（动作库列表）
{
    success: true,
    data: {
        data: [
            {
                id: 456,
                title: '站立姿势',
                action_type: '站立',
                camera_shot: 'medium',
                difficulty_level: 'easy',
                tags: ['站立', '正面', '自然'],
                is_recommended: true,  // 智能推荐标记
                usage_count: 150,
                sketch_image_url: 'https://...'
            },
            // ... 更多动作
        ],
        pagination: { ... }
    }
}

// 成功结果（分镜修改）
{
    success: true,
    storyboard: {
        id: 123,
        action_id: 456,
        action_name: '站立姿势',
        camera_shot: 'medium',
        updated_data: {
            position: { x: 100, y: 200 },
            settings: { mirror: false, opacity: 1.0 }
        },
        preview_url: 'https://...',
        last_modified: '2025-08-08T10:30:00Z'
    }
}

// 失败结果（遵循API规范格式）
{
    code: 403,                             // 业务错误码：401=认证失败, 403=权限不足, 404=分镜不存在, 422=参数验证失败, 5002=控制器异常, 5003=服务层异常
    message: "无权限修改该分镜",           // 业务错误码描述
    data: {                               // 错误详细数据（可选）
        storyboard_id: 123,
        required_permission: 'edit',
        user_permissions: ['view'],
        error_step: 'permission_check'
    },
    timestamp: 1640995200,                // 时间戳
    request_id: "req_abc123_def456"       // 请求ID
}
                </pre>
            </div>

            <h3>🔗 引用示例</h3>
            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔗 在项目编辑流程中引用</h4>
                <p>当项目编辑需要修改分镜时，调用此流程：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 在项目编辑流程中
Note over F: 用户点击"编辑分镜"
F->>StoryboardEdit: 调用分镜修改流程(advanced模式)
Note over StoryboardEdit: 引用 diagram-storyboard-edit-shared.html
StoryboardEdit->>F: 返回修改的分镜数据
F->>ProjectFlow: 更新项目中的分镜信息
                </pre>


            </div>

            <h3>📚 技术规范说明</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <p><strong>本流程遵循以下技术规范：</strong></p>
                <ul>
                    <li><strong>T-1：</strong>Token认证 - 标准化Token验证机制</li>
                    <li><strong>T-2：</strong>权限控制 - 分镜级别的权限验证</li>
                    <li><strong>T-3：</strong>数据一致性 - 事务处理确保数据完整性</li>
                    <li><strong>T-4：</strong>缓存策略 - Redis缓存提升响应速度</li>
                    <li><strong>T-5：</strong>操作日志 - 完整的操作审计记录</li>
                    <li><strong>T-6：</strong>错误处理 - 统一的错误码和消息格式</li>
                    <li><strong>T-7：</strong>UI一致性 - 统一的前端交互体验</li>
                    <li><strong>T-8：</strong>参数化配置 - 灵活的调用参数支持</li>
                    <li><strong>T-9：</strong>智能推荐算法 - 基于TAG匹配的动作推荐机制</li>
                    <li><strong>T-10：</strong>推荐标记 - 自动标记和排序推荐动作</li>
                </ul>
                <p><em>这些规范确保了与系统其他流程的一致性和兼容性。</em></p>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
