<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程A-2: 处理成功的业务流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🔄 Py视频创作工具C-2: AI生成成功</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over P: 🔗 建立WebSocket连接
    P->>W: 建立WebSocket连接
    W->>P: 连接确认，准备接收进度推送

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥

    Note over A: 📊 标准化积分处理流程
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>DB: 写入业务日志(状态:冻结)
    A->>R: 缓存业务日志

    Note over A: 📈 实时进度推送：开始处理
    A->>W: 推送进度更新(10%, "开始AI处理")
    W->>P: 实时推送进度到前端

    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE自动路由到mock/real服务

    A->>W: 推送进度更新(30%, "连接AI平台")
    W->>P: 实时推送进度到前端

    SC->>AI: 调用AI服务(自动环境切换)

    A->>W: 推送进度更新(60%, "AI处理中")
    W->>P: 实时推送进度到前端

    AI->>SC: 返回结果(包含mode标识)
    SC->>A: 返回结果+环境模式信息

    A->>W: 推送进度更新(80%, "保存结果")
    W->>P: 实时推送进度到前端

    Note over A: ✅ 标准化成功处理流程
    A->>DB: 更新业务日志(状态:成功)
    A->>DB: 确认积分扣取(解冻→已扣)
    A->>R: 同步最终状态

    A->>W: 推送进度更新(100%, "处理完成")
    W->>P: 实时推送最终完成状态

    A->>W: 返回成功结果
    W->>P: 推送成功结果(包含环境模式信息)

    Note over P: 🔚 关闭WebSocket连接
    P->>W: 关闭WebSocket连接

    A->>E: 发布成功事件(异步处理)
    Note over P: 业务完成，积分已扣取，用户体验完整
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
