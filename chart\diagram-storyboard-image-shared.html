<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 生成分镜图片（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>🎬 共享业务流程: 生成分镜图片（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>这是一个完整的分镜图片生成业务流程，由"Py视频创作工具前端"直接发起和处理。该流程涵盖了用户从发起分镜图片生成请求到获得最终结果的完整过程。支持多种生成模式：单个生成、批量生成、项目生成、选择性生成等，通过不同参数实现不同的业务场景。所有UI交互和业务逻辑都由前端统一处理，确保用户体验的一致性和流程的完整性。
        </div>

        <div class="reference-box">
            <h4>📋 应用场景</h4>
            <p><strong>本流程适用于以下业务场景：</strong></p>
            <ul>
                <li>• 项目创建时自动生成项目分镜图片</li>
                <li>• 分镜编辑后重新生成单个分镜图片</li>
                <li>• 批量处理多个分镜图片生成</li>
                <li>• 用户主动发起的分镜图片生成需求</li>
            </ul>
            <p><strong>使用方式：</strong>用户通过前端界面操作，系统根据不同场景自动选择相应的生成模式和参数配置。</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over F: 🎬 用户发起分镜图片生成功能

    F->>F: 启动分镜图片生成流程<br/>参数: {mode, storyboard_ids, generation_params}

    Note over F: 📋 参数化配置处理
    alt mode=single（单个生成）
        Note over F: 生成指定的单个分镜图片
    else mode=batch（批量生成）
        Note over F: 根据storyboard_ids批量生成分镜图片
    else mode=project（项目生成）
        Note over F: 生成整个项目的所有分镜图片
    else mode=selective（选择性生成）
        Note over F: 根据筛选条件生成符合要求的分镜图片
    end

    Note over F: 🎨 统一UI处理
    F->>F: 显示分镜图片生成进度界面
    F->>A: 获取分镜生成初始数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
    else Token验证通过
        A->>DB: 查询用户状态和权限
        A->>DB: 查询分镜数据和项目信息
        DB->>A: 返回分镜和项目数据
        A->>F: 返回分镜生成配置数据
        F->>F: 渲染分镜生成界面

        Note over F: 🤖 智能平台选择流程
        F->>A: POST /py-api/ai-models/select-platform<br/>business_type=image, auto_recommend=true
        A->>A: 调用AiPlatformSelectionService
        A->>DB: 查询AI模型配置和可用平台
        A->>DB: 查询用户历史偏好和使用记录
        A->>A: 分析分镜内容特征+用户偏好+平台状态
        A->>A: 返回最佳推荐+备选方案
        A->>F: 返回平台推荐结果
        F->>F: 显示平台选择界面（可选）

        F->>F: 用户确认生成参数，点击生成按钮

        Note over F: 🔗 建立WebSocket连接
        F->>W: POST /py-api/websocket/auth<br/>认证WebSocket连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "image"<br/>}
        W->>W: 验证用户Token和客户端类型
        W->>DB: 创建WebSocket会话记录
        W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2025-01-09T10:30:00Z",<br/>    supported_events: [<br/>      "ai_generation_progress",<br/>      "ai_generation_completed",<br/>      "ai_generation_failed",<br/>      "storyboard_batch_progress"<br/>    ],<br/>    heartbeat_interval: 30<br/>  }<br/>}
        F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
        W->>F: WebSocket连接确认，准备接收进度推送

        Note over A: 📊 标准化积分处理流程
        F->>A: POST /py-api/projects/{project_id}/storyboards/batch-generate-with-websocket<br/>分镜图片生成请求<br/>{<br/>  "mode": "batch",<br/>  "storyboard_ids": [1,2,3],<br/>  "generation_params": {...},<br/>  "websocket_session_id": "ws_session_xxx"<br/>}
        A->>A: 创建异步任务ProcessStoryboardImageGeneration
        A->>F: 立即返回任务信息<br/>{<br/>  "task_id": "storyboard_123456",<br/>  "status": "processing",<br/>  "total_storyboards": 3<br/>}

        Note over A: 🔄 异步任务开始处理
        A->>DB: 检查用户积分(事务锁定)

        alt 积分不足
            Note over A: 积分 < 所需积分
            A->>W: 返回积分不足详细信息<br/>{<br/>  code: 1006,<br/>  message: "积分不足",<br/>  data: null,<br/>  timestamp, request_id<br/>}
            W->>F: 推送积分不足消息
            F->>F: 显示积分不足提示
            Note over A: 无扣费操作，保护用户资金
        else 积分充足
            A->>DB: 扣取积分(冻结状态)
            A->>R: 同步积分状态(缓存更新)
            A->>DB: 写入业务日志(状态:冻结)
            A->>R: 缓存业务日志
            A->>RM: 创建分镜图片元数据记录（仅URL/状态/元信息）

            Note over A: 📈 实时进度推送
            A->>W: 推送进度更新(10%, "开始分镜图片生成")
            W->>F: 实时推送进度到前端
            F->>F: 更新进度条显示

            Note over A: 🎯 执行分镜图片生成处理
            loop 处理每个分镜
                A->>W: 推送进度更新(30%, "分析分镜内容")
                W->>F: 实时推送进度
                A->>A: 解析分镜描述和AI提示词
                A->>W: 推送进度更新(50%, "连接AI平台")
                W->>F: 实时推送进度
                A->>SC: 调用AI图片生成服务
                SC->>AI: 生成分镜图片
                AI->>SC: 返回生成结果
                SC->>A: 返回生成URL与元数据
                A->>W: 推送进度更新(80%, "保存分镜图片数据")
                W->>F: 实时推送进度
                A->>RM: 更新分镜图片元数据
            end

            alt 分镜图片生成失败
                A->>W: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "分镜图片生成失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
                W->>F: 推送失败结果
                A->>DB: 更新业务日志(状态:失败)
                A->>DB: 退还积分(解冻→退还)
                A->>R: 同步退还状态
                A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "storyboard_generation_failed",<br/>  "business_id": task_id,<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "platform": "deepseek",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "AI服务调用失败"<br/>  },<br/>  "metadata": {<br/>    "project_id": 123,<br/>    "storyboard_ids": [1,2,3],<br/>    "generation_params": {...}<br/>  }<br/>}
                F->>F: 显示生成失败提示
                Note over A: 仅更新元数据状态与资金记账，遵循资源下载架构边界
            else 分镜图片生成成功
                A->>DB: 更新业务日志(状态:成功)
                A->>DB: 确认积分扣取(解冻→已扣)
                A->>R: 同步最终状态
                A->>RM: 更新元数据（status=ready, url，仅URL/状态/元信息）

                A->>W: 推送进度更新(100%, "分镜图片生成完成")
                W->>F: 实时推送最终完成状态
                F->>F: 显示生成成功界面

                A->>W: 返回分镜图片数据
                W->>F: 推送分镜图片生成成功结果
                F->>F: 更新分镜图片显示

                Note over A: 📊 用户偏好学习与优化
                A->>DB: 记录用户平台选择行为和偏好权重
                A->>R: 更新用户常用平台缓存
                A->>R: 刷新推荐算法缓存
            end
        end

        Note over F: 🔚 清理资源
        F->>W: 关闭WebSocket连接
    end

    Note over F: 🎯 分镜图片生成完成，继续后续业务流程
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li><strong>🔄 统一业务处理：</strong>前端统一处理所有分镜图片生成需求，支持不同业务场景的参数化配置</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>🎨 UI统一处理：</strong>所有分镜图片生成的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性</li>
                <li><strong>📋 多模式支持：</strong>支持单个生成、批量生成、项目生成、选择性生成等多种调用模式</li>
                <li><strong>🎬 智能内容分析：</strong>基于分镜描述和AI提示词进行智能内容分析和优化</li>
                <li><strong>🤖 智能平台选择：</strong>集成标准化的AI平台选择机制，基于分镜内容特征提供最佳推荐</li>
                <li><strong>🔒 完整积分处理：</strong>包含积分验证、冻结、扣除、返还的完整流程</li>
                <li><strong>📡 实时进度推送：</strong>通过WebSocket提供实时的生成进度反馈</li>
                <li><strong>⚠️ 错误处理机制：</strong>完善的错误处理和用户提示机制</li>
                <li><strong>🔄 状态管理：</strong>完整的业务状态跟踪和数据同步</li>
                <li><strong>📊 用户偏好学习：</strong>记录用户行为，优化后续推荐</li>
                <li><strong>🎯 结果处理：</strong>前端统一处理成功/失败结果，提供一致的用户反馈</li>
                <li><strong>🗂️ 资源管理边界（RM）：</strong>仅管理元数据（URL/状态/元信息），不保存实际资源文件</li>
                <li><strong>🔧 可扩展配置：</strong>支持灵活的参数配置和功能扩展</li>
            </ul>

            <h3>📋 业务参数配置</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 前端配置接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 前端分镜图片生成配置
const storyboardConfig = {
    mode: 'single|batch|project|selective',
    storyboard_ids: [123, 456, 789], // 指定分镜ID列表
    project_id: 123,                 // 项目ID（project模式必填）
    generation_params: {
        platform: 'deepseek|minimax|liblib', // 可选，不指定时使用智能推荐
        quality: 'standard|hd|ultra',
        aspect_ratio: '16:9|9:16|1:1|4:3|3:4',
        style: '写实风3.0|动漫可爱3.0|武侠古风3.0',
        batch_size: 5,               // 批量处理时的批次大小
        priority: 'normal|high|urgent' // 生成优先级
    },
    filters: {                       // selective模式的筛选条件
        status: ['draft', 'approved'],
        has_ai_prompt: true,
        scene_types: ['action', 'dialogue', 'transition']
    },
    ui_options: {
        show_progress: true,         // 显示进度条
        auto_refresh: true,          // 自动刷新结果
        confirm_before_start: true   // 开始前确认
    }
};

// API接口调用示例
// 1. WebSocket认证连接
POST /py-api/websocket/auth
{
    "client_type": "python_tool",
    "user_token": "Bearer xxx"
}

// 2. 批量分镜生成（WebSocket版本）
POST /py-api/projects/{project_id}/storyboards/batch-generate-with-websocket
{
    "mode": "batch",
    "storyboard_ids": [1, 2, 3],
    "generation_params": {
        "platform": "deepseek",
        "quality": "hd",
        "aspect_ratio": "16:9",
        "style": "写实风3.0"
    },
    "websocket_session_id": "session_xxx"
}

// 3. 查询任务状态
GET /py-api/storyboards/tasks/{task_id}/status

// 4. AI平台智能推荐
POST /py-api/ai-models/select-platform
{
    "business_type": "image",
    "auto_recommend": true
}

// 5. 事件发布接口（业务失败时调用）
POST /py-api/events/publish
{
    "event_type": "storyboard_generation_failed",
    "business_id": "task_123456",
    "user_id": 123,
    "error_details": {
        "platform": "deepseek",
        "error_code": "AI_SERVICE_ERROR",
        "error_message": "AI服务调用失败"
    },
    "metadata": {
        "project_id": 123,
        "storyboard_ids": [1, 2, 3],
        "generation_params": {
            "platform": "deepseek",
            "quality": "hd"
        }
    }
}

// 6. WebSocket断开连接
POST /py-api/websocket/disconnect
{
    "session_id": "ws_session_xxx",
    "reason": "task_completed"
}
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果
{
    success: true,
    data: {
        task_id: 'task_123456',
        generated_images: [
            {
                storyboard_id: 123,
                image_url: 'https://...',
                status: 'completed',
                generation_time: 45.2,
                platform_used: 'deepseek',
                metadata: { ... }
            }
        ],
        summary: {
            total_requested: 5,
            successfully_generated: 4,
            failed: 1,
            total_cost: 12.5
        }
    }
}

// 失败结果（遵循API规范格式）
{
    code: 1006,                    // 业务错误码
    message: "积分不足",           // 业务错误码描述
    data: {                       // 错误详细数据
        error_details: 'specific_error_info',
        failed_storyboards: [123, 456],
        generation_step: 'platform_selection|image_generation|saving'
    },
    timestamp: 1640995200,        // 时间戳
    request_id: "req_abc123_def456" // 请求ID
}
                </pre>
            </div>

            <h3>🔗 业务场景示例</h3>
            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔗 项目创建场景</h4>
                <p>当项目创建完成后需要生成分镜图片时：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 项目创建完成后的分镜图片生成
Note over F: 项目创建完成，用户选择生成分镜图片
F->>F: 启动分镜图片生成流程(project模式)
Note over F: 执行完整的分镜图片生成流程
F->>F: 显示生成结果，完成项目初始化
                </pre>

                <h4>🔗 分镜编辑场景</h4>
                <p>当用户编辑分镜后需要重新生成图片时：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 分镜编辑后的图片重新生成
Note over F: 用户修改分镜内容，点击"重新生成图片"
F->>F: 启动分镜图片生成流程(single模式)
Note over F: 执行完整的分镜图片生成流程
F->>F: 更新分镜图片显示
                </pre>
            </div>

            <h3>📚 技术规范说明</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <p><strong>本流程遵循以下技术规范：</strong></p>
                <ul>
                    <li><strong>C-1：</strong>AI任务调度 - 智能平台选择机制</li>
                    <li><strong>C-2：</strong>AI生成成功 - 积分确认扣取流程</li>
                    <li><strong>C-3：</strong>积分不足 - 快速验证机制</li>
                    <li><strong>C-4：</strong>AI生成失败 - 事件总线异步处理</li>
                    <li><strong>C-6：</strong>资源管理 - AI资源管理服务</li>
                    <li><strong>C-7：</strong>资源下载 - 直接下载机制</li>
                    <li><strong>C-8：</strong>作品发布 - 可选发布流程</li>
                    <li><strong>C-9：</strong>环境切换 - AiServiceClient统一调用</li>
                    <li><strong>WS-1：</strong>WebSocket认证 - POST /py-api/websocket/auth</li>
                    <li><strong>WS-2：</strong>WebSocket连接 - wss://api.tiptop.cn:8080</li>
                    <li><strong>WS-3：</strong>实时进度推送 - WebSocketEventService</li>
                    <li><strong>WS-4：</strong>会话管理 - WebSocketSession模型</li>
                    <li><strong>E-1：</strong>事件发布 - POST /py-api/events/publish</li>
                    <li><strong>E-2：</strong>失败事件处理 - storyboard_generation_failed</li>
                    <li><strong>E-3：</strong>异步事件总线 - 解耦业务处理</li>
                </ul>
                <p><em>这些规范确保了与系统其他流程的一致性和兼容性，特别是WebSocket实时通信和事件驱动架构。</em></p>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
