---
description: Lumen 10.x 微服务框架开发规范指南
globs: ["php/api/**/*.php", "**/composer.json", "**/composer.lock", "**/.env*", "**/routes/**", "**/app/**", "**/config/**", "**/database/**", "**/tests/**"]
alwaysApply: true
---

## Lumen 10.x 微服务框架开发规范指南

### 框架信息

- **框架版本**: Lumen 10.x
- **PHP要求**: >= 8.1
- **官方文档**: https://lumen.laravel.com/docs/10.x
- **适用场景**: API服务、微服务架构
- **特点**: 轻量级、高性能、专注API开发

### 系统要求

#### 服务器要求
```bash
# 必需扩展
- PHP >= 8.1
- OpenSSL PHP Extension
- PDO PHP Extension
- Mbstring PHP Extension
```

#### 推荐开发环境
```bash
# 使用Composer安装
composer create-project --prefer-dist laravel/lumen blog

# 本地开发服务器
php -S localhost:8000 -t public
```

### 项目结构

#### 标准目录结构
```
app/
├── Console/
│   └── Commands/
├── Events/
├── Exceptions/
├── Http/
│   ├── Controllers/
│   └── Middleware/
├── Jobs/
├── Listeners/
├── Models/
├── Providers/
└── Services/

bootstrap/
├── app.php
└── cache/

config/
database/
├── factories/
├── migrations/
└── seeders/

public/
├── index.php
└── .htaccess

resources/
├── lang/
└── views/

routes/
├── web.php
└── api.php

storage/
├── app/
├── framework/
└── logs/

tests/
├── Feature/
└── Unit/

vendor/
.env
.env.example
artisan
composer.json
composer.lock
```

### 配置管理

#### 环境配置 (.env)
```ini
# 应用配置
APP_NAME=Lumen
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
APP_TIMEZONE=Asia/Shanghai

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=homestead
DB_USERNAME=homestead
DB_PASSWORD=secret

# 缓存配置
CACHE_DRIVER=file
QUEUE_CONNECTION=sync

# 日志配置
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
```

#### bootstrap/app.php 配置
```php
<?php

require_once __DIR__.'/../vendor/autoload.php';

(new Laravel\Lumen\Bootstrap\LoadEnvironmentVariables(
    dirname(__DIR__)
))->bootstrap();

date_default_timezone_set(env('APP_TIMEZONE', 'UTC'));

/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
*/

$app = new Laravel\Lumen\Application(
    dirname(__DIR__)
);

// 启用Facades
$app->withFacades();

// 启用Eloquent
$app->withEloquent();

/*
|--------------------------------------------------------------------------
| Register Container Bindings
|--------------------------------------------------------------------------
*/

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

/*
|--------------------------------------------------------------------------
| Register Config Files
|--------------------------------------------------------------------------
*/

$app->configure('app');
$app->configure('database');
$app->configure('cache');
$app->configure('queue');

/*
|--------------------------------------------------------------------------
| Register Middleware
|--------------------------------------------------------------------------
*/

$app->middleware([
    App\Http\Middleware\CorsMiddleware::class
]);

$app->routeMiddleware([
    'auth' => App\Http\Middleware\Authenticate::class,
    'throttle' => Laravel\Lumen\Http\Middleware\ThrottleRequests::class,
]);

/*
|--------------------------------------------------------------------------
| Register Service Providers
|--------------------------------------------------------------------------
*/

$app->register(App\Providers\AppServiceProvider::class);
$app->register(App\Providers\AuthServiceProvider::class);
// $app->register(App\Providers\EventServiceProvider::class);

/*
|--------------------------------------------------------------------------
| Load The Application Routes
|--------------------------------------------------------------------------
*/

$app->router->group([
    'namespace' => 'App\Http\Controllers',
], function ($router) {
    require __DIR__.'/../routes/web.php';
});

return $app;
```

### 路由定义

#### API路由 (routes/web.php)
```php
<?php

/** @var \Laravel\Lumen\Routing\Router $router */

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
*/

$router->get('/', function () use ($router) {
    return $router->app->version();
});

// API版本分组
$router->group(['prefix' => 'api/v1'], function () use ($router) {
    
    // 用户认证路由
    $router->group(['prefix' => 'auth'], function () use ($router) {
        $router->post('login', 'AuthController@login');
        $router->post('register', 'AuthController@register');
        $router->post('refresh', 'AuthController@refresh');
        $router->post('logout', 'AuthController@logout');
    });
    
    // 需要认证的路由
    $router->group(['middleware' => 'auth'], function () use ($router) {
        $router->get('profile', 'UserController@profile');
        $router->put('profile', 'UserController@updateProfile');
        
        // 用户管理
        $router->group(['prefix' => 'users'], function () use ($router) {
            $router->get('/', 'UserController@index');
            $router->get('{id}', 'UserController@show');
            $router->post('/', 'UserController@store');
            $router->put('{id}', 'UserController@update');
            $router->delete('{id}', 'UserController@destroy');
        });
    });
    
    // 公开API路由
    $router->group(['prefix' => 'public'], function () use ($router) {
        $router->get('status', 'StatusController@index');
        $router->get('version', 'StatusController@version');
    });
});
```

#### 路由参数验证
```php
// 路由参数约束
$router->get('users/{id:[0-9]+}', 'UserController@show');
$router->get('posts/{slug:[a-z-]+}', 'PostController@show');

// 可选参数
$router->get('users/{id?}', 'UserController@index');

// 路由命名
$router->get('users', ['as' => 'users.index', 'uses' => 'UserController@index']);
```

### 控制器开发

#### 基础控制器
```php
<?php

namespace App\Http\Controllers;

use Laravel\Lumen\Routing\Controller as BaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class Controller extends BaseController
{
    /**
     * 成功响应
     */
    protected function success($data = null, string $message = 'Success', int $code = 200): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ], $code);
    }
    
    /**
     * 错误响应
     */
    protected function error(string $message = 'Error', int $code = 400, $data = null): JsonResponse
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ], $code);
    }
    
    /**
     * 分页响应
     */
    protected function paginated($paginator, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'message' => $message,
            'data' => $paginator->items(),
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
            'timestamp' => time()
        ]);
    }
}
```

#### API控制器示例
```php
<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class UserController extends Controller
{
    /**
     * 获取用户列表
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $users = User::paginate($perPage);
        
        return $this->paginated($users, '用户列表获取成功');
    }
    
    /**
     * 获取单个用户
     */
    public function show(int $id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        return $this->success($user, '用户信息获取成功');
    }
    
    /**
     * 创建用户
     */
    public function store(Request $request): JsonResponse
    {
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:6',
        ]);
        
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);
        
        return $this->success($user, '用户创建成功', 201);
    }
    
    /**
     * 更新用户
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        $this->validate($request, [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $id,
        ]);
        
        $user->update($request->only(['name', 'email']));
        
        return $this->success($user, '用户更新成功');
    }
    
    /**
     * 删除用户
     */
    public function destroy(int $id): JsonResponse
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        $user->delete();
        
        return $this->success(null, '用户删除成功');
    }
}
```

### 中间件开发

#### CORS中间件
```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CorsMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $headers = [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'POST, GET, OPTIONS, PUT, DELETE',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With'
        ];

        if ($request->isMethod('OPTIONS')) {
            return response()->json('{"method":"OPTIONS"}', 200, $headers);
        }

        $response = $next($request);
        foreach ($headers as $key => $value) {
            $response->header($key, $value);
        }

        return $response;
    }
}
```

#### 认证中间件
```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Http\Request;

class Authenticate
{
    /**
     * The authentication guard factory instance.
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     */
    public function __construct(Auth $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, $guard = null)
    {
        if ($this->auth->guard($guard)->guest()) {
            return response()->json([
                'code' => 401,
                'message' => 'Unauthorized',
                'data' => null,
                'timestamp' => time()
            ], 401);
        }

        return $next($request);
    }
}
```

### 模型开发

#### 用户模型示例
```php
<?php

namespace App\Models;

use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Lumen\Auth\Authorizable;

class User extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable, HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name', 'email', 'password',
    ];

    /**
     * The attributes excluded from the model's JSON form.
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
```

### 数据验证

#### 请求验证
```php
// 在控制器中使用验证
public function store(Request $request)
{
    $this->validate($request, [
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users',
        'password' => 'required|string|min:6|confirmed',
        'age' => 'nullable|integer|min:18|max:120',
        'phone' => 'nullable|regex:/^1[3-9]\d{9}$/',
    ], [
        'name.required' => '姓名不能为空',
        'email.required' => '邮箱不能为空',
        'email.email' => '邮箱格式不正确',
        'email.unique' => '邮箱已被注册',
        'password.required' => '密码不能为空',
        'password.min' => '密码至少6位',
        'password.confirmed' => '两次密码不一致',
    ]);
}
```

#### 自定义验证规则
```php
// 在AppServiceProvider中注册自定义验证规则
use Illuminate\Support\Facades\Validator;

public function boot()
{
    Validator::extend('phone', function ($attribute, $value, $parameters, $validator) {
        return preg_match('/^1[3-9]\d{9}$/', $value);
    });
    
    Validator::replacer('phone', function ($message, $attribute, $rule, $parameters) {
        return str_replace(':attribute', $attribute, '手机号格式不正确');
    });
}
```

### 异常处理

#### 异常处理器
```php
<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Laravel\Lumen\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     */
    protected $dontReport = [
        AuthorizationException::class,
        HttpException::class,
        ModelNotFoundException::class,
        ValidationException::class,
    ];

    /**
     * Report or log an exception.
     */
    public function report(Throwable $exception): void
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception)
    {
        // API请求返回JSON格式错误
        if ($request->expectsJson()) {
            return $this->handleApiException($request, $exception);
        }

        return parent::render($request, $exception);
    }

    /**
     * 处理API异常
     */
    protected function handleApiException(Request $request, Throwable $exception): JsonResponse
    {
        if ($exception instanceof ValidationException) {
            return response()->json([
                'code' => 422,
                'message' => '参数验证失败',
                'data' => $exception->errors(),
                'timestamp' => time()
            ], 422);
        }

        if ($exception instanceof ModelNotFoundException) {
            return response()->json([
                'code' => 404,
                'message' => '资源不存在',
                'data' => null,
                'timestamp' => time()
            ], 404);
        }

        if ($exception instanceof AuthorizationException) {
            return response()->json([
                'code' => 403,
                'message' => '权限不足',
                'data' => null,
                'timestamp' => time()
            ], 403);
        }

        if ($exception instanceof NotFoundHttpException) {
            return response()->json([
                'code' => 404,
                'message' => '接口不存在',
                'data' => null,
                'timestamp' => time()
            ], 404);
        }

        // 生产环境隐藏详细错误信息
        $message = env('APP_DEBUG') ? $exception->getMessage() : '服务器内部错误';
        
        return response()->json([
            'code' => 500,
            'message' => $message,
            'data' => null,
            'timestamp' => time()
        ], 500);
    }
}
```

### 数据库操作

#### 数据库迁移
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('phone')->nullable();
            $table->tinyInteger('status')->default(1)->comment('状态：1-正常，0-禁用');
            $table->rememberToken();
            $table->timestamps();
            
            $table->index(['email', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
```

#### 模型工厂
```php
<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone' => $this->faker->phoneNumber(),
            'status' => 1,
            'remember_token' => \Str::random(10),
        ];
    }
}
```

### 测试开发

#### 功能测试
```php
<?php

namespace Tests\Feature;

use App\Models\User;
use Laravel\Lumen\Testing\DatabaseMigrations;
use Laravel\Lumen\Testing\TestCase as BaseTestCase;

class UserTest extends BaseTestCase
{
    use DatabaseMigrations;

    /**
     * 测试用户列表获取
     */
    public function test_can_get_users_list(): void
    {
        User::factory()->count(5)->create();

        $response = $this->get('/api/v1/users');

        $response->assertResponseStatus(200);
        $response->seeJsonStructure([
            'code',
            'message',
            'data' => [
                '*' => ['id', 'name', 'email', 'created_at', 'updated_at']
            ],
            'pagination',
            'timestamp'
        ]);
    }

    /**
     * 测试用户创建
     */
    public function test_can_create_user(): void
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->post('/api/v1/users', $userData);

        $response->assertResponseStatus(201);
        $response->seeJsonStructure([
            'code',
            'message',
            'data' => ['id', 'name', 'email', 'created_at', 'updated_at'],
            'timestamp'
        ]);

        $this->seeInDatabase('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * 测试用户创建验证失败
     */
    public function test_user_creation_validation_fails(): void
    {
        $userData = [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
        ];

        $response = $this->post('/api/v1/users', $userData);

        $response->assertResponseStatus(422);
        $response->seeJsonStructure([
            'code',
            'message',
            'data',
            'timestamp'
        ]);
    }
}
```

### 性能优化

#### 查询优化
```php
// 使用预加载避免N+1查询
$users = User::with(['posts', 'comments'])->get();

// 使用分块处理大量数据
User::chunk(100, function ($users) {
    foreach ($users as $user) {
        // 处理用户数据
    }
});

// 使用索引优化查询
User::where('email', $email)->where('status', 1)->first();
```

#### 缓存策略
```php
use Illuminate\Support\Facades\Cache;

// 缓存用户数据
$user = Cache::remember('user:' . $id, 3600, function () use ($id) {
    return User::find($id);
});

// 清除缓存
Cache::forget('user:' . $id);

// 缓存标签
Cache::tags(['users', 'posts'])->put('user:' . $id, $user, 3600);
Cache::tags(['users'])->flush();
```

### 部署配置

#### 生产环境配置
```ini
# .env.production
APP_ENV=production
APP_DEBUG=false
APP_KEY=your-32-character-secret-key

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=your-database
DB_USERNAME=your-username
DB_PASSWORD=your-password

# 缓存配置
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# 日志配置
LOG_CHANNEL=daily
LOG_LEVEL=error
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name api.example.com;
    root /var/www/lumen/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 最佳实践

#### 1. API设计原则
- 使用RESTful API设计风格
- 统一的响应格式
- 合理的HTTP状态码
- API版本控制
- 完善的错误处理

#### 2. 安全考虑
- 输入验证和过滤
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 速率限制

#### 3. 性能优化
- 数据库查询优化
- 缓存策略
- 异步队列处理
- 代码优化

#### 4. 代码质量
- 遵循PSR标准
- 单元测试覆盖
- 代码注释
- 错误日志记录

Lumen 10.x 专为API开发而设计，提供了轻量级、高性能的微服务解决方案。遵循这些规范将确保项目的可维护性、可扩展性和安全性。
