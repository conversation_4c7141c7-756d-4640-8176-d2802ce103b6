<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Helpers\EventTypeHelper;
use App\Enums\ApiCodeEnum;
use App\Models\CharacterLibrary;
use App\Models\UserCharacterBinding;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use App\Services\Common\TransactionManager;

/**
 * 角色管理服务
 */
class CharacterService
{
    protected $aiModelService;
    protected $pointsService;
    protected $imageService;

    public function __construct(
        AiModelService $aiModelService,
        PointsService $pointsService,
        ImageService $imageService
    ) {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
        $this->imageService = $imageService;
    }
    
    /**
     * 获取角色分类列表 
     */
    public function getCategories(): array
    {
        try {
            $categories = [
                ['name' => CharacterLibrary::CATEGORY_HUMAN, 'value' => CharacterLibrary::CATEGORY_HUMAN],
                ['name' => CharacterLibrary::CATEGORY_ANTHROPOMORPHIC, 'value' => CharacterLibrary::CATEGORY_ANTHROPOMORPHIC],
                ['name' => CharacterLibrary::CATEGORY_ANIMAL, 'value' => CharacterLibrary::CATEGORY_ANIMAL],
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $categories
            ];

        } catch (\Exception $e) {
            Log::error('获取角色分类失败', [
                'method' => __METHOD__,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色分类失败',
                'data' => null
            ];
        }
    }

    /**
     * 绑定角色 (扩展版本 - 支持完整绑定参数)
     */
    public function bindCharacter(int $userId, int $characterId, array $bindingParams = []): array
    {
        try {
            DB::beginTransaction();

            // 解析绑定参数
            $reason = $bindingParams['reason'] ?? '';
            $storyboardPositionId = $bindingParams['storyboard_position_id'] ?? null;
            $bindingContext = $bindingParams['binding_context'] ?? 'library';
            $autoBind = $bindingParams['auto_bind'] ?? false;
            $compatibilityCheck = $bindingParams['compatibility_check'] ?? true;
            $bindingName = $bindingParams['binding_name'] ?? null;
            $customConfig = $bindingParams['custom_config'] ?? [];

            // 检查角色是否存在
            $character = CharacterLibrary::active()->find($characterId);
            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 兼容性检查
            if ($compatibilityCheck && $storyboardPositionId) {
                $compatibilityResult = $this->checkCharacterCompatibility($character, $storyboardPositionId);
                if (!$compatibilityResult['compatible']) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '角色与分镜位置不兼容：' . $compatibilityResult['reason'],
                        'data' => []
                    ];
                }
            }

            // 检查是否已经绑定
            $existingBinding = UserCharacterBinding::byUser($userId)
                ->byCharacter($characterId)
                ->first();

            if ($existingBinding) {
                if ($existingBinding->is_active) {
                    return [
                        'code' => ApiCodeEnum::FAIL,
                        'message' => '角色已经绑定',
                        'data' => []
                    ];
                } else {
                    // 重新激活绑定
                    $existingBinding->is_active = true;
                    $existingBinding->binding_name = $bindingName ?: $character->name;
                    $existingBinding->custom_config = $customConfig;
                    $existingBinding->storyboard_position_id = $storyboardPositionId;
                    $existingBinding->binding_context = $bindingContext;
                    $existingBinding->save();

                    $character->incrementBinding();
                    DB::commit();

                    return [
                        'code' => ApiCodeEnum::SUCCESS,
                        'message' => '角色重新绑定成功',
                        'data' => [
                            'binding_id' => $existingBinding->id,
                            'character_id' => $character->id,
                            'character_name' => $character->name,
                            'binding_name' => $existingBinding->getDisplayName(),
                            'created_at' => $existingBinding->created_at->format('Y-m-d H:i:s')
                        ]
                    ];
                }
            }

            // 创建新绑定 (扩展版本)
            $binding = UserCharacterBinding::create([
                'user_id' => $userId,
                'character_id' => $characterId,
                'binding_name' => $bindingName ?: $character->name, // 默认使用角色名称
                'binding_reason' => $reason, // 绑定原因
                'custom_config' => $customConfig,
                'storyboard_position_id' => $storyboardPositionId, // 分镜位置ID
                'binding_context' => $bindingContext, // 绑定上下文
                'is_active' => true
            ]);

            // 增加角色绑定次数
            $character->incrementBinding();

            DB::commit();

            Log::info('角色绑定成功', [
                'user_id' => $userId,
                'character_id' => $characterId,
                'binding_id' => $binding->id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色绑定成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'character_id' => $character->id,
                    'character_name' => $character->name,
                    'binding_name' => $binding->getDisplayName(),
                    'storyboard_position_id' => $storyboardPositionId,
                    'binding_context' => $bindingContext,
                    'created_at' => $binding->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'character_id' => $characterId,
                'reason' => $reason,
                'binding_name' => $bindingName,
            ];

            Log::error('角色绑定失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色绑定失败',
                'data' => null
            ];
        }
    }

    /**
     * 检查角色与分镜位置的兼容性
     */
    private function checkCharacterCompatibility(CharacterLibrary $character, string $storyboardPositionId): array
    {
        try {
            // 这里应该实现真实的兼容性检查逻辑
            // 目前返回兼容，实际项目中需要根据分镜位置的要求检查角色属性

            // 示例兼容性检查规则：
            // 1. 检查角色风格是否匹配分镜要求
            // 2. 检查角色年龄范围是否适合场景
            // 3. 检查角色性格是否符合剧情需要

            Log::info('角色兼容性检查', [
                'character_id' => $character->id,
                'character_name' => $character->name,
                'storyboard_position_id' => $storyboardPositionId
            ]);

            return [
                'compatible' => true,
                'reason' => '',
                'suggestions' => []
            ];
        } catch (\Exception $e) {
            Log::error('角色兼容性检查失败', [
                'character_id' => $character->id,
                'storyboard_position_id' => $storyboardPositionId,
                'error' => $e->getMessage()
            ]);

            return [
                'compatible' => false,
                'reason' => '兼容性检查失败',
                'suggestions' => []
            ];
        }
    }

    /**
     * 解绑角色
     */
    public function unbindCharacter(int $bindingId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $binding = UserCharacterBinding::byUser($userId)->find($bindingId);
            if (!$binding) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '绑定不存在',
                    'data' => []
                ];
            }

            if (!$binding->is_active) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '绑定已解除',
                    'data' => []
                ];
            }

            // 解除绑定
            $binding->is_active = false;
            $binding->save();

            // 减少角色绑定次数
            $binding->character->decrementBinding();

            DB::commit();

            Log::info('角色解绑成功', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'character_id' => $binding->character_id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色解绑成功',
                'data' => [
                    'binding_id' => $bindingId,
                    'unbind_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'binding_id' => $bindingId,
            ];

            Log::error('角色解绑失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色解绑失败',
                'data' => null
            ];
        }
    }

    /**
     * 基于文件创建角色
     */
    public function createCharacterFromFile(int $userId, ?int $projectId = null, array $creationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 解析创建参数
            $fileId = $creationParams['file_id'];
            $style = $creationParams['style'] ?? null;
            $publishToLibrary = $creationParams['publish_to_library'] ?? false;
            $autoBind = $creationParams['auto_bind'] ?? false;
            $storyboardPositionId = $creationParams['storyboard_position_id'] ?? null;

            // 获取文件信息
            $file = \App\Models\UserFile::find($fileId);
            if (!$file) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '文件不存在',
                    'data' => []
                ];
            }

            // 验证文件所有权
            if ($file->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权访问该文件',
                    'data' => []
                ];
            }

            // 创建角色记录
            $character = CharacterLibrary::create([
                'name' => '自定义角色_' . date('YmdHis'),
                'thumbnail' => $file->file_url,
                'images' => [$file->file_url],
                'style' => $style,
                'created_by' => $userId,
                'project_id' => $projectId,
                'status' => 'published',
                'is_premium' => false,
                'is_featured' => false,
                'source_file_id' => $fileId
            ]);

            // 如果需要发布到角色库
            if ($publishToLibrary) {
                $character->is_public = true;
                $character->save();
            }

            // 如果需要自动绑定
            if ($autoBind) {
                $bindingParams = [
                    'reason' => '文件创建自动绑定',
                    'storyboard_position_id' => $storyboardPositionId,
                    'binding_context' => 'project',
                    'auto_bind' => true
                ];

                $bindingResult = $this->bindCharacter($userId, $character->id, $bindingParams);
                if ($bindingResult['code'] !== ApiCodeEnum::SUCCESS) {
                    Log::warning('自动绑定失败', [
                        'character_id' => $character->id,
                        'user_id' => $userId,
                        'error' => $bindingResult['message']
                    ]);
                }
            }

            DB::commit();

            Log::info('基于文件创建角色成功', [
                'character_id' => $character->id,
                'user_id' => $userId,
                'file_id' => $fileId,
                'style' => $style
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色创建成功',
                'data' => [
                    'character_id' => $character->id,
                    'name' => $character->name,
                    'image_url' => $character->thumbnail,
                    'style' => $character->style,
                    'is_public' => $character->is_public ?? false,
                    'created_at' => $character->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'project_id' => $projectId,
                'creation_params' => $creationParams,
            ];

            Log::error('基于文件创建角色失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新绑定
     */
    public function updateBinding(int $bindingId, int $userId, array $updateData): array
    {
        try {
            $binding = UserCharacterBinding::byUser($userId)->find($bindingId);
            if (!$binding) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '绑定不存在',
                    'data' => []
                ];
            }

            $binding->fill($updateData);
            $binding->save();

            Log::info('绑定更新成功', [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'update_data' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '绑定更新成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'binding_name' => $binding->getDisplayName(),
                    'is_favorite' => $binding->is_favorite,
                    'updated_at' => $binding->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'binding_id' => $bindingId,
                'update_data_keys' => array_keys($updateData),
            ];

            Log::error('绑定更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '绑定更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户绑定列表
     */
    public function getUserBindings(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = UserCharacterBinding::with(['character'])
                ->byUser($userId)
                ->active();

            // 应用筛选条件
            if (isset($filters['is_favorite']) && $filters['is_favorite']) {
                $query->favorite();
            }

            if (!empty($filters['category'])) {
                $query->whereHas('character', function ($q) use ($filters) {
                    $q->where('category', $filters['category']);
                });
            }

            // 排序
            switch ($filters['sort'] ?? 'usage') {
                case 'rating':
                    $query->byRating();
                    break;
                case 'created':
                    $query->orderBy('created_at', 'desc');
                    break;
                default:
                    $query->byUsage();
                    break;
            }

            $bindings = $query->paginate($perPage, ['*'], 'page', $page);

            $bindingsData = $bindings->map(function ($binding) {
                return [
                    'id' => $binding->id,
                    'character' => [
                        'id' => $binding->character->id,
                        'name' => $binding->character->name,
                        'thumbnail' => $binding->character->thumbnail,
                        'category' => $binding->character->category,
                        'rating' => $binding->character->rating
                    ],
                    'binding_name' => $binding->getDisplayName(),
                    'is_favorite' => $binding->is_favorite,
                    'usage_count' => $binding->usage_count,
                    'last_used_at' => $binding->last_used_at?->format('Y-m-d H:i:s'),
                    'user_rating' => $binding->user_rating,
                    'usage_frequency' => $binding->getUsageFrequency(),
                    'created_at' => $binding->created_at->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'bindings' => $bindingsData,
                    'pagination' => [
                        'current_page' => $bindings->currentPage(),
                        'total' => $bindings->total(),
                        'per_page' => $bindings->perPage(),
                        'last_page' => $bindings->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取用户绑定列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取用户绑定列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取角色列表
     */
    public function getCharacters(array $filters, int $page, int $perPage): array
    {
        try {
            $query = CharacterLibrary::active();

            // 应用筛选条件
            // tab参数处理：public/private
            if (!empty($filters['tab']) && $filters['tab'] === 'private') {
                // 私人角色筛选逻辑（基于created_by字段）
                $query->whereNotNull('created_by');
            } else {
                // 公开角色（系统预设角色，created_by为null）
                $query->whereNull('created_by');
            }

            if (!empty($filters['gender'])) {
                $query->byGender($filters['gender']);
            }

            if (!empty($filters['age_range'])) {
                $query->where('age_range', $filters['age_range']);
            }

            if (!empty($filters['search'])) {
                $query->search($filters['search']);
            }

            if (isset($filters['is_premium'])) {
                if ($filters['is_premium']) {
                    $query->premium();
                } else {
                    $query->free();
                }
            }

            if (isset($filters['is_featured']) && $filters['is_featured']) {
                $query->featured();
            }

            if (!empty($filters['tags'])) {
                foreach ($filters['tags'] as $tag) {
                    $query->byTag($tag);
                }
            }

            $characters = $query->ordered()->paginate($perPage, ['*'], 'page', $page);

            $charactersData = $characters->map(function ($character) {
                return [
                    'id' => $character->id,
                    'name' => $character->name,
                    'category' => $character->category,
                    'gender' => $character->gender,
                    'age_range' => $character->age_range,
                    'thumbnail' => $character->thumbnail,
                    'tags' => $character->tags ?? [],
                    'is_premium' => $character->is_premium,
                    'is_featured' => $character->is_featured,
                    'rating' => $character->rating,
                    'rating_count' => $character->rating_count,
                    'binding_count' => $character->binding_count
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'characters' => $charactersData,
                    'pagination' => [
                        'current_page' => $characters->currentPage(),
                        'total' => $characters->total(),
                        'per_page' => $characters->perPage(),
                        'last_page' => $characters->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取角色列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取角色详情 
     */
    public function getCharacterDetail(int $characterId, int $userId): array
    {
        try {
            $character = CharacterLibrary::active()->find($characterId);

            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 检查用户是否已绑定该角色
            $binding = UserCharacterBinding::byUser($userId)
                ->byCharacter($characterId)
                ->active()
                ->first();

            $characterData = [
                'id' => $character->id,
                'name' => $character->name,
                'category' => $character->category,
                'gender' => $character->gender,
                'age_range' => $character->age_range,
                'ai_prompt' => $character->ai_prompt,
                'thumbnail' => $character->thumbnail,
                'images' => $character->images ?? [],
                'tags' => $character->tags ?? [],
                'is_premium' => $character->is_premium,
                'is_featured' => $character->is_featured,
                'rating' => $character->rating,
                'rating_count' => $character->rating_count,
                'binding_count' => $character->binding_count,
                'is_bound' => !is_null($binding),
                'binding_id' => $binding?->id,
                'created_at' => $character->created_at->format('Y-m-d H:i:s')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $characterData
            ];

        } catch (\Exception $e) {
            $error_context = [
                'character_id' => $characterId,
                'user_id' => $userId,
            ];

            Log::error('获取角色详情失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取角色详情失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取推荐角色
     */
    public function getRecommendations(int $userId, int $limit, string $type): array
    {
        try {
            $query = CharacterLibrary::active();

            switch ($type) {
                case 'popular':
                    $characters = $query->popular($limit)->get();
                    break;

                case 'similar':
                    $characters = $this->getSimilarCharacters($userId, $limit);
                    break;

                case 'new':
                    $characters = $query->orderBy('created_at', 'desc')->limit($limit)->get();
                    break;

                default:
                    $characters = $query->featured()->limit($limit)->get();
                    break;
            }

            $recommendationsData = $characters->map(function ($character) use ($type) {
                return [
                    'id' => $character->id,
                    'name' => $character->name,
                    'category' => $character->category,
                    'thumbnail' => $character->thumbnail,
                    'rating' => $character->rating,
                    'binding_count' => $character->binding_count,
                    'reason' => $this->getRecommendationReason($type)
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'recommendations' => $recommendationsData,
                    'recommendation_type' => $type,
                    'total' => $recommendationsData->count()
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'limit' => $limit,
                'type' => $type,
            ];

            Log::error('获取推荐角色失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐角色失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取相似角色
     */
    private function getSimilarCharacters(int $userId, int $limit)
    {
        // 获取用户已绑定的角色
        $userBindings = UserCharacterBinding::byUser($userId)
            ->active()
            ->with('character')
            ->get();

        if ($userBindings->isEmpty()) {
            // 如果用户没有绑定角色，返回推荐角色
            return CharacterLibrary::active()->featured()->limit($limit)->get();
        }

        // 获取用户偏好的分类
        $preferredCategories = $userBindings->pluck('character.category')->unique();

        // 基于偏好推荐相似角色
        $query = CharacterLibrary::active()
            ->whereNotIn('id', $userBindings->pluck('character_id'));

        if ($preferredCategories->isNotEmpty()) {
            $query->whereIn('category', $preferredCategories);
        }

        return $query->limit($limit)->get();
    }

    /**
     * 获取推荐理由
     */
    private function getRecommendationReason(string $type): string
    {
        $reasons = [
            'popular' => '热门推荐',
            'similar' => '基于您的偏好推荐',
            'new' => '最新角色',
            'featured' => '精选推荐'
        ];

        return $reasons[$type] ?? '系统推荐';
    }

    /**
     * 生成角色
     * 第2D1阶段：角色生成功能
     */
    public function generateCharacter(int $userId, string $prompt, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'deepseek';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_TEXT_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的角色生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '角色生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildCharacterPrompt($prompt, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateCharacterCost($model, $enhancedPrompt);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'character_generation',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_CHARACTER_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'character_type' => $generationParams['character_type'] ?? null,
                    'gender' => $generationParams['gender'] ?? null,
                    'age_range' => $generationParams['age_range'] ?? null
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeCharacterGeneration($task);

            Log::info('角色生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('角色生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色生成失败',
                'data' => null
            ];
        }
    }

    /**
     * 构建角色提示词
     */
    private function buildCharacterPrompt(string $prompt, array $params): string
    {
        $enhancedPrompt = "请生成一个详细的角色描述，包含以下信息：\n";
        $enhancedPrompt .= "基础要求：" . $prompt . "\n\n";

        // 添加性别要求
        if (!empty($params['gender'])) {
            $genderMap = [
                'male' => '男性',
                'female' => '女性',
                'other' => '未知',
                '男性' => '男性',
                '女性' => '女性',
                '未知' => '未知'
            ];
            $enhancedPrompt .= "性别：" . $genderMap[$params['gender']] . "\n";
        }

        // 添加年龄要求
        if (!empty($params['age_range'])) {
            $enhancedPrompt .= "年龄范围：" . $params['age_range'] . "\n";
        }

        // 添加角色类型要求
        if (!empty($params['character_type'])) {
            $enhancedPrompt .= "角色类型：" . $params['character_type'] . "\n";
        }

        $enhancedPrompt .= "\n请按以下格式生成角色信息：\n";
        $enhancedPrompt .= "姓名：[角色姓名]\n";
        $enhancedPrompt .= "描述：[角色的基本描述]\n";
        $enhancedPrompt .= "性格：[性格特点]\n";
        $enhancedPrompt .= "背景：[角色背景故事]\n";
        $enhancedPrompt .= "外观：[外观特征描述]\n";
        $enhancedPrompt .= "技能：[角色拥有的技能或能力]\n";
        $enhancedPrompt .= "关系：[与其他角色的关系]";

        return $enhancedPrompt;
    }

    /**
     * 计算角色生成成本
     */
    private function calculateCharacterCost(AiModelConfig $model, string $prompt): float
    {
        $baseTokens = strlen($prompt) / 4; // 估算token数
        $estimatedTokens = $baseTokens * 1.2; // 角色生成相对简单
        return round($estimatedTokens * $model->cost_per_token, 4);
    }

    /**
     * 执行角色生成
     */
    private function executeCharacterGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'character_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $eventType = EventTypeHelper::getFailedEventType('character_creation') ?? 'character_generation_failed';
                $this->pointsService->refundPoints($task->user_id, $task->cost, $eventType, $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $eventType = EventTypeHelper::getFailedEventType('character_creation') ?? 'character_generation_error';
            $this->pointsService->refundPoints($task->user_id, $task->cost, $eventType, $task->id);

            Log::error('角色生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $taskType = 'character_generation';
            $requestData = [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->input_data['enhanced_prompt']
                    ]
                ],
                'temperature' => 0.8,
                'max_tokens' => 1000
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $taskType,
                $requestData,
                $task->user_id
            );

            if ($response['success']) {
                $data = $response['data'];
                $content = $data['choices'][0]['message']['content'] ?? '';

                return [
                    'success' => true,
                    'data' => [
                        'character_info' => $this->parseCharacterInfo($content),
                        'mode' => $response['mode']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . ($response['error'] ?? '未知错误')
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 解析角色信息
     */
    private function parseCharacterInfo(string $content): array
    {
        $characterInfo = [
            'name' => '',
            'ai_prompt' => '',
            'category' => '人类',
            'gender' => null,
            'age_range' => null,
            'tags' => []
        ];

        // 简单的文本解析逻辑
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            if (preg_match('/^姓名[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['name'] = trim($matches[1]);
            } elseif (preg_match('/^分类[：:]\s*(.+)/', $line, $matches)) {
                $category = trim($matches[1]);
                if (in_array($category, ['人类', '拟人动物', '原生动物'])) {
                    $characterInfo['category'] = $category;
                }
            } elseif (preg_match('/^性别[：:]\s*(.+)/', $line, $matches)) {
                $gender = trim($matches[1]);
                $characterInfo['gender'] = $this->mapGenderToEnum($gender);
            } elseif (preg_match('/^年龄[：:]\s*(.+)/', $line, $matches)) {
                $characterInfo['age_range'] = trim($matches[1]);
            } elseif (preg_match('/^标签[：:]\s*(.+)/', $line, $matches)) {
                $tagString = trim($matches[1]);
                // 支持多种分割符：中文顿号、逗号、分号等
                $tags = preg_split('/[,，;；、]/', $tagString);
                $tags = array_map('trim', $tags);
                $tags = array_filter($tags, function($tag) {
                    return !empty($tag);
                });
                // 限制最多5个标签
                $characterInfo['tags'] = array_slice(array_values($tags), 0, 5);
            }
        }

        // 如果解析失败，使用原始内容作为AI提示词
        if (empty($characterInfo['name']) && empty($characterInfo['ai_prompt'])) {
            $characterInfo['ai_prompt'] = $content;
            $characterInfo['name'] = '生成的角色';
        } elseif (empty($characterInfo['ai_prompt'])) {
            $characterInfo['ai_prompt'] = $content;
        }

        return $characterInfo;
    }

    /**
     * 生成角色图像（智能角色模式）
     * 调用ImageService而不是直接调用AiServiceClient
     */
    public function generateCharacterImage(int $userId, string $prompt, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            // 构建角色图像生成的提示词
            $characterPrompt = $this->buildCharacterImagePrompt($prompt, $generationParams);

            // 设置角色图像生成的默认参数
            $imageGenerationParams = array_merge([
                'aspect_ratio' => '1:1', // 角色头像通常是正方形
                'quality' => 'hd',
                'style' => $generationParams['style'] ?? '写实风格',
                'platform' => $generationParams['platform'] ?? 'liblib'
            ], $generationParams);

            // 调用ImageService生成角色图像
            $result = $this->imageService->generateImage(
                $userId,
                $characterPrompt,
                null, // character_id 为空，因为这是在创建新角色
                $projectId,
                $imageGenerationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                Log::info('角色图像生成成功', [
                    'user_id' => $userId,
                    'project_id' => $projectId,
                    'platform' => $imageGenerationParams['platform'],
                    'cost' => $result['data']['cost'] ?? 0
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '角色图像生成成功',
                    'data' => [
                        'image_url' => $result['data']['image_url'] ?? '',
                        'thumbnail_url' => $result['data']['thumbnail_url'] ?? '',
                        'resource_id' => $result['data']['resource_id'] ?? null,
                        'platform' => $result['data']['platform'] ?? '',
                        'cost' => $result['data']['cost'] ?? 0,
                        'generation_params' => $imageGenerationParams
                    ]
                ];
            } else {
                return $result; // 直接返回ImageService的错误结果
            }

        } catch (\Exception $e) {
            Log::error('角色图像生成失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'prompt' => substr($prompt, 0, 100),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色图像生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 构建角色图像生成的提示词
     */
    private function buildCharacterImagePrompt(string $basePrompt, array $generationParams): string
    {
        $prompt = $basePrompt;

        // 添加角色特定的描述
        if (!empty($generationParams['gender'])) {
            $prompt .= ', ' . $generationParams['gender'];
        }

        if (!empty($generationParams['age_range'])) {
            $prompt .= ', ' . $generationParams['age_range'];
        }

        if (!empty($generationParams['character_type'])) {
            $prompt .= ', ' . $generationParams['character_type'];
        }

        // 添加角色图像的通用描述
        $prompt .= ', 高质量角色头像, 清晰的面部特征, 专业摄影';

        // 添加风格描述
        if (!empty($generationParams['style'])) {
            $prompt .= ', ' . $generationParams['style'];
        }

        return $prompt;
    }

    /**
     * 更新角色数据（基于解析后的角色数据数组）
     * 使用事务管理确保数据一致性
     *
     * @param int $characterId 角色ID
     * @param array $characterData 解析后的角色数据数组
     * @param int $userId 用户ID（用于日志记录）
     * @return array 处理结果
     */
    public function updateAiCharacterFromData(int $characterId, array $characterData, int $userId): array
    {
        try {
            // 开始事务
            TransactionManager::begin('CharacterService.updateCharacterFromData');

            // 验证角色是否存在
            $character = CharacterLibrary::find($characterId);
            if (!$character) {
                TransactionManager::rollback('CharacterService.updateCharacterFromData', '指定的角色不存在');
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的角色不存在',
                    'data' => []
                ];
            }

            // 验证角色数据格式
            if (!$this->validateAiCharacterData($characterData)) {
                TransactionManager::rollback('CharacterService.updateCharacterFromData', '角色数据格式无效');

                Log::warning('角色数据格式无效', [
                    'user_id' => $userId,
                    'character_id' => $characterId,
                    'character_data' => $characterData
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '角色数据格式无效',
                    'data' => []
                ];
            }

            // 准备更新数据
            $updateData = [];

            // 更新角色名称
            if (isset($characterData['角色名称']) && !empty($characterData['角色名称'])) {
                $updateData['name'] = $characterData['角色名称'];
            }

            // 更新AI提示词
            if (isset($characterData['提示词']) && !empty($characterData['提示词'])) {
                $updateData['ai_prompt'] = $characterData['提示词'];
            }

            // 根据AI生成的数据更新对应字段
            if (isset($characterData['类型'])) {
                $updateData['category'] = $this->mapCategoryToEnum($characterData['类型']);
            }

            if (isset($characterData['性别'])) {
                $updateData['gender'] = $this->mapGenderToEnum($characterData['性别']);
            }

            if (isset($characterData['年龄阶段'])) {
                $updateData['age_range'] = $characterData['年龄阶段'];
            }

            // 处理 TAG 标签
            if (isset($characterData['TAG']) && is_array($characterData['TAG'])) {
                $updateData['tags'] = $characterData['TAG'];
            }

            // 如果没有需要更新的数据，直接返回成功
            if (empty($updateData)) {
                TransactionManager::commit('CharacterService.updateCharacterFromData');

                Log::info('角色数据无需更新', [
                    'user_id' => $userId,
                    'character_id' => $characterId,
                    'character_name' => $character->name,
                    'character_data' => $characterData
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '角色数据无需更新',
                    'data' => [
                        'character_id' => $characterId,
                        'character_name' => $character->name,
                        'updated_data' => [],
                        'parsed_character_data' => $characterData
                    ]
                ];
            }

            // 更新角色数据
            $character->update($updateData);

            // 提交事务
            TransactionManager::commit('CharacterService.updateCharacterFromData');

            Log::info('角色数据更新成功', [
                'user_id' => $userId,
                'character_id' => $characterId,
                'character_name' => $character->name,
                'updated_fields' => array_keys($updateData),
                'parsed_data' => $characterData
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色数据更新成功',
                'data' => [
                    'character_id' => $characterId,
                    'character_name' => $character->name,
                    'updated_data' => $updateData,
                    'parsed_character_data' => $characterData
                ]
            ];

        } catch (\Exception $e) {
            // 回滚事务
            TransactionManager::rollback('CharacterService.updateCharacterFromData', $e->getMessage());

            Log::error('更新角色数据失败', [
                'method' => __METHOD__,
                'user_id' => $userId,
                'character_id' => $characterId,
                'character_data' => $characterData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '更新角色数据失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 验证角色数据格式
     *
     * @param array $characterData 角色数据数组
     * @return bool 验证结果
     */
    private function validateAiCharacterData(array $characterData): bool
    {
        try {
            // 验证必需字段
            $requiredFields = ['角色名称', '提示词', '类型', '性别', '年龄阶段'];
            foreach ($requiredFields as $field) {
                if (!isset($characterData[$field]) || empty($characterData[$field])) {
                    Log::warning('角色数据缺少必需字段', [
                        'missing_field' => $field,
                        'data' => $characterData
                    ]);
                    return false;
                }
            }

            // 验证类型字段值
            $validTypes = ['人类', '拟人动物', '原生动物'];
            if (!in_array($characterData['类型'], $validTypes)) {
                Log::warning('角色类型无效', [
                    'invalid_type' => $characterData['类型'],
                    'valid_types' => $validTypes
                ]);
                return false;
            }

            // 验证性别字段值（支持新的中文选项）
            $validGenders = ['男', '女', '男性', '女性', '未知'];
            if (!in_array($characterData['性别'], $validGenders)) {
                Log::warning('角色性别无效', [
                    'invalid_gender' => $characterData['性别'],
                    'valid_genders' => $validGenders
                ]);
                return false;
            }

            // 验证年龄阶段字段值
            $validAgeRanges = ['婴幼儿(0-6岁)', '儿童(6-12岁)', '少年(12-18岁)', '青年(18-40岁)', '中年(40-65岁)', '老年(65岁以上)'];
            if (!in_array($characterData['年龄阶段'], $validAgeRanges)) {
                Log::warning('角色年龄阶段无效', [
                    'invalid_age_range' => $characterData['年龄阶段'],
                    'valid_age_ranges' => $validAgeRanges
                ]);
                return false;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('验证角色数据失败', [
                'data' => $characterData,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 将中文类型映射到数据库枚举值
     *
     * @param string $chineseType 中文类型
     * @return string 数据库枚举值
     */
    private function mapCategoryToEnum(string $chineseType): string
    {
        $mapping = [
            '人类' => CharacterLibrary::CATEGORY_HUMAN,
            '拟人动物' => CharacterLibrary::CATEGORY_ANTHROPOMORPHIC,
            '原生动物' => CharacterLibrary::CATEGORY_ANIMAL,
        ];

        return $mapping[$chineseType] ?? CharacterLibrary::CATEGORY_HUMAN;
    }

    /**
     * 映射性别字段（简化版，直接使用中文）
     *
     * @param string $gender 性别
     * @return string 标准化的性别值
     */
    private function mapGenderToEnum(string $gender): string
    {
        // 兼容旧的映射和新的中文直接使用
        $mapping = [
            // 英文值映射
            'male' => CharacterLibrary::GENDER_MALE,
            'female' => CharacterLibrary::GENDER_FEMALE,
            'other' => CharacterLibrary::GENDER_OTHER,
            // 中文值映射
            '男' => CharacterLibrary::GENDER_MALE,
            '女' => CharacterLibrary::GENDER_FEMALE,
            '男性' => CharacterLibrary::GENDER_MALE,
            '女性' => CharacterLibrary::GENDER_FEMALE,
            '未知' => CharacterLibrary::GENDER_OTHER,
        ];

        return $mapping[$gender] ?? CharacterLibrary::GENDER_OTHER;
    }

    /**
     * 验证角色是否存在且激活
     *
     * @param int $characterId 角色ID
     * @return array 验证结果
     */
    public function validateCharacterExists(int $characterId): array
    {
        try {
            $character = CharacterLibrary::where('id', $characterId)
                ->where('status', 'draft')
                ->first();

            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的角色不存在或已被禁用',
                    'data' => []
                ];
            }

            Log::info('角色验证成功', [
                'character_id' => $characterId,
                'character_name' => $character->name
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色验证成功',
                'data' => [
                    'character' => $character
                ]
            ];

        } catch (\Exception $e) {
            Log::error('角色验证失败', [
                'method' => __METHOD__,
                'character_id' => $characterId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色验证失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}
