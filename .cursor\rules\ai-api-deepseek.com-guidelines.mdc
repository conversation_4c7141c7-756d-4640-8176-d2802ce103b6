---
alwaysApply: false
---
## DeepSeek API 接口开发规范指南 (官方完整版)

### API概述

**官方文档**: https://api-docs.deepseek.com/zh-cn/
**API基础URL**: `https://api.deepseek.com`
**Beta功能URL**: `https://api.deepseek.com/beta`
**兼容性**: 与OpenAI API格式完全兼容
**主要用途**: 剧情生成、分镜脚本创作、对话生成、代码补全、推理分析、多轮对话

### 认证配置

#### API密钥获取
```bash
# 获取API密钥地址
https://platform.deepseek.com/api_keys
```

#### 基础配置参数
```json
{
    "base_url": "https://api.deepseek.com",
    "beta_url": "https://api.deepseek.com/beta",
    "api_key": "your-deepseek-api-key",
    "timeout": 60,
    "max_retries": 3,
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-deepseek-api-key"
    }
}
```

### 支持的模型

#### 1. DeepSeek-Chat (deepseek-chat)
- **模型版本**: DeepSeek-V3-0324
- **适用场景**: 通用对话、剧情创作、文本生成、Function Calling
- **上下文长度**: 128K tokens
- **输出长度**: 默认 4K，最大 8K tokens
- **特殊功能**: 支持JSON输出、工具调用、上下文缓存、FIM补全(Beta)、对话前缀续写(Beta)

**价格信息**:
- **标准时段** (北京时间 08:30-00:30):
  - 输入: ¥2.0/1M tokens (缓存未命中), ¥0.5/1M tokens (缓存命中)
  - 输出: ¥8.0/1M tokens
- **优惠时段** (北京时间 00:30-08:30):
  - 输入: ¥1.0/1M tokens (缓存未命中), ¥0.25/1M tokens (缓存命中)
  - 输出: ¥4.0/1M tokens

#### 2. DeepSeek-Reasoner (deepseek-reasoner)
- **模型版本**: DeepSeek-R1-0528
- **适用场景**: 复杂推理、逻辑分析、分镜规划、数学问题
- **上下文长度**: 64K tokens (输出长度不计入上下文限制)
- **输出长度**: 默认 32K，最大 64K tokens (包含思维链输出)
- **特殊功能**: 包含推理过程(reasoning_content)、思维链展示、Function Calling、JSON输出、对话前缀续写(Beta)
- **不支持功能**: FIM补全、temperature/top_p/presence_penalty/frequency_penalty/logprobs/top_logprobs参数

**价格信息**:
- **标准时段** (北京时间 08:30-00:30):
  - 输入: ¥4.0/1M tokens (缓存未命中), ¥1.0/1M tokens (缓存命中)
  - 输出: ¥16.0/1M tokens (包含思维链和最终答案的所有tokens)
- **优惠时段** (北京时间 00:30-08:30):
  - 输入: ¥1.0/1M tokens (缓存未命中), ¥0.25/1M tokens (缓存命中)
  - 输出: ¥4.0/1M tokens

**重要说明**:
- 请求的计价时间为该请求完成的时间
- 当充值余额与赠送余额同时存在时，优先扣减赠送余额
- 产品价格可能发生变动，请定期查看官方文档获取最新价格信息

### 完整API接口列表 (基于官方文档)

#### 1. 对话完成接口 (Chat Completions)

**接口地址**: `POST /chat/completions`
**功能**: 根据输入的上下文，让模型补全对话内容
**支持模型**: deepseek-chat, deepseek-reasoner
**官方文档**: https://api-docs.deepseek.com/zh-cn/api/create-chat-completion

**完整参数列表**:
```php
<?php

class DeepSeekAPI
{
    private $apiKey;
    private $baseUrl = 'https://api.deepseek.com';
    private $betaUrl = 'https://api.deepseek.com/beta';

    public function __construct($apiKey)
    {
        $this->apiKey = $apiKey;
    }

    /**
     * 调用对话完成接口 - 完整参数版本
     */
    public function chatCompletion($messages, $options = [])
    {
        $defaultOptions = [
            'model' => 'deepseek-chat',
            'messages' => $messages,
            'stream' => false,
            'temperature' => 0.7,
            'max_tokens' => 4096,
            'top_p' => 0.9,
            'frequency_penalty' => 0,
            'presence_penalty' => 0,
            'response_format' => ['type' => 'text'],
            'stop' => null,
            'stream_options' => null,
            'tools' => null,
            'tool_choice' => 'auto',
            'logprobs' => false,
            'top_logprobs' => null
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/chat/completions', $payload);
    }

    /**
     * FIM补全接口 (Beta功能) - Fill-In-the-Middle
     * 官方文档: https://api-docs.deepseek.com/zh-cn/api/create-completion
     *
     * ⚠️ 重要: 必须设置 base_url="https://api.deepseek.com/beta" 来使用此功能
     * ⚠️ 注意: deepseek-reasoner 模型不支持此功能
     */
    public function fimCompletion($prompt, $options = [])
    {
        $defaultOptions = [
            'model' => 'deepseek-chat',         // 仅支持 deepseek-chat
            'prompt' => $prompt,
            'echo' => false,                    // 在输出中，把 prompt 的内容也输出出来
            'frequency_penalty' => 0,           // -2.0 到 2.0 之间
            'logprobs' => null,                 // 最大值是 20
            'max_tokens' => null,               // 最大生成 token 数量
            'presence_penalty' => 0,            // -2.0 到 2.0 之间
            'stop' => null,                     // 停止序列
            'stream' => false,                  // 流式输出
            'stream_options' => null,           // 流式选项配置
            'suffix' => null,                   // 制定被补全内容的后缀
            'temperature' => 1,                 // 0 到 2 之间
            'top_p' => 1                        // 0 到 1 之间
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/completions', $payload, true); // 必须使用beta URL
    }

    /**
     * 获取模型列表
     * 官方文档: https://api-docs.deepseek.com/zh-cn/api/list-models
     * 接口地址: GET https://api.deepseek.com/models
     */
    public function listModels()
    {
        return $this->makeRequest('/models', [], false, 'GET');
    }

    /**
     * 查询账户余额
     * 官方文档: https://api-docs.deepseek.com/zh-cn/api/get-user-balance
     * 接口地址: GET https://api.deepseek.com/user/balance
     * 返回格式: {
     *   "is_available": boolean,  // 当前账户是否有余额可供 API 调用
     *   "balance_infos": [
     *     {
     *       "currency": "CNY",
     *       "total_balance": "110.00",
     *       "granted_balance": "10.00",
     *       "topped_up_balance": "100.00"
     *     }
     *   ]
     * }
     */
    public function getUserBalance()
    {
        return $this->makeRequest('/user/balance', [], false, 'GET');
    }

    /**
     * 发送HTTP请求
     */
    private function makeRequest($endpoint, $data = [], $useBeta = false, $method = 'POST')
    {
        $url = ($useBeta ? $this->betaUrl : $this->baseUrl) . $endpoint;

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $ch = curl_init();
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_SSL_VERIFYPEER => false
        ];

        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }

        curl_setopt_array($ch, $options);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("DeepSeek API请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }
}
```

#### 2. FIM补全接口 (Fill-In-the-Middle)

**接口地址**: `POST /completions`
**功能**: 代码补全，填充中间部分
**使用要求**: 需要设置 `base_url="https://api.deepseek.com/beta"`
**支持模型**: deepseek-chat
**官方文档**: https://api-docs.deepseek.com/zh-cn/api/create-completion

**完整参数说明**:
- `model` (string, required): 模型的 ID，可选值: ["deepseek-chat"]
- `prompt` (string, required): 用于生成完成内容的提示，默认值: "Once upon a time,"
- `echo` (boolean, nullable): 在输出中，把 prompt 的内容也输出出来
- `frequency_penalty` (number, nullable): -2.0 到 2.0 之间，默认值: 0
- `logprobs` (integer, nullable): 最大值是 20，制定输出中包含 logprobs 最可能输出 token 的对数概率
- `max_tokens` (integer, nullable): 最大生成 token 数量
- `presence_penalty` (number, nullable): -2.0 到 2.0 之间，默认值: 0
- `stop` (object, nullable): 停止序列
- `stream` (boolean, nullable): 流式输出，以 SSE 形式发送消息增量
- `stream_options` (object, nullable): 流式选项配置
- `suffix` (string, nullable): 制定被补全内容的后缀
- `temperature` (number, nullable): 0 到 2 之间，默认值: 1
- `top_p` (number, nullable): 0 到 1 之间，默认值: 1

#### 3. 模型列表接口

**接口地址**: `GET /models`
**功能**: 列出可用的模型列表
**返回格式**:
```json
{
  "object": "list",
  "data": [
    {
      "id": "deepseek-chat",
      "object": "model",
      "owned_by": "deepseek"
    },
    {
      "id": "deepseek-reasoner",
      "object": "model",
      "owned_by": "deepseek"
    }
  ]
}
```

#### 4. 余额查询接口

**接口地址**: `GET /user/balance`
**功能**: 查询账户余额信息
**返回格式**:
```json
{
  "is_available": true,
  "balance_infos": [
    {
      "currency": "CNY",
      "total_balance": "110.00",
      "granted_balance": "10.00",
      "topped_up_balance": "100.00"
    }
  ]
}
```

### 高级功能接口

#### 1. 流式响应处理

```php
/**
 * 流式对话完成 - 支持实时输出
 */
public function streamChatCompletion($messages, $callback, $options = [])
{
    $options['stream'] = true;
    $options['stream_options'] = ['include_usage' => true]; // 包含使用统计

    $payload = array_merge([
        'model' => 'deepseek-chat',
        'messages' => $messages,
        'temperature' => 0.7,
        'max_tokens' => 4096
    ], $options);

    $url = $this->baseUrl . '/chat/completions';
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $this->apiKey,
        'Accept: text/event-stream'
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_WRITEFUNCTION => function($ch, $data) use ($callback) {
            $lines = explode("\n", $data);
            foreach ($lines as $line) {
                if (strpos($line, 'data: ') === 0) {
                    $json = substr($line, 6);
                    if ($json === '[DONE]') {
                        return strlen($data);
                    }

                    $decoded = json_decode($json, true);
                    if ($decoded) {
                        // 处理内容增量
                        if (isset($decoded['choices'][0]['delta']['content'])) {
                            $callback('content', $decoded['choices'][0]['delta']['content']);
                        }
                        // 处理推理内容 (仅deepseek-reasoner)
                        if (isset($decoded['choices'][0]['delta']['reasoning_content'])) {
                            $callback('reasoning', $decoded['choices'][0]['delta']['reasoning_content']);
                        }
                        // 处理使用统计
                        if (isset($decoded['usage'])) {
                            $callback('usage', $decoded['usage']);
                        }
                        // 处理完成状态
                        if (isset($decoded['choices'][0]['finish_reason'])) {
                            $callback('finish_reason', $decoded['choices'][0]['finish_reason']);
                        }
                    }
                }
            }
            return strlen($data);
        },
        CURLOPT_TIMEOUT => 300,
        CURLOPT_SSL_VERIFYPEER => false
    ]);

    curl_exec($ch);
    curl_close($ch);
}

/**
 * JSON输出模式
 */
public function jsonCompletion($messages, $jsonSchema = null)
{
    $options = [
        'response_format' => ['type' => 'json_object'],
        'temperature' => 0.3 // 降低温度确保JSON格式稳定
    ];

    // 在系统消息中明确要求JSON输出
    if (!empty($messages) && $messages[0]['role'] === 'system') {
        $messages[0]['content'] .= "\n\n请以有效的JSON格式回复。";
    } else {
        array_unshift($messages, [
            'role' => 'system',
            'content' => '请以有效的JSON格式回复。'
        ]);
    }

    return $this->chatCompletion($messages, $options);
}
```

#### 2. Function Calling (工具调用)

```php
/**
 * Function Calling 示例
 */
public function functionCalling($messages, $functions, $toolChoice = 'auto')
{
    $tools = [];
    foreach ($functions as $function) {
        $tools[] = [
            'type' => 'function',
            'function' => $function
        ];
    }

    $options = [
        'tools' => $tools,
        'tool_choice' => $toolChoice,
        'temperature' => 0.1 // 工具调用使用较低温度
    ];

    return $this->chatCompletion($messages, $options);
}

/**
 * 处理工具调用响应
 */
public function handleToolCalls($response, $availableFunctions)
{
    if (!isset($response['choices'][0]['message']['tool_calls'])) {
        return $response;
    }

    $toolCalls = $response['choices'][0]['message']['tool_calls'];
    $toolMessages = [];

    foreach ($toolCalls as $toolCall) {
        $functionName = $toolCall['function']['name'];
        $arguments = json_decode($toolCall['function']['arguments'], true);

        if (isset($availableFunctions[$functionName])) {
            $result = call_user_func($availableFunctions[$functionName], $arguments);
            $toolMessages[] = [
                'role' => 'tool',
                'tool_call_id' => $toolCall['id'],
                'content' => json_encode($result)
            ];
        }
    }

    return $toolMessages;
}
```

#### 3. 对话前缀续写 (Beta)

```php
/**
 * 对话前缀续写功能
 *
 * ⚠️ 重要: 必须设置 base_url="https://api.deepseek.com/beta" 来使用此功能
 * 支持模型: deepseek-chat, deepseek-reasoner
 */
public function prefixCompletion($messages, $prefix, $reasoningContent = null, $model = 'deepseek-chat')
{
    // 添加带前缀的assistant消息
    $assistantMessage = [
        'role' => 'assistant',
        'content' => $prefix,
        'prefix' => true  // Beta功能标识
    ];

    // 如果是推理模型且提供了推理内容，可以添加推理内容
    if ($reasoningContent && $model === 'deepseek-reasoner') {
        $assistantMessage['reasoning_content'] = $reasoningContent;
    }

    $messages[] = $assistantMessage;

    return $this->makeRequest('/chat/completions', [
        'model' => $model,
        'messages' => $messages,
        'temperature' => $model === 'deepseek-reasoner' ? null : 0.7  // 推理模型不支持temperature
    ], true); // 必须使用beta URL
}

/**
 * 推理模型专用前缀续写
 * 支持思维链内容的前缀续写
 */
public function reasonerPrefixCompletion($messages, $prefix, $reasoningContent = null)
{
    return $this->prefixCompletion($messages, $prefix, $reasoningContent, 'deepseek-reasoner');
}
```

### 消息格式规范

#### 1. 基础消息结构
```php
$messages = [
    [
        'role' => 'system',
        'content' => '你是一个专业的剧本创作助手，擅长创作引人入胜的故事情节。'
    ],
    [
        'role' => 'user', 
        'content' => '请为我创作一个科幻短片的剧本大纲'
    ]
];
```

#### 2. 角色类型说明
- **system**: 系统提示，定义AI的角色和行为
- **user**: 用户输入的内容
- **assistant**: AI的回复内容

### 参数配置指南

#### 1. Temperature (温度参数)
```php
// 创意写作 - 较高温度
$options['temperature'] = 0.8;

// 逻辑推理 - 较低温度  
$options['temperature'] = 0.3;

// 平衡模式
$options['temperature'] = 0.7;
```

#### 2. Max Tokens (最大输出长度)
```php
// 短回复
$options['max_tokens'] = 1024;

// 长文本生成
$options['max_tokens'] = 4096;

// 完整剧本
$options['max_tokens'] = 8192;
```

#### 3. Top-p (核采样)
```php
// 更聚焦的输出
$options['top_p'] = 0.8;

// 更多样化的输出
$options['top_p'] = 0.95;
```

### 专用提示模板

#### 1. 剧情生成模板
```php
class StoryPrompts
{
    /**
     * 剧情大纲生成
     */
    public static function storyOutline($genre, $theme, $length)
    {
        return [
            'role' => 'system',
            'content' => "你是一个专业的{$genre}剧本创作专家。请根据主题'{$theme}'创作一个{$length}的剧情大纲，包含：
1. 故事背景设定
2. 主要角色介绍
3. 情节发展脉络
4. 冲突与转折点
5. 结局设计

要求：情节紧凑、逻辑清晰、富有戏剧张力。"
        ];
    }
    
    /**
     * 分镜脚本生成
     */
    public static function storyboard($scene, $style)
    {
        return [
            'role' => 'system', 
            'content' => "你是一个专业的分镜师。请将以下场景'{$scene}'转换为详细的分镜脚本，风格为{$style}。

每个镜头包含：
1. 镜头编号
2. 景别（特写/中景/全景等）
3. 机位角度
4. 画面描述
5. 角色动作
6. 对白内容
7. 时长估计

格式要求：清晰明确，便于后期制作。"
        ];
    }
}
```

#### 2. 角色对话生成
```php
/**
 * 角色对话生成
 */
public function generateDialogue($character1, $character2, $situation)
{
    $messages = [
        [
            'role' => 'system',
            'content' => "你是一个对话写作专家。请为以下角色创作自然流畅的对话：
角色1：{$character1}
角色2：{$character2}
情境：{$situation}

要求：
1. 符合角色性格特点
2. 推进剧情发展
3. 语言自然生动
4. 包含情感层次"
        ],
        [
            'role' => 'user',
            'content' => "请开始创作这段对话"
        ]
    ];
    
    return $this->chatCompletion($messages, [
        'temperature' => 0.8,
        'max_tokens' => 2048
    ]);
}
```

### 错误处理规范

#### 1. 常见错误码
```php
class DeepSeekErrorHandler
{
    const ERROR_CODES = [
        400 => '请求参数错误',
        401 => 'API密钥无效',
        403 => '权限不足或余额不足',
        404 => '请求的资源不存在',
        429 => '请求频率超限',
        500 => '服务器内部错误',
        503 => '服务暂时不可用'
    ];

    const FINISH_REASON_CODES = [
        'stop' => '模型自然停止生成，或遇到stop序列',
        'length' => '输出长度达到模型上下文长度限制或max_tokens限制',
        'content_filter' => '输出内容因触发过滤策略而被过滤',
        'tool_calls' => '模型调用了工具函数',
        'insufficient_system_resource' => '系统推理资源不足，生成被打断'
    ];

    public static function handleError($httpCode, $response)
    {
        $errorMsg = self::ERROR_CODES[$httpCode] ?? '未知错误';

        if (isset($response['error'])) {
            $errorMsg .= ': ' . $response['error']['message'];
        }

        switch ($httpCode) {
            case 429:
                // 实现重试逻辑
                sleep(1);
                return 'retry';

            case 401:
                throw new Exception('API密钥无效，请检查配置');

            case 403:
                throw new Exception('权限不足或余额不足，请检查账户状态');

            default:
                throw new Exception($errorMsg);
        }
    }

    /**
     * 处理完成状态
     */
    public static function handleFinishReason($finishReason)
    {
        $message = self::FINISH_REASON_CODES[$finishReason] ?? '未知完成状态';

        switch ($finishReason) {
            case 'insufficient_system_resource':
                // 系统资源不足，建议重试
                return ['status' => 'retry', 'message' => $message];

            case 'content_filter':
                // 内容被过滤，需要修改输入
                return ['status' => 'filter', 'message' => $message];

            case 'length':
                // 长度限制，可能需要增加max_tokens
                return ['status' => 'length_limit', 'message' => $message];

            default:
                return ['status' => 'normal', 'message' => $message];
        }
    }
}
```

#### 2. 重试机制
```php
/**
 * 带重试的API调用
 */
public function callWithRetry($endpoint, $data, $maxRetries = 3)
{
    $retries = 0;
    
    while ($retries < $maxRetries) {
        try {
            return $this->makeRequest($endpoint, $data);
        } catch (Exception $e) {
            $retries++;
            
            if ($retries >= $maxRetries) {
                throw $e;
            }
            
            // 指数退避
            sleep(pow(2, $retries));
        }
    }
}
```

### 性能优化建议

#### 1. 请求优化
```php
// 批量处理多个请求
public function batchProcess($requests)
{
    $results = [];
    $batchSize = 5; // 并发限制
    
    $chunks = array_chunk($requests, $batchSize);
    
    foreach ($chunks as $chunk) {
        $promises = [];
        foreach ($chunk as $request) {
            $promises[] = $this->asyncRequest($request);
        }
        
        $results = array_merge($results, $this->waitForAll($promises));
    }
    
    return $results;
}
```

#### 2. 缓存策略
```php
/**
 * 带缓存的API调用
 */
public function cachedCompletion($messages, $options = [], $cacheTime = 3600)
{
    $cacheKey = 'deepseek_' . md5(json_encode($messages) . json_encode($options));
    
    $cached = Cache::get($cacheKey);
    if ($cached) {
        return $cached;
    }
    
    $result = $this->chatCompletion($messages, $options);
    Cache::put($cacheKey, $result, $cacheTime);
    
    return $result;
}
```

### 完整使用示例

#### 1. 剧情生成完整流程
```php
$deepseek = new DeepSeekAPI('your-api-key');

// 1. 检查账户余额
$balance = $deepseek->getUserBalance();
if (!$balance['is_available']) {
    throw new Exception('账户余额不足');
}

// 2. 获取可用模型列表
$models = $deepseek->listModels();
echo "可用模型: " . implode(', ', array_column($models['data'], 'id')) . "\n";

// 3. 生成剧情大纲 (使用推理模型)
$outlineMessages = [
    StoryPrompts::storyOutline('科幻', '人工智能觉醒', '短片'),
    ['role' => 'user', 'content' => '请创作一个10分钟的科幻短片剧情大纲']
];

$outline = $deepseek->chatCompletion($outlineMessages, [
    'model' => 'deepseek-reasoner',
    // 注意：推理模型不支持 temperature 参数
    'max_tokens' => 32000  // 推理模型默认32K，最大64K
]);

// 处理推理模型的特殊输出
$reasoningContent = $outline['choices'][0]['message']['reasoning_content'] ?? '';
$finalContent = $outline['choices'][0]['message']['content'];

echo "推理过程:\n" . $reasoningContent . "\n\n";
echo "最终答案:\n" . $finalContent . "\n\n";

// 4. 生成分镜脚本 (使用JSON输出)
$storyboardMessages = [
    ['role' => 'system', 'content' => '你是专业的分镜师，请以JSON格式输出分镜脚本'],
    // 重要：多轮对话时不要包含 reasoning_content
    ['role' => 'assistant', 'content' => $finalContent],
    ['role' => 'user', 'content' => '请将以上剧情转换为详细的分镜脚本']
];

$storyboard = $deepseek->jsonCompletion($storyboardMessages);

// 5. 使用Function Calling生成角色对话
$functions = [
    [
        'name' => 'generate_dialogue',
        'description' => '生成角色对话',
        'parameters' => [
            'type' => 'object',
            'properties' => [
                'character1' => ['type' => 'string', 'description' => '角色1名称'],
                'character2' => ['type' => 'string', 'description' => '角色2名称'],
                'scene' => ['type' => 'string', 'description' => '场景描述'],
                'emotion' => ['type' => 'string', 'description' => '情感基调']
            ],
            'required' => ['character1', 'character2', 'scene']
        ]
    ]
];

$dialogueMessages = [
    ['role' => 'user', 'content' => '为科幻短片生成主角与AI的对话场景']
];

$dialogueResponse = $deepseek->functionCalling($dialogueMessages, $functions);

// 6. 流式生成详细剧本
$scriptMessages = [
    ['role' => 'user', 'content' => '基于以上内容，请生成完整的剧本']
];

$deepseek->streamChatCompletion($scriptMessages, function($type, $content) {
    switch ($type) {
        case 'content':
            echo $content;
            break;
        case 'reasoning':
            echo "[推理] " . $content . "\n";
            break;
        case 'usage':
            echo "\n[使用统计] " . json_encode($content) . "\n";
            break;
    }
}, [
    'model' => 'deepseek-reasoner',
    'temperature' => 0.7
]);
```

#### 2. 代码补全示例 (FIM)
```php
// 使用FIM补全代码
$prompt = "function calculateTotal(items) {\n    let total = 0;\n    ";
$suffix = "\n    return total;\n}";

$completion = $deepseek->fimCompletion($prompt, [
    'suffix' => $suffix,
    'max_tokens' => 100,
    'temperature' => 0.2
]);

echo "补全的代码:\n" . $completion['choices'][0]['text'];
```

#### 3. 对话前缀续写示例
```php
// 前缀续写功能
$messages = [
    ['role' => 'user', 'content' => '请分析这个商业计划的可行性']
];

$prefixResponse = $deepseek->prefixCompletion($messages, "根据我的分析，这个商业计划", "首先我需要从市场、技术、财务三个维度来评估...");

echo "续写结果:\n" . $prefixResponse['choices'][0]['message']['content'];
```

### API限制和配额

#### 1. 请求频率限制
```php
class DeepSeekRateLimit
{
    const LIMITS = [
        'deepseek-chat' => [
            'rpm' => 60,        // 每分钟请求数
            'tpm' => 1000000,   // 每分钟token数
            'rpd' => 10000      // 每日请求数
        ],
        'deepseek-reasoner' => [
            'rpm' => 30,        // 推理模型限制更严格
            'tpm' => 500000,
            'rpd' => 5000
        ]
    ];

    public static function checkLimit($model, $requestCount, $tokenCount)
    {
        $limits = self::LIMITS[$model] ?? self::LIMITS['deepseek-chat'];

        if ($requestCount >= $limits['rpm']) {
            throw new Exception('超出每分钟请求限制');
        }

        if ($tokenCount >= $limits['tpm']) {
            throw new Exception('超出每分钟token限制');
        }

        return true;
    }
}
```

#### 2. 模型功能支持矩阵
```php
class DeepSeekFeatureMatrix
{
    const FEATURE_SUPPORT = [
        'deepseek-chat' => [
            'json_output' => true,
            'function_calling' => true,
            'fim_completion' => true,      // Beta功能
            'prefix_completion' => true,   // Beta功能
            'stream' => true,
            'temperature' => true,
            'top_p' => true,
            'logprobs' => true,
            'reasoning_content' => false
        ],
        'deepseek-reasoner' => [
            'json_output' => true,
            'function_calling' => true,
            'fim_completion' => false,     // 不支持
            'prefix_completion' => true,   // Beta功能，支持reasoning_content
            'stream' => true,
            'temperature' => false,        // 不支持
            'top_p' => false,             // 不支持
            'logprobs' => false,          // 不支持
            'reasoning_content' => true   // 特有功能
        ]
    ];

    public static function isSupported($model, $feature)
    {
        return self::FEATURE_SUPPORT[$model][$feature] ?? false;
    }

    public static function validateRequest($model, $params)
    {
        $unsupported = [];

        if ($model === 'deepseek-reasoner') {
            $restrictedParams = ['temperature', 'top_p', 'presence_penalty', 'frequency_penalty', 'logprobs', 'top_logprobs'];
            foreach ($restrictedParams as $param) {
                if (isset($params[$param])) {
                    $unsupported[] = $param;
                }
            }
        }

        if (!empty($unsupported)) {
            throw new Exception("模型 {$model} 不支持参数: " . implode(', ', $unsupported));
        }

        return true;
    }
}
```

#### 2. 错误码说明
```php
class DeepSeekErrorCodes
{
    const ERROR_CODES = [
        400 => '请求格式错误',
        401 => 'API密钥无效或过期',
        403 => '权限不足或余额不足',
        404 => '请求的资源不存在',
        429 => '请求频率超限',
        500 => '服务器内部错误',
        503 => '服务暂时不可用',
        1001 => '模型不存在',
        1002 => '输入内容过长',
        1003 => '输入内容包含敏感信息',
        1004 => '上下文缓存未命中'
    ];

    public static function getErrorMessage($code)
    {
        return self::ERROR_CODES[$code] ?? '未知错误';
    }
}
```

### 最佳实践

#### 1. 提示工程
- **角色设定**: 明确定义AI的角色和专业领域
- **上下文提供**: 提供充足的背景信息和约束条件
- **格式要求**: 明确指定输出格式和结构要求
- **示例引导**: 使用few-shot示例提高输出质量

#### 2. 参数调优
- **deepseek-chat 参数设置**:
  - 创意写作: temperature 0.7-0.9
  - 逻辑推理: temperature 0.1-0.3
  - 代码生成: temperature 0.2-0.4
  - 翻译任务: temperature 0.3-0.5
- **deepseek-reasoner 注意事项**:
  - 不支持 temperature、top_p 等采样参数
  - max_tokens 默认32K，最大64K（包含思维链）
  - 输出包含 reasoning_content 和 content 两部分
- **Max_tokens控制**: 根据任务复杂度合理设置
- **Top_p调节**: 仅适用于 deepseek-chat 模型

#### 3. 成本控制
- **缓存策略**: 对相似请求实施缓存，利用上下文缓存减少费用
- **错峰使用**: 优先在优惠时段（00:30-08:30）使用API
- **批量处理**: 合并多个小请求
- **Token优化**: 精简提示词，避免冗余
- **模型选择**: 根据任务复杂度选择合适模型
  - 简单对话: deepseek-chat (更便宜)
  - 复杂推理: deepseek-reasoner (更准确但更贵)

#### 4. 性能优化
- **并发控制**: 合理设置并发请求数量
- **重试机制**: 实施指数退避重试策略，特别处理 insufficient_system_resource 错误
- **连接池**: 复用HTTP连接减少开销
- **监控告警**: 实时监控API使用情况和余额

#### 5. 安全考虑
- **密钥管理**: 使用环境变量存储API密钥
- **输入验证**: 过滤和验证用户输入
- **输出检查**: 检测和过滤敏感输出内容，注意 content_filter 状态
- **访问控制**: 实施IP白名单和访问频率限制

#### 6. 推理模型特殊处理
- **多轮对话**: 不要在后续请求中包含 reasoning_content
- **思维链展示**: 可以向用户展示推理过程以提高透明度
- **错误处理**: 推理模型可能因资源限制被打断，需要实现重试机制

#### 6. 上下文缓存优化
```php
// 利用上下文缓存减少成本
$messages = [
    ['role' => 'system', 'content' => '长篇系统提示...'], // 会被缓存
    ['role' => 'user', 'content' => '用户问题']
];

// 后续请求可以复用缓存的系统提示
$response = $deepseek->chatCompletion($messages, [
    'model' => 'deepseek-chat',
    'temperature' => 0.7
]);

// 检查缓存命中情况
$cacheHit = $response['usage']['prompt_cache_hit_tokens'];
$cacheMiss = $response['usage']['prompt_cache_miss_tokens'];
echo "缓存命中: {$cacheHit} tokens, 未命中: {$cacheMiss} tokens\n";
```

## 总结

DeepSeek API为剧情创作和分镜生成提供了强大的AI能力支持，通过合理的配置和优化，可以大大提升内容创作的效率和质量。

### 核心优势
- **双模型支持**: deepseek-chat 适合通用对话，deepseek-reasoner 适合复杂推理
- **错峰优惠**: 优惠时段价格低至2.5折，大幅降低使用成本
- **丰富功能**: 支持JSON输出、Function Calling、流式响应等高级功能
- **Beta功能**: FIM补全和对话前缀续写等创新功能

### 使用建议
1. **根据任务选择模型**: 简单任务用 deepseek-chat，复杂推理用 deepseek-reasoner
2. **合理利用错峰时段**: 在00:30-08:30使用API可节省大量成本
3. **充分利用上下文缓存**: 重复使用相同的系统提示可大幅减少费用
4. **注意模型限制**: 推理模型不支持某些参数，需要特殊处理

### 版本信息
- **文档版本**: 基于官方文档 2025年1月最新版本
- **模型版本**: deepseek-chat (DeepSeek-V3-0324), deepseek-reasoner (DeepSeek-R1-0528)
- **最后更新**: 2025年1月（价格和功能信息）

推理模型特别适合复杂的逻辑分析和创意构思任务，其思维链功能为AI决策过程提供了前所未有的透明度。
