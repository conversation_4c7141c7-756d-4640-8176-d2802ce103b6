<?php

namespace App\Services\PyApi;

use App\Jobs\ProcessTextGeneration;
use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\WebSocketSession;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Services\Common\TransactionManager;

/**
 * AI生成服务
 */
class AiGenerationService
{
    protected $aiModelService;
    protected $pointsService;
    protected $characterService;
    protected $styleService;
    protected $projectService;

    public function __construct(
        AiModelService $aiModelService,
        PointsService $pointsService,
        CharacterService $characterService,
        StyleService $styleService,
        ProjectService $projectService
    ) {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
        $this->characterService = $characterService;
        $this->styleService = $styleService;
        $this->projectService = $projectService;
    }

    // 创建异步任务（在这里执行异步任务前冻结积分）
    public function generateTextWithWebSocket($request, $userId)
    {
        try {
            // 验证WebSocket会话ID
            $webSocketSessionId = $request->get('websocket_session_id');
            $webSocketService = app(WebSocketService::class);
            $sessionValidResult = $webSocketService->validateSession($webSocketSessionId, $userId);
            if ($sessionValidResult['code'] !== ApiCodeEnum::SUCCESS)
            {
                return [
                    'code' => $sessionValidResult['code'],
                    'message' => $sessionValidResult['message'],
                    'data' => $sessionValidResult['data']
                ];
            }

            // 异步任务ID定义
            $taskId = 'text_gen_' . time() . '_' . Str::random(8);

            // 🔧 修复：直接从指定的WebSocket会话ID获取业务类型，而不是查询最新的活跃会话
            // 从指定的WebSocket会话中获取business_type，用于确定AI生成的具体业务场景
            $businessTypeResult = $this->getBusinessTypeFromSpecificWebSocketSession($webSocketSessionId, $userId, $taskId);
            if ($businessTypeResult['code'] !== ApiCodeEnum::SUCCESS) {
                TransactionManager::rollback('ProcessTextGeneration.handle', $businessTypeResult['message']);
                throw new \Exception($businessTypeResult['message']);
            }
            $businessType = $businessTypeResult['data']['business_type'] ?? null;

            //{"info_id":"信息ID：3,4(必填)","storyboard_id": "分镜ID(生图提示词扩写必填)","aspect_ratio": "规格(WebSocket连接认证 business_type=aistory 或 mystory 时必传：16:9,4:3,9:16,3:4)","style_id": "风格(WebSocket连接认证 business_type=aistory 或 mystory 时必传：风格库的风格ID)"}

            // 表单数据
            $toolData = [
                // 纯文本生成
                'text_generation'            => [],
                // 分镜提示词扩写
                'text_generation_prompt'     => [],
                // 角色提示词扩写
                'text_prompt_character'      => [],
                // 风格提示词扩写
                'text_prompt_style'          => [],
                // 故事扩写
                'text_generation_story'      => [],
                // 智能故事分镜
                'text_generation_aistory'    => [],
                // 自有故事分镜
                'text_generation_mystory'    => []
            ];

            // 项目数据转数组
            $toolData[$businessType] = json_decode($request->get('tool_data'), true);

            // 验证必需的参数
            if (!isset($toolData[$businessType]['info_id'])) 
            {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目数据中缺少KEY=info_id的参数',
                    'data' => []
                ];
            }
            else
            {
                // AI故事分镜："business_type": "aistory"
                // MY故事分镜："business_type": "mystory"
                // 分镜提示词："business_type": "prompt"
                // 故事扩写："business_type": "story"
                // 风格提示词："business_type": "promptstyle"
                // 角色提示词："business_type": "promptcharacter"
                // business_type = promptcharacter 验证的是 character_library表
                // business_type = promptstyle 验证的是 stye_library 表

                file_put_contents('D:/longtool/phpStudy_64/WWW/tool_api/php/api/businessType.txt',$businessType);

                // 根据不同的 business_type 验证不同的表
                if ($toolData[$businessType]['info_id']) {
                    $validationResult = $this->validateInfoIdByBusinessType(
                        $businessType,
                        $toolData[$businessType]['info_id'],
                        $userId
                    );

                    if ($validationResult['code'] !== ApiCodeEnum::SUCCESS) {
                        return $validationResult;
                    }
                }
            }

            // 🎯 积分预检查需要的生成参数
            $generationParams = [
                'max_tokens' => $request->get('max_tokens', 1000),
                'temperature' => $request->get('temperature', 0.7),
                'top_p' => $request->get('top_p', 0.9)
            ];

            // 🎯 调用服务层进行积分预检查
            $pointsCheckResult = $this->checkPointsForWebSocketTextGeneration(
                $userId,
                $request->get('prompt'),
                $request->get('model_id'),
                $generationParams
            );

            // 积分检查失败，直接返回错误，不创建异步任务
            if ($pointsCheckResult['code'] !== ApiCodeEnum::SUCCESS) 
            {
                return [
                    'code' => $pointsCheckResult['code'],
                    'message' => $pointsCheckResult['message'],
                    'data' => $pointsCheckResult['data'] ?? []
                ];
            }

            // 所需积分
            $estimatedCost = $pointsCheckResult['data']['estimated_cost'];

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'text_generation',
                null,
                300 // 5分钟超时
            );

            // 积分冻结失败时，回滚事务并返回错误，阻止任务创建
            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                Log::warning('积分冻结失败，阻止任务创建', [
                    'user_id' => $userId,
                    'estimated_cost' => $estimatedCost,
                    'freeze_result' => $freezeResult
                ]);
                return $freezeResult;
            }

            // 积分充足，创建异步任务
            $prompt = $request->get('prompt');
            $modelId = $request->get('model_id');

            // 🎯 获取积分交易ID（consumePoints需要的关键参数）
            // 来源：PointsService.freezePoints() 返回的 transaction_id
            // 用途：后续消费积分时通过此ID找到对应的冻结记录
            $transactionId = $freezeResult['data']['transaction_id'] ?? null;

            // 上下文（设置AI扮演角色的作用）
            $context = $request->get('context', 'prompt_edit');

            // 预算积分
            $generationParams['estimated_cost'] = $estimatedCost;

            // 🔍 调试日志：检查参数传递
            Log::info('WebSocket文本生成 - 参数传递检查', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'estimated_cost' => $estimatedCost,
                'generation_params' => $generationParams,
                'generation_params_keys' => array_keys($generationParams),
                'generation_params_count' => count($generationParams)
            ]);

            // 写入AI生成任务表
            // 🎯 关键改动：在事务外创建任务记录，确保失败时也能保留

            // 获取模型配置
            if ($modelId) {
                $model = AiModelConfig::active()->find($modelId);
                if (!$model) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '指定的模型不存在',
                        'data' => []
                    ];
                }
            } else {
                $modelResult = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
                if ($modelResult['code'] !== ApiCodeEnum::SUCCESS) {
                    return $modelResult;
                }
                $model = $modelResult['data']['model'];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '模型服务当前不可用',
                    'data' => []
                ];
            }

            // 构建项目数据
            $infoData = [];
            $infoData['storyboard_id'] = $toolData[$businessType]['storyboard_id'] ?? null;
            $infoData['aspect_ratio'] = $toolData[$businessType]['aspect_ratio'] ?? null;
            $infoData['style_id'] = $toolData[$businessType]['style_id'] ?? null;

            // 创建生成任务记录（在事务外，不受回滚影响）
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'info_id' => $toolData[$businessType]['info_id'],
                'info_data' => $infoData,
                'model_config_id' => $model->id,
                'task_type' => $businessType,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'max_tokens' => $generationParams['max_tokens'] ?? 1000,
                    'temperature' => $generationParams['temperature'] ?? 0.7,
                    'top_p' => $generationParams['top_p'] ?? 0.9
                ],
                'generation_params' => $generationParams,
                'external_task_id' => $taskId, // 使用异步任务ID作为外部任务ID
                'transaction_id' => $transactionId,
                'cost' => $generationParams['estimated_cost']
            ]);

            // 记录任务创建成功
            Log::info('AI生成任务记录创建成功', [
                'task_record_id' => $task->id,
                'external_task_id' => $taskId,
                'user_id' => $userId,
                'status' => 'pending',
                'created_outside_transaction' => true
            ]);

            // 创建异步任务（dispatch()）在 generateTextWithWebSocket 方法中写入 jobs 表
            // ↓
            // ProcessTextGeneration.handle() 执行异步任务
            // ↓
            // AiGenerationService.generateText() 执行AI生成
            // ↓
            // AiServiceClient.callWithUserChoice() 调用AI平台
            // ↓
            // 生成成功：WebSocketEventService.pushAiGenerationCompleted() 推送完成事件 + 消费积分 + 更新AI生成任务表状态为completed
            // 生成失败分支：
            // ├─ 业务错误：pushAiGenerationFailed() + 返还积分 + 任务正常结束（不进入failed_jobs）+ 更新AI生成任务表状态为failed
            // └─ 系统错误：抛出异常触发重试，重试耗尽后进入failed_jobs表并调用failed()方法 + 更新AI生成任务表状态为failed
            //    ↓
            //    failed_jobs表记录删除：手动执行 queue:forget {id} 或 queue:flush 或 queue:retry {id}成功后自动删除
            // ↓
            // system_events 表（所有失败都通过publishFailureEvent()发布事件，用于监控统计）
            dispatch(new ProcessTextGeneration(
                $userId,                    // 用户ID
                $prompt,                    // 用户输入的提示词
                $modelId,                   // AI模型配置ID
                $toolData,                  // 工具数据（包含信息ID、分镜ID等业务参数）
                $generationParams,          // AI生成参数（温度、最大令牌数等）
                $businessType,              // 业务类型（php\api\config\ai.php 中 映射后的类型）
                $taskId,                    // 外部任务ID（用于WebSocket通信）
                $task->id,                  // 任务记录ID（p_ai_generation_tasks表主键）
                $context                    // 上下文（prompt_edit等，传到ProcessTextGeneration但未传到generateText）
            ));

            // 返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'context' => $context,
                'estimated_cost' => $estimatedCost,
                'prompt' => substr($prompt, 0, 100),
                'generation_params' => $generationParams,
                'websocket_session_id' => $webSocketSessionId,
                'timestamp' => Carbon::now()->format('c'),
                'request_id' => 'req_' . Str::random(16)
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务创建成功',
                'data' => $responseData
            ];
        } 
        catch (\Exception $e)
        {
            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($request->get('prompt'), 0, 100),
                'model_id' => $request->get('model_id'),
                'info_id' => $request->get('info_id')
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 生成文本
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 业务流程：
     * 1. 解析提示词中的项目参数（比例、风格）
     * 2. 根据参数创建或关联项目
     * 3. 获取或创建AI模型配置
     * 4. 预扣积分并创建生成任务
     * 5. 此服务只支持WebSocket异步执行文本生成
     *
     * @param int $userId 用户ID
     * @param string $prompt 生成提示词（可能包含JSON格式的项目参数）
     * @param int|null $modelId AI模型配置ID，为空时使用默认模型
     * @param int|null $infoId 信息ID，为空时根据提示词参数创建项目
     * @param array $generationParams 生成参数（temperature、max_tokens等）
     * @param string|null $businessType 业务类型（php\api\config\ai.php business_type_mappings 中的后端任务类型，映射后的类型）
     *
     * @return array 返回结果数组
     *   - code: 状态码（ApiCodeEnum）
     *   - message: 提示信息
     *   - data: 任务数据
     *     - task_id: 任务ID
     *     - status: 任务状态
     *     - estimated_time: 预估完成时间
     *     - info_id: 信息ID（如果有）
     *
     * @throws \Exception 当业务逻辑异常时抛出 
     */
    public function generateText(
        int $userId,
        string $prompt,
        int $modelId,
        array $toolData = [],
        array $generationParams = [],
        string $businessType, // php\api\config\ai.php 中 映射后的类型
        string $externalTaskId,
        ?int $taskRecordId = null // 任务记录ID
        ): array
    {
        // 信息ID
        $infoId = $toolData[$businessType]['info_id'];

        try {
            TransactionManager::begin('AiGenerationService.generateText');

            // 调试日志：记录开始时的事务状态
            Log::debug('AiGenerationService.generateText - 事务开始', [
                'user_id' => $userId,
                'external_task_id' => $externalTaskId,
                'transaction_level' => TransactionManager::getLevel(),
                'in_transaction' => TransactionManager::inTransaction(),
                'nesting_info' => TransactionManager::getNestingInfo(),
                'transaction_stack' => TransactionManager::getTransactionStack()
            ]);


            // 确定任务类型：优先使用传递的businessType，否则使用默认值
            $taskType = $businessType ?: AiGenerationTask::TYPE_TEXT_GENERATION;

            // 信息ID
            if($businessType == 'text_generation_aistory' || $businessType == 'text_generation_mystory' || $businessType == 'text_generation_prompt' || $businessType == 'text_prompt_character' || $businessType == 'text_prompt_style')
            {
                $infoId = $toolData[$businessType]['info_id'];
            }

            // 生成分镜必传参数（在 handleStoryboardGeneration 有判断参数的缺失的业务流程）
            if($businessType == 'text_generation_aistory' || $businessType == 'text_generation_mystory')
            {
                // 尺寸
                $aspectRatio = $toolData[$businessType]['aspect_ratio'];
                // 风格库的风格ID
                $styleId = $toolData[$businessType]['style_id'];
            }

            // 生图提示词扩写必传参数（在 handlePromptGeneration 有判断参数的缺失的业务流程）
            if($businessType == 'text_generation_prompt')
            {
                // 分镜ID
                $storyboardId = $toolData[$businessType]['storyboard_id'];
            }

            // 获取模型配置
            if ($modelId)
            {
                $model = AiModelConfig::active()->find($modelId);
                if (!$model) {
                    TransactionManager::rollback('AiGenerationService.generateText', '指定的模型不存在');
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '指定的模型不存在',
                        'data' => []
                    ];
                }
            }
            else
            {
                $modelResult = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
                if ($modelResult['code'] !== ApiCodeEnum::SUCCESS) {
                    TransactionManager::rollback('AiGenerationService.generateText', $modelResult['message']);
                    return $modelResult;
                }
                $model = $modelResult['data']['model'];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                TransactionManager::rollback('AiGenerationService.generateText', '模型服务当前不可用');
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '模型服务当前不可用',
                    'data' => []
                ];
            }

            // 🔍 调试日志：检查generateText方法中的参数
            Log::info('generateText方法 - 参数检查', [
                'user_id' => $userId,
                'external_task_id' => $externalTaskId,
                'generation_params' => $generationParams,
                'generation_params_keys' => array_keys($generationParams),
                'generation_params_count' => count($generationParams),
                'has_estimated_cost' => isset($generationParams['estimated_cost']),
                'estimated_cost_value' => $generationParams['estimated_cost'] ?? 'NOT_SET'
            ]);

            // 验证必需的参数
            if (!isset($generationParams['estimated_cost'])) {
            
                $error_context = [
                    'user_id' => $userId,
                    'external_task_id' => $externalTaskId,
                    'generation_params_keys' => array_keys($generationParams),
                    'generation_params_count' => count($generationParams)
                ];

                Log::error('generateText方法 - estimated_cost参数缺失', [
                    'method' => __METHOD__,
                    'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                    'error' => 'estimated_cost参数是必需的，但在generationParams中未找到'
                ]);

                TransactionManager::rollback('AiGenerationService.generateText', 'estimated_cost参数缺失');

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'estimated_cost参数是必需的，但在generationParams中未找到',
                    'data' => [
                        'missing_param' => 'estimated_cost',
                        'available_params' => array_keys($generationParams)
                    ]
                ];
            }

            // 🔧 获取已存在的任务记录（在 generateTextWithWebSocket 中创建）
            if ($taskRecordId) {
                $task = AiGenerationTask::find($taskRecordId);
                if (!$task) {
                    // ⚠️ 任务记录不存在时无法更新状态，直接回滚
                    TransactionManager::rollback('AiGenerationService.generateText', '任务记录不存在');
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '任务记录不存在',
                        'data' => []
                    ];
                }

                // 更新任务状态为处理中
                $task->update([
                    'status' => AiGenerationTask::STATUS_PROCESSING,
                    'started_at' => Carbon::now()->format('c')
                ]);

                Log::info('任务状态更新为处理中', [
                    'task_record_id' => $task->id,
                    'external_task_id' => $externalTaskId,
                    'status' => 'processing'
                ]);
            }
            else 
            {
                // 🚨 不再支持旧的调用方式，必须传递任务记录ID
                TransactionManager::rollback('AiGenerationService.generateText', '缺少任务记录ID');
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '系统错误：缺少任务记录ID，请通过generateTextWithWebSocket方法创建任务',
                    'data' => [
                        'error_type' => 'missing_task_record_id',
                        'required_parameter' => 'taskRecordId',
                        'suggestion' => '请使用generateTextWithWebSocket方法创建任务后再调用此方法'
                    ]
                ];
            }

            // 执行生成任务
            $aiTextData = $this->executeTextGeneration($task);

            // 检查生成任务是否成功，如果失败则先回滚再更新任务状态
            if ($aiTextData['code'] !== ApiCodeEnum::SUCCESS) {
                // 🔧 关键改动：先回滚再更新任务状态（避免状态更新被回滚）
                TransactionManager::rollback('AiGenerationService.generateText', $aiTextData['message']);

                // 回滚后更新任务状态（在事务外执行，确保不被回滚影响）
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'completed_at' => Carbon::now()->format('c'),
                    'error_message' => $aiTextData['message']
                ]);

                Log::info('AI生成失败，事务回滚后任务状态已更新', [
                    'task_record_id' => $task->id,
                    'external_task_id' => $externalTaskId,
                    'status' => 'failed',
                    'error_message' => $aiTextData['message']
                ]);

                return $aiTextData;
            }

            // 项目更新、分镜入库、生图提示词修改
            if(isset($aiTextData['data']['result']['text']) &&
               !empty(trim($aiTextData['data']['result']['text'])))
            {
                // 根据任务类型调用相应的处理方法
                $result = null;

                // 1、项目分镜生成
                if($taskType == 'text_generation_aistory' || $taskType == 'text_generation_mystory')
                {
                    $result = $this->handleStoryboardGeneration($userId, $infoId, $aspectRatio, $styleId, $aiTextData, $taskType);
                }

                // 2、项目故事更新
                elseif($taskType == 'text_generation_story')
                {
                    $result = $this->handleStoryGeneration($userId, $infoId, $aiTextData, $taskType);
                }

                // 3、分镜图片提示词更新
                elseif($taskType == 'text_generation_prompt')
                {
                    $result = $this->handlePromptGeneration($userId, $storyboardId, $aiTextData, $taskType);
                }

                // 4、角色图片提示词更新
                elseif($taskType == 'text_prompt_character')
                {
                    $result = $this->handleCharactertGeneration($userId, $infoId, $aiTextData, $taskType);
                }

                // 5、风格图片提示词更新
                elseif($taskType == 'text_prompt_style')
                {
                    $result = $this->handleStyleGeneration($userId, $infoId, $aiTextData, $taskType);
                }

                // 检查处理结果，如果失败则直接返回
                if($result && $result['code'] !== ApiCodeEnum::SUCCESS)
                {
                    // 🔧 业务处理失败时，先回滚再更新任务状态（避免状态更新被回滚）
                    TransactionManager::rollback('AiGenerationService.generateText', $result['message']);

                    // 回滚后更新任务状态（在事务外执行，确保不被回滚影响）
                    $task->update([
                        'status' => AiGenerationTask::STATUS_FAILED,
                        'completed_at' => Carbon::now()->format('c'),
                        'error_message' => $result['message']
                    ]);

                    Log::info('业务处理失败，事务回滚后任务状态已更新', [
                        'task_record_id' => $task->id,
                        'external_task_id' => $externalTaskId,
                        'status' => 'failed',
                        'error_message' => $result['message']
                    ]);

                    return $result;
                }
            }
            else
            {
                // AI文本生成失败的处理
                Log::error('AI文本生成失败，跳过后续处理', [
                    'user_id' => $userId,
                    'task_id' => $task->id,
                    'ai_response_code' => $aiTextData['code'] ?? 'unknown',
                    'ai_response_message' => $aiTextData['message'] ?? 'unknown',
                    'has_text_content' => isset($aiTextData['data']['result']['text']) && !empty(trim($aiTextData['data']['result']['text']))
                ]);

                // 🔧 AI生成失败时，先回滚再更新任务状态（避免状态更新被回滚）
                // 如果AI生成失败，直接返回失败结果，不执行后续的项目更新逻辑
                TransactionManager::rollback('AiGenerationService.generateText', 'AI文本生成失败');

                // 回滚后更新任务状态（在事务外执行，确保不被回滚影响）
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'completed_at' => Carbon::now()->format('c'),
                    'error_message' => 'AI文本生成失败：无有效文本内容'
                ]);

                Log::info('AI生成失败，事务回滚后任务状态已更新', [
                    'task_record_id' => $task->id,
                    'external_task_id' => $externalTaskId,
                    'status' => 'failed',
                    'error_message' => 'AI文本生成失败：无有效文本内容'
                ]);

                return $aiTextData;
            }

            Log::info('文本生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'model_id' => $model->id,
                'estimated_cost' => $generationParams['estimated_cost']
            ]);

            // 调试日志：检查提交前的事务状态
            Log::debug('AiGenerationService.generateText - 准备提交事务', [
                'user_id' => $userId,
                'external_task_id' => $externalTaskId,
                'transaction_level' => TransactionManager::getLevel(),
                'in_transaction' => TransactionManager::inTransaction(),
                'nesting_info' => TransactionManager::getNestingInfo(),
                'transaction_stack' => TransactionManager::getTransactionStack()
            ]);

            // 🎉 成功时更新任务状态为 completed
            $task->update([
                'status' => AiGenerationTask::STATUS_COMPLETED,
                'completed_at' => Carbon::now()->format('c'),
                'output_data' => $aiTextData['data'] ?? null
            ]);

            Log::info('任务状态更新为完成', [
                'task_record_id' => $task->id,
                'external_task_id' => $externalTaskId,
                'status' => 'completed'
            ]);

            TransactionManager::commit('AiGenerationService.generateText');

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务已完成',
                'data' => [
                    'task_id' => $task->id,
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'estimated_cost' => $generationParams['estimated_cost'],
                    'model_name' => $model->model_name ?? '',
                    'platform' => $model->platform ?? '',
                    'result' => $aiTextData['data'] ?? null
                ]
            ];
        }
        catch (\Exception $e)
        {
            // 🔧 异常时先回滚再更新任务状态（避免状态更新被回滚）
            TransactionManager::rollback('AiGenerationService.generateText', $e->getMessage());

            // 回滚后更新任务状态（在事务外执行，确保不被回滚影响）
            if (isset($task) && $task) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'completed_at' => Carbon::now()->format('c'),
                    'error_message' => $e->getMessage()
                ]);

                Log::info('异常处理，事务回滚后任务状态已更新', [
                    'task_record_id' => $task->id,
                    'external_task_id' => $externalTaskId ?? 'unknown',
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ]);
            }

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'model_id' => $modelId,
                'info_id' => $infoId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取任务状态
     */
    public function getTaskStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'input_data' => $task->input_data,
                    'output_data' => $task->output_data,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'error_message' => $task->error_message,
                    'retry_count' => $task->retry_count,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $task->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取任务状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户任务列表
     */
    public function getUserTasks(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = AiGenerationTask::byUser($userId);
            
            // 应用筛选条件
            if (!empty($filters['task_type'])) {
                $query->byType($filters['task_type']);
            }
            
            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }
            
            if (!empty($filters['platform'])) {
                $query->byPlatform($filters['platform']);
            }
            
            $tasks = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $tasksData = $tasks->map(function ($task) {
                return [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'tasks' => $tasksData,
                    'pagination' => [
                        'current_page' => $tasks->currentPage(),
                        'total' => $tasks->total(),
                        'per_page' => $tasks->perPage(),
                        'last_page' => $tasks->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取用户任务列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 手动重试失败的AI生成任务
     *
     * 🎯 适用场景：
     * - 自动重试耗尽后的人工干预
     * - 业务错误修复后的手动重试
     * - 管理员主动重试特定任务
     *
     * 🔄 与自动重试的区别：
     * - 自动重试：Laravel队列系统，使用jobs表参数
     * - 手动重试：用户触发，使用p_ai_generation_tasks表数据
     *
     * 📋 重试流程：
     * 1. 验证任务存在性和重试资格
     * 2. 增加重试计数
     * 3. 重置任务状态为processing
     * 4. 重新分发到队列系统执行
     * 5. 返回重试结果
     */
    public function retryTask(int $taskId, int $userId): array
    {
        try {
            TransactionManager::begin('AiGenerationService.retryTask');

            // 🔍 步骤1：查找并验证任务
            $task = AiGenerationTask::byUser($userId)->find($taskId);

            if (!$task) {
                TransactionManager::rollback('AiGenerationService.retryTask', '任务不存在');
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            // 🔍 步骤2：检查重试资格
            if (!$task->canRetry()) {
                TransactionManager::rollback('AiGenerationService.retryTask', '任务无法重试');
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '任务无法重试（状态：' . $task->status . '，重试次数：' . $task->retry_count . '/' . $task->max_retries . '）',
                    'data' => [
                        'task_status' => $task->status,
                        'retry_count' => $task->retry_count,
                        'max_retries' => $task->max_retries
                    ]
                ];
            }

            // 🔍 步骤3：增加重试计数并重置状态
            $task->incrementRetry();

            Log::info('开始手动重试任务', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'task_type' => $task->task_type,
                'retry_count' => $task->retry_count,
                'max_retries' => $task->max_retries,
                'original_external_task_id' => $task->external_task_id
            ]);

            // 🔍 步骤4：根据任务类型判断是否支持重试
            $textGenerationTypes = [
                AiGenerationTask::TYPE_TEXT_GENERATION,
                AiGenerationTask::TYPE_TEXT_GENERATION_PROMPT,
                AiGenerationTask::TYPE_TEXT_GENERATION_STORY,
                AiGenerationTask::TYPE_TEXT_GENERATION_AISTORY,
                AiGenerationTask::TYPE_TEXT_GENERATION_MYSTORY,
                AiGenerationTask::TYPE_CHARACTER_GENERATION
            ];

            if (!in_array($task->task_type, $textGenerationTypes)) {
                TransactionManager::rollback('AiGenerationService.retryTask', '不支持的任务类型');
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => "暂不支持重试任务类型: {$task->task_type}",
                    'data' => [
                        'task_type' => $task->task_type,
                        'supported_types' => $textGenerationTypes
                    ]
                ];
            }

            // 🔍 步骤5：重新分发任务到队列系统
            // 生成新的外部任务ID用于追踪
            $newExternalTaskId = 'retry_' . time() . '_' . Str::random(8);

            // 从任务记录中重建参数
            $toolData = [];
            if ($task->info_data) {
                $businessType = $task->task_type;
                $toolData[$businessType] = array_merge(
                    ['info_id' => $task->info_id],
                    $task->info_data
                );
            }

            // 重新分发到队列
            dispatch(new ProcessTextGeneration(
                $task->user_id,                     // 用户ID
                $task->getInputData('prompt', ''),  // 用户输入的提示词
                $task->model_config_id,             // AI模型配置ID
                $toolData,                          // 工具数据（包含信息ID、分镜ID等业务参数）
                $task->generation_params ?? [],     // AI生成参数（温度、最大令牌数等）
                $task->task_type,                   // 业务类型（php\api\config\ai.php 中 映射后的类型）
                $newExternalTaskId,                 // 外部任务ID（用于WebSocket通信）
                $task->id,                          // 任务记录ID（p_ai_generation_tasks表主键）
                'manual_retry'                      // 上下文（prompt_edit等，传到ProcessTextGeneration但未传到generateText）
            ));

            // 🔍 步骤6：更新任务的外部ID和状态
            $task->update([
                'external_task_id' => $newExternalTaskId,
                'status' => AiGenerationTask::STATUS_PENDING,
                'error_message' => null,
                'started_at' => null,
                'completed_at' => null
            ]);

            TransactionManager::commit('AiGenerationService.retryTask');

            Log::info('任务手动重试成功分发', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'new_external_task_id' => $newExternalTaskId,
                'retry_count' => $task->retry_count,
                'queue_dispatched' => true
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务重试成功，已重新加入队列',
                'data' => [
                    'task_id' => $task->id,
                    'external_task_id' => $newExternalTaskId,
                    'status' => $task->status,
                    'retry_count' => $task->retry_count,
                    'max_retries' => $task->max_retries,
                    'context' => 'manual_retry'
                ]
            ];

        } catch (\Exception $e) {
            TransactionManager::rollback('AiGenerationService.retryTask', $e->getMessage());

            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('任务手动重试失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务重试失败: ' . $e->getMessage(),
                'data' => [
                    'error_type' => get_class($e),
                    'error_message' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 执行文本生成（调用AI服务）
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function executeTextGeneration(AiGenerationTask $task): array
    {
        try {
            TransactionManager::begin('AiGenerationService.executeTextGeneration');

            Log::info('🔍 开始执行文本生成任务', [
                'task_id' => $task->id,
                'user_id' => $task->user_id,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'task_type' => $task->task_type,
                'external_task_id' => $task->external_task_id
            ]);

            // 获取提示词模板拼接在 $prompt 后面 $taskType 
            // $taskType = storyboard 时 获取 prompt_temp.storyboard.[aistory 或 mystory] 
            $prompTemp = config('ai.prompt_temp.' .$task->task_type);

            $task->start();

            $requestData = [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->getInputData('prompt') . PHP_EOL . $prompTemp
                    ]
                ],
                'max_tokens' => $task->getInputData('max_tokens', 1000),
                'temperature' => $task->getInputData('temperature', 0.7),
                'top_p' => $task->getInputData('top_p', 0.9)
            ];

            Log::info('🔍 准备调用AI服务', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'task_type' => $task->task_type,
                'request_data' => [
                    'model' => $requestData['model'],
                    'max_tokens' => $requestData['max_tokens'],
                    'temperature' => $requestData['temperature'],
                    'top_p' => $requestData['top_p'],
                    'prompt_length' => strlen($requestData['messages'][0]['content'])
                ],
                'ai_service_url' => 'https://aiapi.tiptop.cn/'
            ]);

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $task->task_type,
                $requestData,
                $task->user_id
            );

            Log::info('🔍 AI服务调用完成', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'response_success' => $response['success'] ?? false,
                'response_status' => $response['status'] ?? 'unknown',
                'response_mode' => $response['mode'] ?? 'unknown',
                'has_data' => isset($response['data']),
                'has_error' => isset($response['error'])
            ]);

            if ($response['success']) {
                Log::info('🔍 AI服务调用成功，开始解析响应', [
                    'task_id' => $task->id,
                    'response_data_keys' => array_keys($response['data'] ?? []),
                    'response_data_type' => gettype($response['data'] ?? null)
                ]);

                $aiResponse = $response['data'];

                // 解析AI服务响应
                $generatedText = $aiResponse['choices'][0]['message']['content'] ?? '';
                $tokensUsed = $aiResponse['usage']['total_tokens'] ?? 0;

                Log::info('🔍 AI响应解析完成', [
                    'task_id' => $task->id,
                    'generated_text_length' => strlen($generatedText),
                    'tokens_used' => $tokensUsed,
                    'finish_reason' => $aiResponse['choices'][0]['finish_reason'] ?? 'unknown',
                    'model' => $aiResponse['model'] ?? 'unknown'
                ]);

                $outputData = [
                    'text' => $generatedText,
                    'finish_reason' => $aiResponse['choices'][0]['finish_reason'] ?? 'stop',
                    'model' => $aiResponse['model'] ?? $task->model_name,
                    'usage' => $aiResponse['usage'] ?? []
                ];

                Log::info('🔍 开始完成任务并消费积分', [
                    'task_id' => $task->id,
                    'output_data_keys' => array_keys($outputData),
                    'tokens_used' => $tokensUsed
                ]);

                // 完成任务并检查结果
                $completeResult = $task->complete($outputData, $tokensUsed);
                if ($completeResult['code'] !== ApiCodeEnum::SUCCESS) {
                    // 任务完成失败，回滚事务并返回错误
                    TransactionManager::rollback('AiGenerationService.executeTextGeneration', $completeResult['message']);
                    return [
                        'code' => $completeResult['code'],
                        'message' => '任务完成失败: ' . $completeResult['message'],
                        'data' => []
                    ];
                }

                Log::info('🔍 任务完成', [
                    'task_id' => $task->id,
                    'task_status' => $task->status,
                    'processing_time_ms' => $task->processing_time_ms
                ]);
                // 返回数据
                $responseData = [
                    'task_id' => $task->id,                                    // 任务ID
                    'status' => 'completed',                                   // 任务状态：已完成
                    'result' => [                                              // 生成结果
                        'text' => $generatedText,                             // AI生成的文本内容
                        'finish_reason' => $outputData['finish_reason'],      // 完成原因（stop/length/content_filter等）
                        'model' => $outputData['model'],                      // 使用的AI模型名称
                        'usage' => $outputData['usage'],                      // Token使用统计信息
                        'tokens_used' => $tokensUsed                          // 总Token消耗数量
                    ],
                    'info_id' => $task->info_id,                        // 关联的信息ID（可为null）
                    'context' => $task->context,                              // 生成上下文（prompt_edit等）
                    'processing_time_ms' => $task->processing_time_ms,        // 处理耗时（毫秒）
                    'timestamp' => Carbon::now()->format('c')                 // 完成时间戳（ISO 8601格式）
                ];

                TransactionManager::commit('AiGenerationService.executeTextGeneration');

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '文本生成成功',
                    'data' => $responseData
                ];
            } else {
                // AI服务调用失败，记录详细错误信息
                Log::error('🔍 AI服务调用失败', [
                    'task_id' => $task->id,
                    'platform' => $task->platform,
                    'task_type' => $task->task_type,
                    'user_id' => $task->user_id,
                    'response' => $response,
                    'error_message' => $response['error'] ?? 'AI服务调用失败',
                    'status' => $response['status'] ?? 'unknown',
                    'mode' => $response['mode'] ?? 'unknown'
                ]);

                throw new \Exception($response['error'] ?? 'AI服务调用失败');
            }

        } catch (\Exception $e) {
            TransactionManager::rollback('AiGenerationService.executeTextGeneration', $e->getMessage());

            Log::error('🔍 executeTextGeneration异常捕获', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'task_type' => $task->task_type,
                'user_id' => $task->user_id,
                'exception_class' => get_class($e),
                'exception_message' => $e->getMessage(),
                'exception_file' => $e->getFile(),
                'exception_line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // 标记任务失败并检查结果
            $failResult = $task->fail($e->getMessage());
            if ($failResult['code'] !== ApiCodeEnum::SUCCESS) {
                Log::error('标记任务失败状态时发生错误', [
                    'task_id' => $task->id,
                    'original_error' => $e->getMessage(),
                    'fail_error' => $failResult['message']
                ]);
            }

            Log::error('🔍 文本生成任务失败', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'error' => $e->getMessage(),
                'task_status' => $task->status
            ]);

            Log::error('文本生成任务失败', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'error' => $e->getMessage()
            ]);

            // 返回失败数据
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成失败',
                'data' => [
                    'task_id' => $task->id,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'timestamp' => Carbon::now()->format('c')
                ]
            ];
        }
    }

    /**
     * WebSocket文本生成积分预检查
     * 在创建异步任务之前检查积分是否充足
     *
     * @param int $userId 用户ID
     * @param string $prompt 提示词
     * @param int|null $modelId 模型ID
     * @param array $generationParams 生成参数
     * @return array 检查结果
     */
    public function checkPointsForWebSocketTextGeneration(int $userId, string $prompt, ?int $modelId = null, array $generationParams = []): array
    {
        try {
            // 解析prompt获取实际提示词
            $promptData = json_decode($prompt, true);
            $actualPrompt = $promptData['提示词'] ?? $prompt;

            // 获取模型配置
            if ($modelId) {
                $model = AiModelConfig::active()->find($modelId);
                if (!$model) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '指定的模型不存在',
                        'data' => []
                    ];
                }
            } else {
                $modelResult = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
                if ($modelResult['code'] !== ApiCodeEnum::SUCCESS) {
                    return $modelResult;
                }
                $model = $modelResult['data']['model'];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateTextGenerationCost($model, $actualPrompt, $generationParams);

            // 检查积分是否充足
            $pointsCheck = $this->pointsService->checkPoints($userId, $estimatedCost, 'text_generation');

            if ($pointsCheck['code'] !== ApiCodeEnum::SUCCESS || !$pointsCheck['data']['sufficient']) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => $pointsCheck['data'] ?? []
                ];
            }

            // 积分检查通过
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分检查通过',
                'data' => [
                    'estimated_cost' => $estimatedCost,
                    'model' => $model,
                    'available_points' => $pointsCheck['data']['current_balance']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('WebSocket文本生成积分预检查失败', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::INTERNAL_ERROR,
                'message' => '积分检查失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 处理分镜生成任务
     *
     * @param int $userId 用户ID
     * @param int $infoId 信息ID
     * @param string $aspectRatio 尺寸比例
     * @param string $styleId 风格ID
     * @param array $aiTextData AI生成的文本数据
     * @param string $taskType 任务类型
     * @return array 处理结果
     */
    private function handleStoryboardGeneration(int $userId, int $infoId, string $aspectRatio, string $styleId, array $aiTextData, string $taskType): array
    {
        try {
            // 数据验证是否符合更新项目需求
            if(!$infoId || !$aspectRatio || !$styleId)
            {
                Log::warning($taskType.'类型任务缺少必要参数', [
                    'user_id' => $userId,
                    'info_id' => $infoId,
                    'aspect_ratio' => $aspectRatio,
                    'style_id' => $styleId,
                    'task_type' => $taskType
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => $taskType.'类型任务缺少必要参数：需要同时提供info_id、aspect_ratio和style_id',
                    'data' => [
                        'missing_params' => [
                            'info_id' => $infoId ? '已提供' : '缺失',
                            'aspect_ratio' => $aspectRatio ? '已提供' : '缺失',
                            'style_id' => $styleId ? '已提供' : '缺失'
                        ]
                    ]
                ];
            }

            // 导入分镜服务层
            $ProjectStoryboardService = new \App\Services\PyApi\ProjectStoryboardService();

            // 更新项目故事标题、故事简概、风格、规格、状态
            $result = $ProjectStoryboardService->extractStoryboardsFromStory($userId, $infoId, $styleId, $aspectRatio, $aiTextData['data']['result']['text']);

            if($result['code'] === ApiCodeEnum::SUCCESS)
            {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '分镜生成成功',
                    'data' => $result['data'] ?? []
                ];
            }
            else
            {
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '分镜生成失败：' . $result['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {

            $error_context = [
                'user_id' => $userId,
                'info_id' => $infoId,
                'aspect_ratio' => $aspectRatio,
                'style_id' => $styleId,
                'task_type' => $taskType
            ];

            Log::error('分镜生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '分镜生成异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 处理故事生成任务
     *
     * @param int $userId 用户ID
     * @param int $infoId 信息ID
     * @param array $aiTextData AI生成的文本数据
     * @param string $taskType 任务类型
     * @return array 处理结果
     */
    private function handleStoryGeneration(int $userId, int $infoId, array $aiTextData, string $taskType): array
    {
        try {
            // 验证信息ID是否存在
            if(!$infoId)
            {
                Log::warning('text_generation类型任务缺少信息ID', [
                    'user_id' => $userId,
                    'task_type' => $taskType
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'text_generation类型任务缺少必要参数：info_id',
                    'data' => []
                ];
            }

            // 使用 ProjectService 更新项目故事内容
            $projectService = new \App\Services\PyApi\ProjectService();
            $result = $projectService->updateProject($infoId, $userId, [
                'story_content' => $aiTextData['data']['result']['text']
            ]);

            if($result['code'] === ApiCodeEnum::SUCCESS)
            {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '故事智能扩写成功',
                    'data' => $result['data'] ?? []
                ];
            }
            else
            {
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '故事智能扩写失败：' . $result['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'info_id' => $infoId,
                'task_type' => $taskType
            ];

            Log::error('故事生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '故事生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 处理提示词生成任务
     *
     * @param int $userId 用户ID
     * @param int $storyboardId 分镜ID
     * @param array $aiTextData AI生成的文本数据
     * @param string $taskType 任务类型
     * @return array 处理结果
     */
    private function handlePromptGeneration(int $userId, int $storyboardId, array $aiTextData, string $taskType): array
    {
        try {
            // 验证分镜ID是否存在
            if(!$storyboardId)
            {
                Log::warning('text_generation_prompt类型任务缺少分镜ID', [
                    'user_id' => $userId,
                    'task_type' => $taskType
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'text_generation_prompt类型任务缺少必要参数：storyboard_id',
                    'data' => []
                ];
            }

            // 使用 ProjectStoryboardService 更新分镜AI提示词
            $storyboardService = new \App\Services\PyApi\ProjectStoryboardService();
            $result = $storyboardService->updateStoryboard($storyboardId, $userId, [
                'ai_prompt' => $aiTextData['data']['result']['text']
            ]);

            if($result['code'] === ApiCodeEnum::SUCCESS)
            {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '分镜图片提示词智能扩写成功',
                    'data' => $result['data'] ?? []
                ];
            }
            else
            {
                return [
                    'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                    'message' => '分镜图片提示词智能扩写失败：' . $result['message'],
                    'data' => []
                ];
            }

        } catch (\Exception $e) {

            $error_context = [
                'user_id' => $userId,
                'storyboard_id' => $storyboardId,
                'task_type' => $taskType
            ];

            Log::error('提示词生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '提示词生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 🔧 修复：从指定的WebSocket会话ID获取business_type（php\api\config\ai.php 映射后的business_type）
     */
    private function getBusinessTypeFromSpecificWebSocketSession(string $sessionId, int $userId, string $taskId): array
    {
        try {
            // 查询指定的WebSocket会话
            $session = WebSocketSession::where('session_id', $sessionId)
                ->where('user_id', $userId)
                ->active()
                ->first();

            if ($session && !empty($session->business_type)) {
                Log::info('从指定WebSocket会话获取business_type', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'session_id' => $session->session_id,
                    'business_type' => $session->business_type
                ]);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '获取business_type成功',
                    'data' => [
                        'business_type' => $session->business_type,
                        'session_id' => $session->session_id
                    ]
                ];
            } else {
                Log::warning('指定WebSocket会话不存在或business_type为空', [
                    'task_id' => $taskId,
                    'user_id' => $userId,
                    'session_id' => $sessionId,
                    'session_exists' => $session ? 'yes' : 'no',
                    'business_type' => $session->business_type ?? 'null'
                ]);

                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的WebSocket会话不存在或业务类型为空',
                    'data' => [
                        'session_id' => $sessionId,
                        'user_id' => $userId
                    ]
                ];
            }

        } catch (\Exception $e) {
            Log::error('从指定WebSocket会话获取business_type失败', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '获取WebSocket会话业务类型失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 处理角色生成任务
     * 解析新的JSON格式角色数据并调用角色服务层更新
     *
     * @param int $userId 用户ID
     * @param int $infoId 信息ID（角色ID）
     * @param array $aiTextData AI生成的文本数据
     * @param string $taskType 任务类型
     * @return array 处理结果
     */
    private function handleCharactertGeneration(int $userId, int $infoId, array $aiTextData, string $taskType): array
    {
        try {
            // 验证信息ID是否存在
            if(!$infoId)
            {
                Log::warning('text_prompt_character类型任务缺少信息ID', [
                    'user_id' => $userId,
                    'task_type' => $taskType
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'text_prompt_character类型任务缺少必要参数：info_id',
                    'data' => []
                ];
            }

            // 解析AI生成的角色数据（新JSON格式）
            $aiGeneratedText = $aiTextData['data']['result']['text'] ?? '';
            $characterData = $this->parseCharacterJsonData($aiGeneratedText);

            if (!$characterData) {
                Log::warning('AI生成的角色数据格式无效', [
                    'user_id' => $userId,
                    'info_id' => $infoId,
                    'ai_text' => $aiGeneratedText
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'AI生成的角色数据格式无效',
                    'data' => []
                ];
            }

            // 调用角色服务层更新角色数据
            $characterService = app(\App\Services\PyApi\CharacterService::class);
            $result = $characterService->updateAiCharacterFromData($infoId, $characterData, $userId);

            if ($result['code'] !== ApiCodeEnum::SUCCESS) {
                return $result;
            }

            Log::info('角色数据智能生成成功', [
                'user_id' => $userId,
                'info_id' => $infoId,
                'task_type' => $taskType,
                'character_data' => $characterData
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色数据智能生成成功',
                'data' => $result['data']
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'info_id' => $infoId,
                'task_type' => $taskType
            ];

            Log::error('角色生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 处理风格生成任务
     * 解析新的JSON格式风格数据并调用风格服务层更新
     *
     * @param int $userId 用户ID
     * @param int $infoId 信息ID（风格ID）
     * @param array $aiTextData AI生成的文本数据
     * @param string $taskType 任务类型
     * @return array 处理结果
     */
    private function handleStyleGeneration(int $userId, int $infoId, array $aiTextData, string $taskType): array
    {
        try {
            // 验证信息ID是否存在
            if(!$infoId)
            {
                Log::warning('text_prompt_style类型任务缺少信息ID', [
                    'user_id' => $userId,
                    'task_type' => $taskType
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'text_prompt_style类型任务缺少必要参数：info_id',
                    'data' => []
                ];
            }

            // 解析AI生成的风格数据（新JSON格式）
            $aiGeneratedText = $aiTextData['data']['result']['text'] ?? '';
            $styleData = $this->parseStyleJsonData($aiGeneratedText);

            if (!$styleData) {
                Log::warning('AI生成的风格数据格式无效', [
                    'user_id' => $userId,
                    'info_id' => $infoId,
                    'ai_text' => $aiGeneratedText
                ]);

                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'AI生成的风格数据格式无效',
                    'data' => []
                ];
            }

            // 调用风格服务层更新风格数据
            $styleService = app(\App\Services\PyApi\StyleService::class);
            $result = $styleService->updateAiStyleFromData($infoId, $styleData, $userId);

            if ($result['code'] !== ApiCodeEnum::SUCCESS) {
                return $result;
            }

            Log::info('风格数据智能生成成功', [
                'user_id' => $userId,
                'info_id' => $infoId,
                'task_type' => $taskType,
                'style_data' => $styleData
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '风格数据智能生成成功',
                'data' => $result['data']
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'info_id' => $infoId,
                'task_type' => $taskType
            ];

            Log::error('风格生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '风格生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 计算文本生成成本
     */
    private function calculateTextGenerationCost(AiModelConfig $model, string $prompt, array $params): float
    {
        $inputTokens = strlen($prompt) / 4; // 简单估算
        $maxTokens = $params['max_tokens'] ?? 1000;
        $totalTokens = $inputTokens + $maxTokens;

        return round($totalTokens * $model->cost_per_request, 4);
    }

    /**
     * 解析AI生成的角色JSON数据
     *
     * @param string $jsonText AI生成的JSON文本
     * @return array|null 解析后的角色数据，失败返回null
     */
    private function parseCharacterJsonData(string $jsonText): ?array
    {
        try {
            // 清理可能的多余文本，提取JSON部分
            $jsonText = trim($jsonText);

            // 尝试找到JSON开始和结束位置
            $startPos = strpos($jsonText, '{');
            $endPos = strrpos($jsonText, '}');

            if ($startPos !== false && $endPos !== false && $endPos > $startPos) {
                $jsonText = substr($jsonText, $startPos, $endPos - $startPos + 1);
            }

            // 解析JSON
            $data = json_decode($jsonText, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('JSON解析失败', [
                    'json_text' => $jsonText,
                    'json_error' => json_last_error_msg()
                ]);
                return null;
            }

            // 验证必需字段
            $requiredFields = ['角色名称', '提示词', '类型', '性别', '年龄阶段','TAG'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    Log::warning('角色数据缺少必需字段', [
                        'missing_field' => $field,
                        'data' => $data
                    ]);
                    return null;
                }
            }

            // 处理 TAG 字段（可选字段）
            if (isset($data['TAG'])) {
                // 确保 TAG 是数组格式
                if (is_array($data['TAG'])) {
                    // 过滤空值和非字符串值，限制最多5个标签
                    $tags = array_filter($data['TAG'], function($tag) {
                        return is_string($tag) && !empty(trim($tag));
                    });
                    $data['TAG'] = array_slice(array_values($tags), 0, 5);
                } else {
                    // 如果不是数组，尝试转换为数组
                    $tagString = is_string($data['TAG']) ? $data['TAG'] : '';
                    if (!empty($tagString)) {
                        // 尝试按逗号、分号或中文顿号分割
                        $tags = preg_split('/[,，;；、]/', $tagString);
                        $tags = array_map('trim', $tags);
                        $tags = array_filter($tags, function($tag) {
                            return !empty($tag);
                        });
                        $data['TAG'] = array_slice(array_values($tags), 0, 5);
                    } else {
                        $data['TAG'] = [];
                    }
                }
            } else {
                // 如果没有 TAG 字段，设置为空数组
                $data['TAG'] = [];
            }

            Log::info('角色JSON数据解析成功', [
                'parsed_data' => [
                    '提示词' => mb_substr($data['提示词'], 0, 50) . '...',
                    '类型' => $data['类型'],
                    '性别' => $data['性别'],
                    '年龄阶段' => $data['年龄阶段'],
                    'TAG数量' => count($data['TAG']),
                    'TAG内容' => $data['TAG']
                ]
            ]);

            return $data;

        } catch (\Exception $e) {
            Log::error('解析角色JSON数据失败', [
                'json_text' => $jsonText,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 解析AI生成的风格JSON数据
     *
     * @param string $jsonText AI生成的JSON文本
     * @return array|null 解析后的风格数据，失败返回null
     */
    private function parseStyleJsonData(string $jsonText): ?array
    {
        try {
            // 清理可能的多余文本，提取JSON部分
            $jsonText = trim($jsonText);

            // 尝试找到JSON开始和结束位置
            $startPos = strpos($jsonText, '{');
            $endPos = strrpos($jsonText, '}');

            if ($startPos !== false && $endPos !== false && $endPos > $startPos) {
                $jsonText = substr($jsonText, $startPos, $endPos - $startPos + 1);
            }

            // 解析JSON
            $data = json_decode($jsonText, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('风格JSON解析失败', [
                    'json_text' => $jsonText,
                    'json_error' => json_last_error_msg()
                ]);
                return null;
            }

            // 验证必需字段
            $requiredFields = ['风格名称', '风格描述', '提示词'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    Log::warning('风格数据缺少必需字段', [
                        'missing_field' => $field,
                        'data' => $data
                    ]);
                    return null;
                }
            }

            // 处理 TAG 字段（可选字段）
            if (isset($data['TAG'])) {
                // 确保 TAG 是数组格式
                if (is_array($data['TAG'])) {
                    // 过滤空值和非字符串值，限制最多5个标签
                    $tags = array_filter($data['TAG'], function($tag) {
                        return is_string($tag) && !empty(trim($tag));
                    });
                    $data['TAG'] = array_slice(array_values($tags), 0, 5);
                } else {
                    // 如果不是数组，尝试转换为数组
                    $tagString = is_string($data['TAG']) ? $data['TAG'] : '';
                    if (!empty($tagString)) {
                        // 尝试按逗号、分号或中文顿号分割
                        $tags = preg_split('/[,，;；、]/', $tagString);
                        $tags = array_map('trim', $tags);
                        $tags = array_filter($tags, function($tag) {
                            return !empty($tag);
                        });
                        $data['TAG'] = array_slice(array_values($tags), 0, 5);
                    } else {
                        $data['TAG'] = [];
                    }
                }
            } else {
                // 如果没有 TAG 字段，设置为空数组
                $data['TAG'] = [];
            }

            Log::info('风格JSON数据解析成功', [
                'parsed_data' => [
                    '风格名称' => $data['风格名称'],
                    '风格描述' => mb_substr($data['风格描述'], 0, 50) . '...',
                    '提示词' => mb_substr($data['提示词'], 0, 50) . '...',
                    'TAG数量' => count($data['TAG']),
                    'TAG内容' => $data['TAG']
                ]
            ]);

            return $data;

        } catch (\Exception $e) {
            Log::error('解析风格JSON数据失败', [
                'json_text' => $jsonText,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 根据映射后的业务类型验证信息ID
     *
     * @param string $businessType 映射后的业务类型（如 text_prompt_character, text_prompt_style）
     * @param int $infoId 信息ID
     * @param int $userId 用户ID
     * @return array 验证结果
     */
    private function validateInfoIdByBusinessType(string $businessType, int $infoId, int $userId): array
    {
        try {
            switch ($businessType) {
                case 'text_prompt_character':
                    // 调用角色服务层验证
                    $result = $this->characterService->validateCharacterExists($infoId);
                    if ($result['code'] !== ApiCodeEnum::SUCCESS) {
                        return $result;
                    }

                    Log::info('角色验证成功', [
                        'business_type' => $businessType,
                        'character_id' => $infoId,
                        'character_name' => $result['data']['character']->name,
                        'user_id' => $userId
                    ]);
                    break;

                case 'text_prompt_style':
                    // 调用风格服务层验证
                    $result = $this->styleService->validateStyleExists($infoId);
                    if ($result['code'] !== ApiCodeEnum::SUCCESS) {
                        return $result;
                    }

                    Log::info('风格验证成功', [
                        'business_type' => $businessType,
                        'style_id' => $infoId,
                        'style_name' => $result['data']['style']->name,
                        'user_id' => $userId
                    ]);
                    break;

                default:
                    // 调用项目服务层验证
                    $result = $this->projectService->validateProjectExists($infoId, $userId);
                    if ($result['code'] !== ApiCodeEnum::SUCCESS) {
                        return $result;
                    }

                    Log::info('项目验证成功', [
                        'business_type' => $businessType,
                        'project_id' => $infoId,
                        'project_name' => $result['data']['project']->name,
                        'user_id' => $userId
                    ]);
                    break;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '验证成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            Log::error('业务类型验证失败', [
                'method' => __METHOD__,
                'business_type' => $businessType,
                'info_id' => $infoId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '验证失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

}