---
alwaysApply: false
---
## 海螺AI (MiniMax) API 接口开发规范指南 (完整版)

### API概述

**官方文档**: https://platform.minimaxi.com/document/
**API基础URL**: `https://api.minimaxi.com`
**账户管理**: https://platform.minimaxi.com/user-center/basic-information/interface-key
**主要功能**: 文本生成、语音合成、视频生成、音乐生成、图像生成、文件管理、批量处理
**适用场景**: 智能对话、内容创作、多媒体生成、AI助手开发、批量处理

### 支持模型 (基于官方文档)

#### 文本生成模型
- **MiniMax-M1**: 全球最长上下文窗口，支持1M-token输入，80k-token输出，Agent应用性能出色
- **MiniMax-Text-01**: 标准文本生成模型，支持1M-token上下文，最大生成token数2048

#### 视频生成模型
- **Hailuo 02**: 全新视频模型，指令理解更精准，支持原生1080p视频，最长可生成10秒内容

### 认证配置

#### API密钥配置
```php
<?php

class MiniMaxConfig
{
    const BASE_URL = 'https://api.minimaxi.com';
    const HAILUO_URL = 'https://api.minimaxi.com';  // 统一使用官方域名
    const TIMEOUT = 180;
    const MAX_RETRIES = 3;

    private $apiKey;
    private $groupId;
    private $userId;
    private $sessionId;

    public function __construct($apiKey, $groupId = null, $userId = null)
    {
        $this->apiKey = $apiKey;
        $this->groupId = $groupId;
        $this->userId = $userId;
        $this->sessionId = $this->generateSessionId();
    }

    private function generateSessionId()
    {
        return 'sess_' . uniqid() . '_' . time();
    }

    public function getHeaders($apiType = 'standard')
    {
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'User-Agent: MiniMax-PHP-Client/2.0'
        ];

        if ($this->groupId) {
            $headers[] = 'GroupId: ' . $this->groupId;
        }

        if ($this->userId) {
            $headers[] = 'X-User-ID: ' . $this->userId;
        }

        // 统一API配置，移除海螺AI特殊处理
        $headers[] = 'X-Session-ID: ' . $this->sessionId;
        $headers[] = 'X-Platform: api';

        return $headers;
    }

    public function getBaseUrl($apiType = 'standard')
    {
        // 统一使用官方API域名
        return self::BASE_URL;
    }

    public function getApiKey()
    {
        return $this->apiKey;
    }

    public function getGroupId()
    {
        return $this->groupId;
    }
}

### 完整API接口列表 (基于官方文档)

#### 接口分类
1. **文本生成** - ChatCompletion、批量处理、函数调用
2. **语音合成** - 同步语音合成、异步长文本语音合成、快速复刻、音色设计
3. **视频生成** - 文本生视频、图像生视频
4. **音乐生成** - 文本生成音乐
5. **图像生成** - 文本生图、图像编辑
6. **文件管理** - 文件上传、下载、删除
7. **错误码查询** - 错误信息查询

### 1. 文本生成接口 (ChatCompletion)

**接口地址**: `POST https://api.minimaxi.com/v1/text/chatcompletion_v2`
**官方文档**: https://platform.minimaxi.com/document/对话
**兼容性**: 支持OpenAI API格式和OpenAI SDK接入

```php
<?php

class MiniMaxChatAPI
{
    private $config;

    public function __construct(MiniMaxConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 文本生成接口 (ChatCompletion) - 基于官方文档
     * 支持模型: MiniMax-M1, MiniMax-Text-01
     * 支持OpenAI SDK接入
     */
    public function chatCompletion($messages, $options = [])
    {
        $defaultOptions = [
            'model' => 'MiniMax-M1',              // 模型ID (必填)
            'messages' => $messages,               // 对话内容 (必填)
            'stream' => false,                     // 是否流式返回
            'max_tokens' => 8192,                  // 最大生成token数 (0,40000]
            'temperature' => 1,                    // 随机性 (0,1] - M1推荐[0.8,1]
            'top_p' => 0.95,                      // 采样方法 (0,1]
            'mask_sensitive_info' => false,       // 是否对敏感信息打码
            'tool_choice' => 'auto',              // 工具调用开关: none/auto
            'tools' => null,                      // 支持的工具
            'response_format' => null,            // 输出格式 (仅MiniMax-Text-01支持)
            'stream_options' => null              // 流式选项
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/text/chatcompletion_v2', $payload);
    }

    /**
     * 使用OpenAI SDK接入 (推荐方式)
     */
    public function createOpenAIClient($apiKey)
    {
        // 需要先安装: pip install --upgrade openai
        return [
            'base_url' => 'https://api.minimaxi.com/v1',
            'api_key' => $apiKey,
            'usage_example' => '
from openai import OpenAI

client = OpenAI(
    api_key="<minimax api key>",
    base_url="https://api.minimaxi.com/v1"
)

response = client.chat.completions.create(
    model="MiniMax-M1",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello!"}
    ],
    stream=True
)
            '
        ];
    }

    /**
     * 多模态对话 (支持图像输入)
     */
    public function multimodalChat($messages, $options = [])
    {
        $defaultOptions = [
            'model' => 'abab6.5-chat-preview',
            'messages' => $messages,
            'max_tokens' => 4096,
            'temperature' => 0.7,
            'vision_detail' => 'high'
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/text/chatcompletion_v2', $payload);
    }
    
    /**
     * 流式对话
     */
    public function streamChat($messages, $callback, $options = [])
    {
        $options['stream'] = true;
        $payload = array_merge([
            'model' => 'MiniMax-M1',              // 使用官方最新模型
            'messages' => $messages,
            'temperature' => 0.9,                 // M1推荐[0.8,1]
            'max_tokens' => 8192                  // M1支持更大输出
        ], $options);
        
        return $this->streamRequest('/v1/text/chatcompletion_v2', $payload, $callback);
    }
    
    /**
     * 角色扮演对话
     */
    public function rolePlayChat($botId, $messages, $options = [])
    {
        $defaultOptions = [
            'bot_id' => $botId,
            'messages' => $messages,
            'stream' => false,
            'temperature' => 0.8,
            'max_tokens' => 2048
        ];
        
        $payload = array_merge($defaultOptions, $options);
        
        return $this->makeRequest('/v1/text/chatcompletion_pro', $payload);
    }
    
    private function makeRequest($endpoint, $data)
    {
        $url = MiniMaxConfig::BASE_URL . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("MiniMax API请求失败: HTTP {$httpCode}");
        }
        
        return json_decode($response, true);
    }
    
    private function streamRequest($endpoint, $data, $callback)
    {
        $url = MiniMaxConfig::BASE_URL . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => array_merge($this->config->getHeaders(), ['Accept: text/event-stream']),
            CURLOPT_WRITEFUNCTION => function($ch, $data) use ($callback) {
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    if (strpos($line, 'data: ') === 0) {
                        $json = substr($line, 6);
                        if ($json === '[DONE]') {
                            return strlen($data);
                        }
                        
                        $decoded = json_decode($json, true);
                        if ($decoded && isset($decoded['choices'][0]['delta']['content'])) {
                            $callback($decoded['choices'][0]['delta']['content']);
                        }
                    }
                }
                return strlen($data);
            },
            CURLOPT_TIMEOUT => 300,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        curl_exec($ch);
        curl_close($ch);
    }
}
```

#### 2. 视频生成API (完整更新)

**接口地址**: `POST https://api.minimaxi.com/v1/video_generation`
**查询接口**: `GET https://api.minimaxi.com/v1/query/video_generation?task_id={task_id}`
**官方文档**: https://platform.minimaxi.com/document/video_generation
**支持模型**:
- **MiniMax-Hailuo-02**: 新一代视频生成模型，支持1080P超清视频，10s视频生成
- **T2V-01-Director**: 文生视频模型（导演版），支持运镜指令控制
- **I2V-01-Director**: 图生视频模型（导演版），支持运镜指令控制
- **I2V-01-live**: 图生视频模型，增强卡通、漫画、手绘风格表现
- **S2V-01**: 主体参考视频生成模型，支持人物主体稳定性

```php
<?php

class MiniMaxVideoAPI
{
    private $config;

    public function __construct(MiniMaxConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 文本生成视频 - Hailuo 02模型
     * 支持原生1080p视频，最长可生成10秒内容
     */
    public function textToVideo($prompt, $options = [])
    {
        $defaultOptions = [
            'model' => 'Hailuo 02',               // 视频生成模型
            'prompt' => $prompt,                  // 文本提示词 (必填)
            'duration' => 5,                      // 视频时长 (秒)
            'resolution' => '1080p',              // 分辨率: 720p, 1080p
            'aspect_ratio' => '16:9',             // 画面比例
            'fps' => 24,                          // 帧率
            'seed' => null,                       // 随机种子
            'callback_url' => null                // 回调地址
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/video_generation', $payload);
    }

    /**
     * 查询视频生成任务状态
     */
    public function getVideoTask($taskId)
    {
        return $this->makeRequest("/v1/video_generation/{$taskId}", [], 'GET');
    }

    private function makeRequest($endpoint, $data, $method = 'POST')
    {
        $url = MiniMaxConfig::BASE_URL . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $method === 'POST' ? json_encode($data) : null,
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("MiniMax Video API请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }
}
```

#### 3. 图像生成API
```php
<?php

class MiniMaxImageAPI
{
    private $config;
    
    public function __construct(MiniMaxConfig $config)
    {
        $this->config = $config;
    }
    
    /**
     * 文本生成图像
     */
    public function textToImage($prompt, $options = [])
    {
        $defaultOptions = [
            'model' => 'image-01',            // 官方最新模型
            'prompt' => $prompt,              // 图像描述 (必填) [最大1500字符]
            'aspect_ratio' => '1:1',          // 宽高比: 1:1/16:9/4:3/3:2/2:3/3:4/9:16/21:9
            'width' => null,                  // 宽度 [512,2048] (需为8的倍数)
            'height' => null,                 // 高度 [512,2048] (需为8的倍数)
            'response_format' => 'url',       // 返回格式: url/base64
            'seed' => null,                   // 随机种子
            'n' => 1,                         // 生成数量 [1,9]
            'prompt_optimizer' => false       // prompt自动优化
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/image_generation', $payload);
    }
    
    /**
     * 图生图 (主体参考)
     */
    public function imageToImage($prompt, $subjectReference, $options = [])
    {
        $defaultOptions = [
            'model' => 'image-01',
            'prompt' => $prompt,
            'subject_reference' => [
                [
                    'image' => $subjectReference,  // Base64编码或URL
                    'weight' => 1.0               // 参考权重 [0.1,2.0]
                ]
            ],
            'aspect_ratio' => '1:1',
            'response_format' => 'url',
            'n' => 1,
            'prompt_optimizer' => false
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/image_generation', $payload);
    }

    /**
     * 画风控制生图 (image-01-live模型)
     */
    public function generateWithStyle($prompt, $style, $options = [])
    {
        $defaultOptions = [
            'model' => 'image-01-live',       // 支持画风设置的模型
            'prompt' => $prompt,
            'style' => [
                'style_type' => $style,       // 画风类型
                'intensity' => 1.0            // 画风强度 [0.1,2.0]
            ],
            'aspect_ratio' => '1:1',
            'response_format' => 'url',
            'n' => 1,
            'prompt_optimizer' => false
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/image_generation', $payload);
    }
    
    private function makeRequest($endpoint, $data)
    {
        $url = MiniMaxConfig::BASE_URL . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("MiniMax API请求失败: HTTP {$httpCode}");
        }
        
        return json_decode($response, true);
    }
}
```

### 4. 语音合成接口 (完整实现)

**官方文档**: https://platform.minimaxi.com/document/同步语音合成
**支持功能**: 同步语音合成、异步长文本语音合成、快速复刻、音色设计、声音管理

```php
<?php

class MiniMaxVoiceAPI
{
    private $config;

    public function __construct(MiniMaxConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 同步语音合成 (T2A v2)
     * 接口地址: POST https://api.minimaxi.com/v1/t2a_v2
     */
    public function textToSpeech($text, $options = [])
    {
        $defaultOptions = [
            'model' => 'speech-01',           // 语音合成模型
            'text' => $text,                  // 待合成文本 (必填)
            'voice_id' => 'male-qn-qingse',   // 音色ID (必填)
            'speed' => 1.0,                   // 语速 [0.5, 2.0]
            'vol' => 1.0,                     // 音量 [0.1, 3.0]
            'pitch' => 0,                     // 音调 [-12, 12]
            'audio_setting' => [
                'sample_rate' => 32000,       // 采样率: 16000/24000/32000
                'bitrate' => 128000,          // 比特率: 64000/128000/256000
                'format' => 'mp3'             // 格式: mp3/wav/pcm
            ],
            'pronunciation_dict' => null,     // 发音词典
            'output_format' => 'mp3'          // 输出格式
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/t2a_v2', $payload);
    }

    /**
     * 异步长文本语音合成
     * 接口地址: POST https://api.minimaxi.com/v1/t2a_async
     */
    public function asyncTextToSpeech($text, $options = [])
    {
        $defaultOptions = [
            'model' => 'speech-01-async',
            'text' => $text,
            'voice_id' => 'male-qn-qingse',
            'speed' => 1.0,
            'vol' => 1.0,
            'pitch' => 0,
            'audio_setting' => [
                'sample_rate' => 32000,
                'bitrate' => 128000,
                'format' => 'mp3'
            ]
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/t2a_async', $payload);
    }

    /**
     * 快速复刻 (Voice Cloning)
     * 接口地址: POST https://api.minimaxi.com/v1/voice_clone
     */
    public function voiceClone($audioFile, $voiceName, $options = [])
    {
        $defaultOptions = [
            'voice_name' => $voiceName,       // 声音名称 (必填)
            'audio_file' => $audioFile,       // 音频文件 (必填)
            'text' => null,                   // 参考文本
            'language' => 'zh'                // 语言: zh/en
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/voice_clone', $payload);
    }

    /**
     * 音色设计
     * 接口地址: POST https://api.minimaxi.com/v1/voice_design
     */
    public function voiceDesign($options = [])
    {
        $defaultOptions = [
            'gender' => 'male',               // 性别: male/female
            'age' => 'adult',                 // 年龄: child/teen/adult/elder
            'emotion' => 'neutral',           // 情感: neutral/happy/sad/angry
            'accent' => 'standard',           // 口音: standard/regional
            'voice_name' => 'custom_voice_' . time()
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/voice_design', $payload);
    }

    /**
     * 查询可用声音ID
     * 接口地址: GET https://api.minimaxi.com/v1/query/voice
     */
    public function getAvailableVoices()
    {
        return $this->makeGetRequest('/v1/query/voice');
    }

    /**
     * 删除声音
     * 接口地址: DELETE https://api.minimaxi.com/v1/voice_delete
     */
    public function deleteVoice($voiceId)
    {
        $payload = ['voice_id' => $voiceId];

        return $this->makeDeleteRequest('/v1/voice_delete', $payload);
    }

    private function makeRequest($endpoint, $data)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("MiniMax Voice API请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }

    private function makeGetRequest($endpoint)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    private function makeDeleteRequest($endpoint, $data)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => 'DELETE',
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }
}
```

### 5. 音乐生成接口 (完整实现)

**官方文档**: https://platform.minimaxi.com/document/Music%20Generation
**支持模型**: music-1.5 (prompt+lyrics), music-01 (基于上传音频)
**支持功能**: 文本生成音乐、音频上传分离、音乐风格转换

```php
<?php

class MiniMaxMusicAPI
{
    private $config;

    public function __construct(MiniMaxConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 音频文件上传 (用于music-01模型)
     * 接口地址: POST https://api.minimaxi.com/v1/music_upload
     */
    public function uploadMusicFile($filePath, $purpose, $options = [])
    {
        $defaultOptions = [
            'purpose' => $purpose,            // song/voice/instrumental (必填)
            'file' => $filePath               // 音频文件路径 (必填)
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/music_upload', $payload);
    }

    /**
     * 音乐生成 (music-1.5模型 - prompt+lyrics)
     * 接口地址: POST https://api.minimaxi.com/v1/music_generation
     */
    public function generateMusicWithPrompt($prompt, $lyrics, $options = [])
    {
        $defaultOptions = [
            'model' => 'music-1.5',           // 模型 (必填)
            'prompt' => $prompt,              // 音乐创作灵感 (必填) [10,300]字符
            'lyrics' => $lyrics,              // 歌词 (必填) [10,600]字符
            'stream' => false,                // 是否开启流式
            'output_format' => 'hex',         // url/hex，默认hex
            'audio_setting' => [
                'sample_rate' => 44100,       // 采样率
                'bitrate' => 256000,          // 比特率
                'format' => 'mp3'             // 格式
            ]
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/music_generation', $payload);
    }

    /**
     * 音乐生成 (music-01模型 - 基于上传音频)
     * 接口地址: POST https://api.minimaxi.com/v1/music_generation
     */
    public function generateMusicWithReference($options = [])
    {
        $defaultOptions = [
            'model' => 'music-01',            // 模型 (必填)
            'refer_voice' => null,            // 参考音色ID (通过upload获得)
            'refer_instrumental' => null,     // 参考伴奏ID (通过upload获得)
            'refer_vocal' => null,            // 替换音色ID (可选)
            'lyrics' => null,                 // 歌词 (可选) [最长200字符]
            'stream' => false,
            'audio_setting' => [
                'sample_rate' => 44100,
                'bitrate' => 256000,
                'format' => 'mp3'
            ]
        ];

        $payload = array_merge($defaultOptions, $options);

        // refer_voice、refer_instrumental至少一个必填
        if (empty($payload['refer_voice']) && empty($payload['refer_instrumental'])) {
            throw new Exception('refer_voice和refer_instrumental至少需要提供一个');
        }

        return $this->makeRequest('/v1/music_generation', $payload);
    }

    /**
     * 流式音乐生成
     */
    public function streamMusicGeneration($prompt, $lyrics, $callback, $options = [])
    {
        $options['stream'] = true;
        $payload = array_merge([
            'model' => 'music-1.5',
            'prompt' => $prompt,
            'lyrics' => $lyrics,
            'output_format' => 'hex'          // 流式场景仅支持hex
        ], $options);

        return $this->streamRequest('/v1/music_generation', $payload, $callback);
    }

    /**
     * 音乐风格模板
     */
    public function getMusicStyles()
    {
        return [
            'ambient' => '环境音乐',
            'classical' => '古典音乐',
            'electronic' => '电子音乐',
            'jazz' => '爵士音乐',
            'rock' => '摇滚音乐',
            'pop' => '流行音乐',
            'folk' => '民谣音乐',
            'cinematic' => '电影配乐',
            'indie' => '独立音乐',
            'blues' => '蓝调音乐'
        ];
    }

    /**
     * 歌词结构模板
     */
    public function getLyricsStructure()
    {
        return [
            '[Intro]' => '前奏',
            '[Verse]' => '主歌',
            '[Chorus]' => '副歌',
            '[Bridge]' => '桥段',
            '[Outro]' => '尾奏'
        ];
    }

    private function makeRequest($endpoint, $data)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 300,           // 音乐生成需要更长时间
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("MiniMax Music API请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }

    private function streamRequest($endpoint, $data, $callback)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => array_merge($this->config->getHeaders(), ['Accept: text/event-stream']),
            CURLOPT_WRITEFUNCTION => function($ch, $data) use ($callback) {
                $lines = explode("\n", $data);
                foreach ($lines as $line) {
                    if (strpos($line, 'data: ') === 0) {
                        $json = substr($line, 6);
                        if ($json === '[DONE]') {
                            return strlen($data);
                        }

                        $decoded = json_decode($json, true);
                        if ($decoded && isset($decoded['data']['audio'])) {
                            $callback($decoded['data']['audio']);
                        }
                    }
                }
                return strlen($data);
            },
            CURLOPT_TIMEOUT => 600,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        curl_exec($ch);
        curl_close($ch);
    }
}
```

### 6. 文件管理接口 (完整实现)

**接口地址**: `GET https://api.minimaxi.com/v1/files/retrieve?file_id={file_id}`
**官方文档**: https://platform.minimaxi.com/document/file

```php
<?php

class MiniMaxFileAPI
{
    private $config;

    public function __construct(MiniMaxConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 文件下载
     * 接口地址: GET https://api.minimaxi.com/v1/files/retrieve?file_id={file_id}
     */
    public function retrieveFile($fileId)
    {
        $endpoint = '/v1/files/retrieve?file_id=' . urlencode($fileId);

        return $this->makeGetRequest($endpoint);
    }

    /**
     * 文件上传 (通用)
     */
    public function uploadFile($filePath, $purpose = 'general')
    {
        $payload = [
            'file' => $filePath,
            'purpose' => $purpose
        ];

        return $this->makeRequest('/v1/files/upload', $payload);
    }

    /**
     * 获取文件列表
     */
    public function listFiles($purpose = null)
    {
        $endpoint = '/v1/files';
        if ($purpose) {
            $endpoint .= '?purpose=' . urlencode($purpose);
        }

        return $this->makeGetRequest($endpoint);
    }

    /**
     * 删除文件
     */
    public function deleteFile($fileId)
    {
        $endpoint = '/v1/files/' . urlencode($fileId);

        return $this->makeDeleteRequest($endpoint);
    }

    private function makeRequest($endpoint, $data)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    private function makeGetRequest($endpoint)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    private function makeDeleteRequest($endpoint)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => 'DELETE',
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => MiniMaxConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }
}
```

### 模型配置

#### 1. 支持的模型 (基于官方文档)
```php
class MiniMaxModels
{
    const CHAT_MODELS = [
        'MiniMax-M1' => '全球最长上下文窗口，支持1M-token输入，80k-token输出，Agent应用性能出色',
        'MiniMax-Text-01' => '标准文本生成模型，支持1M-token上下文，最大生成token数2048'
    ];

    const VIDEO_MODELS = [
        'Hailuo 02' => '全新视频模型，指令理解更精准，支持原生1080p视频，最长可生成10秒内容'
    ];

    const VIDEO_MODELS = [
        'MiniMax-Hailuo-02' => '新一代视频生成模型，支持1080P超清视频，10s视频生成',
        'T2V-01-Director' => '文生视频模型（导演版），支持运镜指令控制',
        'I2V-01-Director' => '图生视频模型（导演版），支持运镜指令控制',
        'I2V-01-live' => '图生视频模型，增强卡通、漫画、手绘风格表现',
        'S2V-01' => '主体参考视频生成模型，支持人物主体稳定性',
        'I2V-01' => '标准图生视频模型',
        'T2V-01' => '标准文生视频模型'
    ];

    const VOICE_MODELS = [
        'speech-01' => '同步语音合成',
        'speech-01-async' => '异步长文本语音合成',
        'speech-clone' => '快速复刻',
        'speech-design' => '音色设计'
    ];

    const MUSIC_MODELS = [
        'music-1.5' => '新版音乐生成模型，支持prompt+lyrics',
        'music-01' => '基于上传音频的音乐生成模型'
    ];

    const IMAGE_MODELS = [
        'image-01' => '全新图像生成模型，支持文生图、图生图',
        'image-01-live' => '图像生成模型，支持文生图并进行画风设置'
    ];
}
```

#### 2. 专用提示词模板
```php
class MiniMaxPrompts
{
    /**
     * 剧情创作提示词
     */
    public static function storyCreation($genre, $theme, $length)
    {
        return "作为一个专业的{$genre}编剧，请创作一个以'{$theme}'为主题的{$length}剧本。要求：
1. 情节紧凑，逻辑清晰
2. 角色鲜明，对话生动
3. 冲突明确，转折合理
4. 结构完整，节奏适中
请按照标准剧本格式输出。";
    }
    
    /**
     * 分镜脚本生成
     */
    public static function storyboardScript($scene, $style)
    {
        return "请将以下场景转换为详细的分镜脚本，风格为{$style}：
场景：{$scene}

每个镜头包含：
- 镜头编号
- 景别描述
- 机位角度
- 画面内容
- 角色动作
- 对白内容
- 时长估计

要求专业、详细、可执行。";
    }
    
    /**
     * 角色对话生成
     */
    public static function characterDialogue($character1, $character2, $situation, $emotion)
    {
        return "请为以下角色创作一段{$emotion}的对话：
角色A：{$character1}
角色B：{$character2}
情境：{$situation}

要求：
1. 符合角色性格特点
2. 体现情感状态
3. 推进剧情发展
4. 语言自然流畅
5. 包含潜台词";
    }
}
```

### 高级功能应用

#### 1. 智能剧本生成器
```php
class IntelligentScriptGenerator
{
    private $chatAPI;
    private $imageAPI;
    private $videoAPI;
    
    public function __construct($config)
    {
        $this->chatAPI = new MiniMaxChatAPI($config);
        $this->imageAPI = new MiniMaxImageAPI($config);
        $this->videoAPI = new MiniMaxVideoAPI($config);
    }
    
    /**
     * 生成完整剧本
     */
    public function generateFullScript($concept, $genre = '科幻', $length = '短片')
    {
        // 1. 生成剧本大纲
        $outlinePrompt = MiniMaxPrompts::storyCreation($genre, $concept, $length);
        $outlineMessages = [
            ['role' => 'user', 'content' => $outlinePrompt]
        ];
        
        $outlineResponse = $this->chatAPI->chatCompletion($outlineMessages, [
            'temperature' => 0.8,
            'max_tokens' => 4096
        ]);
        
        $outline = $outlineResponse['choices'][0]['message']['content'];
        
        // 2. 生成详细剧本
        $scriptPrompt = "基于以下大纲，请创作详细的剧本，包含完整的对话和场景描述：\n\n" . $outline;
        $scriptMessages = [
            ['role' => 'user', 'content' => $scriptPrompt]
        ];
        
        $scriptResponse = $this->chatAPI->chatCompletion($scriptMessages, [
            'temperature' => 0.7,
            'max_tokens' => 8192
        ]);
        
        $script = $scriptResponse['choices'][0]['message']['content'];
        
        // 3. 生成分镜脚本
        $storyboardPrompt = MiniMaxPrompts::storyboardScript($script, '电影级');
        $storyboardMessages = [
            ['role' => 'user', 'content' => $storyboardPrompt]
        ];
        
        $storyboardResponse = $this->chatAPI->chatCompletion($storyboardMessages, [
            'temperature' => 0.6,
            'max_tokens' => 6144
        ]);
        
        $storyboard = $storyboardResponse['choices'][0]['message']['content'];
        
        return [
            'concept' => $concept,
            'outline' => $outline,
            'script' => $script,
            'storyboard' => $storyboard,
            'metadata' => [
                'genre' => $genre,
                'length' => $length,
                'generated_at' => date('Y-m-d H:i:s')
            ]
        ];
    }
    
    /**
     * 生成角色对话
     */
    public function generateDialogue($characters, $situation, $emotion = '自然')
    {
        $dialogues = [];
        
        for ($i = 0; $i < count($characters) - 1; $i++) {
            $char1 = $characters[$i];
            $char2 = $characters[$i + 1];
            
            $prompt = MiniMaxPrompts::characterDialogue($char1, $char2, $situation, $emotion);
            $messages = [
                ['role' => 'user', 'content' => $prompt]
            ];
            
            $response = $this->chatAPI->chatCompletion($messages, [
                'temperature' => 0.8,
                'max_tokens' => 2048
            ]);
            
            $dialogues[] = [
                'characters' => [$char1, $char2],
                'dialogue' => $response['choices'][0]['message']['content'],
                'situation' => $situation,
                'emotion' => $emotion
            ];
        }
        
        return $dialogues;
    }
}
```

#### 2. 多模态内容生成工作流
```php
class MultiModalWorkflow
{
    private $scriptGen;
    private $imageAPI;
    private $videoAPI;
    
    public function __construct($config)
    {
        $this->scriptGen = new IntelligentScriptGenerator($config);
        $this->imageAPI = new MiniMaxImageAPI($config);
        $this->videoAPI = new MiniMaxVideoAPI($config);
    }
    
    /**
     * 从概念到视频的完整流程
     */
    public function conceptToVideo($concept, $style = 'realistic')
    {
        // 1. 生成剧本
        $scriptData = $this->scriptGen->generateFullScript($concept);
        
        // 2. 解析分镜场景
        $scenes = $this->parseStoryboard($scriptData['storyboard']);
        
        // 3. 生成分镜图像
        $images = $this->generateSceneImages($scenes, $style);
        
        // 4. 生成视频片段
        $videos = $this->generateSceneVideos($images);
        
        return [
            'script' => $scriptData,
            'scenes' => $scenes,
            'images' => $images,
            'videos' => $videos,
            'workflow_metadata' => [
                'concept' => $concept,
                'style' => $style,
                'total_scenes' => count($scenes),
                'completed_at' => date('Y-m-d H:i:s')
            ]
        ];
    }
    
    private function parseStoryboard($storyboard)
    {
        // 简化的分镜解析
        $scenes = [];
        $lines = explode("\n", $storyboard);
        
        foreach ($lines as $line) {
            if (preg_match('/镜头\s*(\d+)[:：](.+)/', $line, $matches)) {
                $scenes[] = [
                    'shot_number' => $matches[1],
                    'description' => trim($matches[2])
                ];
            }
        }
        
        return $scenes;
    }
    
    private function generateSceneImages($scenes, $style)
    {
        $images = [];
        
        foreach ($scenes as $scene) {
            try {
                $prompt = "高质量电影分镜，{$style}风格，" . $scene['description'];
                
                $response = $this->imageAPI->textToImage($prompt, [
                    'width' => 1920,
                    'height' => 1080,
                    'style' => $style,
                    'num_inference_steps' => 30
                ]);
                
                $images[] = [
                    'scene' => $scene,
                    'image_url' => $response['data'][0]['url'],
                    'status' => 'success'
                ];
            } catch (Exception $e) {
                $images[] = [
                    'scene' => $scene,
                    'error' => $e->getMessage(),
                    'status' => 'failed'
                ];
            }
        }
        
        return $images;
    }
    
    private function generateSceneVideos($images)
    {
        $videos = [];
        
        foreach ($images as $image) {
            if ($image['status'] !== 'success') {
                $videos[] = [
                    'scene' => $image['scene'],
                    'status' => 'skipped',
                    'reason' => 'image_generation_failed'
                ];
                continue;
            }
            
            try {
                $response = $this->videoAPI->imageToVideo($image['image_url'], '', [
                    'duration' => 4,
                    'fps' => 25
                ]);
                
                // 等待视频生成完成
                $taskId = $response['task_id'];
                $videoResult = $this->waitForVideoCompletion($taskId);
                
                $videos[] = [
                    'scene' => $image['scene'],
                    'source_image' => $image['image_url'],
                    'video_url' => $videoResult['video_url'],
                    'status' => 'success'
                ];
            } catch (Exception $e) {
                $videos[] = [
                    'scene' => $image['scene'],
                    'error' => $e->getMessage(),
                    'status' => 'failed'
                ];
            }
        }
        
        return $videos;
    }
    
    private function waitForVideoCompletion($taskId, $maxWaitTime = 600)
    {
        $startTime = time();
        
        while (time() - $startTime < $maxWaitTime) {
            $status = $this->videoAPI->getVideoStatus($taskId);
            
            if ($status['status'] === 'completed') {
                return $status['result'];
            } elseif ($status['status'] === 'failed') {
                throw new Exception('视频生成失败: ' . $status['error']);
            }
            
            sleep(10);
        }
        
        throw new Exception('视频生成超时');
    }
}
```

### 使用示例

#### 完整的内容创作流程
```php
// 初始化配置
$config = new MiniMaxConfig('your-api-key', 'your-group-id');
$workflow = new MultiModalWorkflow($config);

// 从概念生成完整视频
$concept = "一个关于人工智能觉醒的科幻故事";
$result = $workflow->conceptToVideo($concept, 'cyberpunk');

// 输出结果
echo "剧本生成完成\n";
echo "生成了 " . count($result['images']) . " 个分镜图像\n";
echo "生成了 " . count($result['videos']) . " 个视频片段\n";

// 保存结果
file_put_contents('script.txt', $result['script']['script']);
foreach ($result['videos'] as $index => $video) {
    if ($video['status'] === 'success') {
        echo "视频片段 {$index}: {$video['video_url']}\n";
    }
}
```

### 错误处理和最佳实践

#### 1. 错误处理
```php
class MiniMaxErrorHandler
{
    const ERROR_CODES = [
        400 => '请求参数错误',
        401 => 'API密钥无效',
        403 => '权限不足',
        429 => '请求频率超限',
        500 => '服务器内部错误'
    ];
    
    public static function handleError($httpCode, $response)
    {
        $errorMsg = self::ERROR_CODES[$httpCode] ?? '未知错误';
        
        if (isset($response['error'])) {
            $errorMsg .= ': ' . $response['error']['message'];
        }
        
        switch ($httpCode) {
            case 429:
                sleep(5);
                return 'retry';
            default:
                throw new Exception($errorMsg);
        }
    }
}
```

#### 2. 最佳实践
1. **合理设置参数**: 根据内容类型调整temperature和max_tokens
2. **流式处理**: 对于长文本生成使用流式接口
3. **批量优化**: 合理安排任务队列避免频率限制
4. **缓存策略**: 对相似请求实施缓存机制
5. **错误恢复**: 实施完善的重试和容错机制

海螺AI (MiniMax) 提供了强大的多模态AI能力，特别适合需要文本、图像、视频一体化创作的应用场景。
