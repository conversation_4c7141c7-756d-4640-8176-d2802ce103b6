<?php
/**
 * 系统控制器
 * 提供系统管理、配置和监控功能
 */

class SystemController
{
    private $logger;
    
    public function __construct()
    {
        $this->logger = new Logger();
    }
    
    /**
     * 健康检查接口
     * GET /health
     */
    public function healthCheck($params = [])
    {
        $startTime = microtime(true);

        // 业务状态模拟 - 增强：添加系统健康检查的各种状态
        $statuses = ['healthy', 'degraded', 'unhealthy', 'maintenance'];
        $weights = [70, 15, 10, 5]; // 健康70%，降级15%，不健康10%，维护5%

        $systemStatus = $this->weightedRandom($statuses, $weights);

        // 根据系统状态生成相应的检查结果
        $checks = $this->generateHealthChecks($systemStatus);

        $responseTime = round((microtime(true) - $startTime) * 1000, 2);

        $response = [
            'status' => $systemStatus,
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => AIAPI_VERSION,
            'uptime' => $this->getUptime(),
            'response_time' => $responseTime . 'ms',
            'checks' => $checks
        ];

        // 根据状态添加特定信息
        switch ($systemStatus) {
            case 'healthy':
                $response['message'] = '所有系统组件运行正常';
                break;

            case 'degraded':
                $response['message'] = '部分系统组件性能下降，但服务可用';
                $response['warnings'] = $this->generateDegradedWarnings();
                break;

            case 'unhealthy':
                $response['message'] = '系统存在严重问题，服务可能不稳定';
                $response['errors'] = $this->generateUnhealthyErrors();
                http_response_code(503);
                break;

            case 'maintenance':
                $response['message'] = '系统正在维护中，部分功能可能不可用';
                $response['maintenance_info'] = [
                    'start_time' => date('Y-m-d H:i:s', time() - rand(300, 3600)),
                    'estimated_end' => date('Y-m-d H:i:s', time() + rand(300, 1800)),
                    'affected_services' => ['DeepSeek API', 'LiblibAI API'][rand(0, 1)]
                ];
                http_response_code(503);
                break;
        }

        return $response;
    }
    
    /**
     * 获取系统配置信息
     * GET /config
     */
    public function getConfig($params = [])
    {
        $config = getAIConfig();
        
        // 隐藏敏感信息
        foreach ($config['platforms'] as $platform => &$platformConfig) {
            if (isset($platformConfig['real_api']['api_key'])) {
                $platformConfig['real_api']['api_key'] = '***';
            }
        }
        
        return [
            'version' => AIAPI_VERSION,
            'debug_mode' => AIAPI_DEBUG,
            'log_level' => AIAPI_LOG_LEVEL,
            'platforms' => $config['platforms'],
            'business_flow' => $config['business_flow'],
            'system_info' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => $this->formatBytes(memory_get_usage(true)),
                'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
                'disk_free' => $this->formatBytes(disk_free_space(__DIR__))
            ]
        ];
    }
    
    /**
     * 获取所有接口列表 - 增强：添加服务状态模拟
     * GET /routes
     */
    public function listRoutes($params = [])
    {
        global $routes;

        // 业务状态模拟 - 增强：添加路由服务的各种状态
        $statuses = ['available', 'partial_outage', 'maintenance', 'degraded'];
        $weights = [75, 10, 5, 10]; // 可用75%，部分故障10%，维护5%，降级10%

        $serviceStatus = $this->weightedRandom($statuses, $weights);

        $routeList = [];
        foreach ($routes as $route) {
            $routeInfo = [
                'method' => $route['method'],
                'path' => $route['path'],
                'description' => $route['description'] ?? '',
                'controller' => $route['controller'],
                'action' => $route['action']
            ];

            // 根据服务状态添加接口状态信息
            switch ($serviceStatus) {
                case 'available':
                    $routeInfo['status'] = 'active';
                    break;

                case 'partial_outage':
                    // 随机标记部分接口不可用
                    if (rand(1, 100) <= 20) { // 20%的接口不可用
                        $routeInfo['status'] = 'unavailable';
                        $routeInfo['error'] = 'Service temporarily unavailable';
                    } else {
                        $routeInfo['status'] = 'active';
                    }
                    break;

                case 'maintenance':
                    // 维护模式下，部分接口不可用
                    if (strpos($route['path'], '/deepseek/') !== false) {
                        $routeInfo['status'] = 'maintenance';
                        $routeInfo['message'] = 'Under maintenance';
                    } else {
                        $routeInfo['status'] = 'active';
                    }
                    break;

                case 'degraded':
                    $routeInfo['status'] = 'degraded';
                    $routeInfo['warning'] = 'Performance may be affected';
                    break;
            }

            $routeList[] = $routeInfo;
        }

        $response = [
            'service_status' => $serviceStatus,
            'total_routes' => count($routeList),
            'routes' => $routeList,
            'base_url' => 'http://localhost/php/aiapi',
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // 根据服务状态添加特定信息
        switch ($serviceStatus) {
            case 'available':
                $response['message'] = '所有接口服务正常';
                break;

            case 'partial_outage':
                $response['message'] = '部分接口服务异常，请检查具体接口状态';
                $response['affected_routes'] = array_filter($routeList, function($route) {
                    return isset($route['status']) && $route['status'] === 'unavailable';
                });
                break;

            case 'maintenance':
                $response['message'] = '系统维护中，部分接口可能不可用';
                $response['maintenance_info'] = [
                    'start_time' => date('Y-m-d H:i:s', time() - rand(300, 1800)),
                    'estimated_end' => date('Y-m-d H:i:s', time() + rand(600, 3600)),
                    'affected_services' => ['DeepSeek API']
                ];
                break;

            case 'degraded':
                $response['message'] = '服务性能下降，响应可能较慢';
                $response['performance_warning'] = '建议稍后重试或联系技术支持';
                break;
        }

        return $response;
    }
    
    /**
     * 切换平台模式（模拟/真实）
     * POST /config/toggle/{platform}
     */
    public function toggleMode($params = [])
    {
        $platform = $params['platform'] ?? null;
        
        if (!$platform) {
            return HttpHelper::errorResponse(
                'MISSING_PLATFORM',
                '请指定要切换的平台'
            );
        }
        
        $config = getAIConfig($platform);
        if (!$config) {
            return HttpHelper::errorResponse(
                'INVALID_PLATFORM',
                '不支持的平台: ' . $platform
            );
        }
        
        $data = HttpHelper::getRequestBody();
        $mockEnabled = $data['mock_enabled'] ?? null;
        
        if ($mockEnabled === null) {
            return HttpHelper::errorResponse(
                'MISSING_PARAMETER',
                '请指定 mock_enabled 参数'
            );
        }
        
        // 这里应该更新配置文件，简化实现只返回状态
        $this->logger->info("平台模式切换", [
            'platform' => $platform,
            'mock_enabled' => $mockEnabled,
            'previous_mode' => $config['mock_enabled'] ? 'mock' : 'real'
        ]);
        
        return [
            'platform' => $platform,
            'mock_enabled' => (bool)$mockEnabled,
            'mode' => $mockEnabled ? 'mock' : 'real',
            'message' => "平台 {$platform} 已切换到" . ($mockEnabled ? '模拟' : '真实') . "模式"
        ];
    }
    
    /**
     * 获取日志统计
     * GET /logs/stats
     */
    public function getLogStats($params = [])
    {
        $date = $params['date'] ?? date('Y-m-d');
        $stats = $this->logger->getLogStats($date);

        return $stats;
    }

    /**
     * 获取性能监控报告
     * GET /performance/report
     */
    public function getPerformanceReport($params = [])
    {
        $date = $params['date'] ?? date('Y-m-d');
        $performanceMonitor = PerformanceMonitor::getInstance();

        return $performanceMonitor->generatePerformanceReport($date);
    }

    /**
     * 获取缓存统计
     * GET /cache/stats
     */
    public function getCacheStats($params = [])
    {
        $cache = new CacheManager();

        return [
            'cache_stats' => $cache->getStats(),
            'system_info' => [
                'cache_dir' => AIAPI_CACHE_DIR,
                'cache_ttl' => AIAPI_CACHE_TTL,
                'cache_enabled' => true
            ]
        ];
    }

    /**
     * 清理缓存
     * POST /cache/clear
     */
    public function clearCache($params = [])
    {
        $cache = new CacheManager();
        $data = HttpHelper::getRequestBody();

        if (isset($data['type']) && $data['type'] === 'expired') {
            $cleaned = $cache->cleanup();
            $message = "已清理 {$cleaned} 个过期缓存文件";
        } else {
            $cleared = $cache->clear();
            $message = "已清空 {$cleared} 个缓存文件";
        }

        return [
            'success' => true,
            'message' => $message,
            'timestamp' => time()
        ];
    }

    /**
     * 生成API文档
     * GET /docs/generate
     */
    public function generateApiDocs($params = [])
    {
        $format = $params['format'] ?? 'json';
        $docGenerator = new ApiDocGenerator();

        if ($format === 'markdown' || $format === 'md') {
            $filename = $docGenerator->saveDocumentation('markdown');
            $content = $docGenerator->generateMarkdownDoc();

            return [
                'format' => 'markdown',
                'filename' => basename($filename),
                'content' => $content,
                'generated_at' => date('Y-m-d H:i:s')
            ];
        } else {
            $docs = $docGenerator->generateFullDocumentation();
            $filename = $docGenerator->saveDocumentation('json');

            return [
                'format' => 'json',
                'filename' => basename($filename),
                'documentation' => $docs,
                'generated_at' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    /**
     * 检查API服务状态
     */
    private function checkApiService()
    {
        try {
            // 检查基本功能
            $testData = ['test' => true];
            $encoded = json_encode($testData);
            $decoded = json_decode($encoded, true);
            
            return [
                'healthy' => $decoded['test'] === true,
                'message' => 'API服务正常运行'
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => 'API服务异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查日志系统状态
     */
    private function checkLogSystem()
    {
        try {
            $logDir = AIAPI_LOG_DIR;
            $writable = is_writable($logDir);
            
            return [
                'healthy' => $writable,
                'message' => $writable ? '日志系统正常' : '日志目录不可写'
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => '日志系统异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查配置系统状态
     */
    private function checkConfigSystem()
    {
        try {
            $config = getAIConfig();
            $hasConfig = !empty($config['platforms']);
            
            return [
                'healthy' => $hasConfig,
                'message' => $hasConfig ? '配置系统正常' : '配置文件缺失'
            ];
        } catch (Exception $e) {
            return [
                'healthy' => false,
                'message' => '配置系统异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查各平台状态
     */
    private function checkPlatforms()
    {
        $config = getAIConfig();
        $platforms = [];
        
        foreach ($config['platforms'] as $platform => $platformConfig) {
            $platforms[$platform] = [
                'healthy' => true,
                'mode' => $platformConfig['mock_enabled'] ? 'mock' : 'real',
                'message' => '平台配置正常'
            ];
        }
        
        return [
            'healthy' => true,
            'message' => '所有平台配置正常',
            'platforms' => $platforms
        ];
    }
    
    /**
     * 获取系统运行时间
     */
    private function getUptime()
    {
        // 简化实现，返回当前时间
        return date('Y-m-d H:i:s');
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 加权随机选择
     */
    private function weightedRandom($values, $weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);

        $currentWeight = 0;
        for ($i = 0; $i < count($values); $i++) {
            $currentWeight += $weights[$i];
            if ($random <= $currentWeight) {
                return $values[$i];
            }
        }

        return $values[0];
    }

    /**
     * 根据系统状态生成健康检查结果
     */
    private function generateHealthChecks($systemStatus)
    {
        $baseChecks = [
            'api_service' => [
                'healthy' => true,
                'message' => 'API服务正常运行'
            ],
            'log_system' => [
                'healthy' => true,
                'message' => '日志系统正常'
            ],
            'config_system' => [
                'healthy' => true,
                'message' => '配置系统正常'
            ],
            'platforms' => [
                'healthy' => true,
                'message' => '所有平台配置正常',
                'platforms' => [
                    'deepseek' => ['healthy' => true, 'mode' => 'mock', 'message' => '平台配置正常'],
                    'liblib' => ['healthy' => true, 'mode' => 'mock', 'message' => '平台配置正常'],
                    'kling' => ['healthy' => true, 'mode' => 'mock', 'message' => '平台配置正常'],
                    'minimax' => ['healthy' => true, 'mode' => 'mock', 'message' => '平台配置正常']
                ]
            ]
        ];

        // 根据系统状态调整检查结果
        switch ($systemStatus) {
            case 'degraded':
                // 随机选择一个组件降级
                $degradedComponent = ['api_service', 'log_system'][rand(0, 1)];
                $baseChecks[$degradedComponent]['healthy'] = false;
                $baseChecks[$degradedComponent]['message'] = '性能下降，响应缓慢';
                $baseChecks[$degradedComponent]['warning'] = '建议监控该组件状态';
                break;

            case 'unhealthy':
                // 多个组件异常
                $baseChecks['api_service']['healthy'] = false;
                $baseChecks['api_service']['message'] = 'API服务异常';
                $baseChecks['platforms']['healthy'] = false;
                $baseChecks['platforms']['message'] = '部分平台连接异常';
                break;

            case 'maintenance':
                // 维护模式
                $baseChecks['platforms']['healthy'] = false;
                $baseChecks['platforms']['message'] = '平台维护中';
                $baseChecks['platforms']['platforms']['deepseek']['healthy'] = false;
                $baseChecks['platforms']['platforms']['deepseek']['message'] = '维护中';
                break;
        }

        return $baseChecks;
    }

    /**
     * 生成降级状态的警告信息
     */
    private function generateDegradedWarnings()
    {
        $warnings = [
            'API响应时间增加，建议监控',
            '部分平台连接不稳定',
            '日志写入延迟，但不影响核心功能',
            '系统负载较高，建议优化'
        ];

        return [array_rand(array_flip($warnings))];
    }

    /**
     * 生成不健康状态的错误信息
     */
    private function generateUnhealthyErrors()
    {
        $errors = [
            [
                'code' => 'API_SERVICE_DOWN',
                'message' => 'API服务无响应',
                'severity' => 'critical'
            ],
            [
                'code' => 'PLATFORM_CONNECTION_FAILED',
                'message' => '无法连接到AI平台',
                'severity' => 'high'
            ],
            [
                'code' => 'DATABASE_CONNECTION_ERROR',
                'message' => '数据库连接异常',
                'severity' => 'critical'
            ]
        ];

        return [$errors[rand(0, count($errors) - 1)]];
    }
}
