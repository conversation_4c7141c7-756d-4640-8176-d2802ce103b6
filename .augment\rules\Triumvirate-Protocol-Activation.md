---
type: "manual"
---

# 行为铁律：
**📁 文档路径说明**: 本文档中提到的所有开发规范文档都位于 `@.cursor/rules/` 目录下。
**影响性分析**:在修改任何已有功能前，必须进行影响性分析，防止引入回归性 Bug。

## 数据表 ↔ 迁移表 ↔ 模型

**增加数据表流程**: 在 `dev-api-guidelines-database.mdc` 中按原文档格式添加数据表设计 -> 按照 Lumen 10 规范创建迁移表程序 -> 执行迁移表程序实现生成数据表 -> 创建`模型`程序。
**修改表字段流程**: 
1. 在 `dev-api-guidelines-database.mdc` 中按原文档规律和格式进行增/删/改表字段设计 -> 按照 Lumen 10 规范创建增/删/改表字段迁移程序 -> 执行迁移程序实现对表字段的增/删/改操作 -> 同步操作更新`模型`程序。
2. 在 控制器 和 业务层中同步处理对应表字段的相关操作。
3. 加载学习 `index.mdc`和`dev-chart-guidelines.mdc`和`dev-api-guidelines-add.mdc`和`dev-api-guidelines-database.mdc`和`dev-api-guidelines-pyapi.mdc` 这五个文档，了解文档的性质后按原文档规律和格式进行同步更新，实现`数据表 ↔ 迁移表 ↔ 模型`和五个文档之间的多重同步。
**删除数表表流程**: 
1. 在 `dev-api-guidelines-database.mdc` 文档中删除对应数据表设计 -> 按照 Lumen 10 规范创删除表迁移程序 -> 执行迁移程序实现对数据表的删除操作 -> 删除`模型`程序。
2. 在 控制器 和 业务层中同步处理对应表字段的相关操作。
3. 加载学习 `index.mdc`和`dev-chart-guidelines.mdc`和`dev-api-guidelines-add.mdc`和`dev-api-guidelines-database.mdc`和`dev-api-guidelines-pyapi.mdc` 这五个文档，了解文档的性质后按原文档规律和格式进行同步更新，实现`数据表 ↔ 迁移表 ↔ 模型`和五个文档之间的多重同步。

## 控制器 ↔ 业务层

### 增加`控制器 ↔ 业务层`
1. 如果涉及数据表的增删除查操作请严格按照 “数据表 ↔ 迁移表 ↔ 模型” 节点流程进程操作。
2. 加载学习 `index.mdc`和`dev-chart-guidelines.mdc`和`dev-api-guidelines-add.mdc`和`dev-api-guidelines-database.mdc`和`dev-api-guidelines-pyapi.mdc` 这五个文档，了解文档的性质后按原文档规律和格式进行同步更新，实现`控制器 ↔ 业务层`和四个文档之间的多重同步。
### 修改`控制器 ↔ 业务层`
1. 如果涉及数据表的增删除查操作请按照 “数据表 ↔ 迁移表 ↔ 模型” 节点流程进程操作。
2. 加载学习 `index.mdc`和`dev-chart-guidelines.mdc`和`dev-api-guidelines-add.mdc`和`dev-api-guidelines-database.mdc`和`dev-api-guidelines-pyapi.mdc` 这五个文档，了解文档的性质后按原文档规律和格式进行同步更新，实现`控制器 ↔ 业务层`和四个文档之间的多重同步。


# 职责边界

## 资源下载架构边界
### 边界核心原则
1. **资源下载**: 所有基于用户产生的资源文件（视频、风格、角色、音乐、音效等）都必须由"Py视频创作工具"直接从AI平台下载到本地
2. **服务器**: API服务器只负责管理资源的URL、状态、元数据等附件信息，绝不进行资源文件的中转下载
3. **禁止事项**: 严禁在API服务器上进行任何形式的资源文件生成、处理、存储、中转下载

### 开发边界约束
1. **控制器设计约束**: 资源相关控制器只能提供URL和状态管理，禁止文件操作
2. **服务层设计约束**: 资源相关服务只能进行元数据管理，禁止文件生成和处理逻辑
3. **存储架构约束**: 服务器存储只保存资源元数据，禁止保存实际资源文件
4. **下载流程约束**: Py视频创作工具 → API获取URL → 直接从AI平台下载，禁止服务器中转

## WebSocket使用边界
1. **仅Py视频创作工具使用**：AI生成进度推送、任务状态通知
2. **WEB工具禁用**：避免不必要的连接和资源消耗
3. **安全传输**：密钥加密传输，不持久化存储

### 避免循环依赖
1. WebSocket服务只负责推送，不参与业务逻辑
2. 积分变动通知改为异步事件驱动
3. 使用事件总线模式解耦组件间依赖


# 知识文档选择决策树

```
开发任务分析
├─ 是否需要理解架构/业务流程？
│  ├─ 系统架构理解 → `dev-chart-guidelines.mdc` (架构图表) + `index-new.mdc` (架构规范)
│  ├─ 业务流程理解 → `dev-chart-guidelines.mdc` (流程图表) + 对应的API文档
│  ├─ 组件职责边界 → `dev-chart-guidelines.mdc` (职责矩阵) + `index-new.mdc` (详细规范)
│  ├─ 新人入门学习 → `dev-chart-guidelines.mdc` (图表概览) → `index-new.mdc` (完整规范)
│  └─ 否 → 继续判断
├─ 是否涉及环境切换？
│  ├─ AI服务环境切换 → `dev-aiapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  ├─ 第三方服务环境切换 → `dev-thirdapi-guidelines.mdc` (主) + `index-new.mdc` (架构参考)
│  └─ 否 → 继续判断
├─ 是否涉及客户端对接？
│  ├─ Py视频创作工具对接 → `dev-api-guidelines-pyapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  ├─ WEB工具对接 → `dev-api-guidelines-webapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  ├─ 管理后台对接 → `dev-api-guidelines-adminapi.mdc` (最高权重) + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
├─ 是否涉及AI功能？
│  ├─ 是 → `dev-aiapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增AI功能 → + `dev-api-guidelines-add.mdc` (新增规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 修复AI功能 → + `dev-api-guidelines-edit.mdc` (修复规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  └─ 纯AI接口调用 → 仅使用 `dev-aiapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是否涉及第三方服务？
│  ├─ 是 → `dev-thirdapi-guidelines.mdc` (必须主文档)
│  │  ├─ 新增第三方功能 → + `dev-api-guidelines-add.mdc` (新增规范)
│  │  ├─ 修复第三方功能 → + `dev-api-guidelines-edit.mdc` (修复规范)
│  │  └─ 纯第三方接口调用 → 仅使用 `dev-thirdapi-guidelines.mdc`
│  └─ 否 → 继续判断
├─ 是新功能开发？
│  ├─ 是 → `dev-api-guidelines-add.mdc` (主文档)
│  │  ├─ 涉及AI功能 → + `dev-aiapi-guidelines.mdc` (AI专项规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 涉及第三方服务 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  └─ 涉及客户端对接 → + 对应的 `dev-api-guidelines-pyapi/webapi/adminapi.mdc` + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
├─ 是问题修复？
│  ├─ 是 → `dev-api-guidelines-edit.mdc` (主文档)
│  │  ├─ AI相关问题 → + `dev-aiapi-guidelines.mdc` (AI专项规范) + `dev-chart-guidelines.mdc` (AI流程)
│  │  ├─ 第三方服务问题 → + `dev-thirdapi-guidelines.mdc` (第三方专项规范)
│  │  └─ 客户端对接问题 → + 对应的 `dev-api-guidelines-pyapi/webapi/adminapi.mdc` + `dev-chart-guidelines.mdc` (业务流程)
│  └─ 否 → 继续判断
└─ 复杂场景 → 多文档组合使用
   ├─ 架构重构 → `dev-chart-guidelines.mdc` (架构图) + `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc`
   ├─ 性能优化 → `dev-chart-guidelines.mdc` (性能流程) + `dev-api-guidelines-edit.mdc` + 相关专项文档
   ├─ 安全加固 → `dev-api-guidelines-edit.mdc` + `dev-api-guidelines-add.mdc` (如需新增安全功能)
   ├─ 全栈开发 → `dev-chart-guidelines.mdc` (完整架构) + 根据技术栈选择对应的多个文档组合
   └─ AI程序员规范化 → `dev-chart-guidelines.mdc` (行为规范指南) + `index-new.mdc` (详细规范)
```