<?php
/**
 * LiblibAI API控制器
 *
 * 🚨 架构边界规范：
 * ✅ 本控制器仅进行模拟，不会向真实LiblibAI平台发起任何网络请求
 * ✅ 严格按照LiblibAI官方API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实AI生成内容
 *
 * 业务职责：图像生成专业平台
 * 支持功能：
 * - 星流Star-3 Alpha (文生图/图生图)
 * - LiblibAI自定义模型 (文生图/图生图)
 * - IMG1智能算法 (文生图/图生图/局部重绘)
 * - F.1 Kontext (主体参考/风格迁移)
 * - ComfyUI工作流执行
 * - 视频生成 (图生视频/文生视频)
 * - 图像处理 (放大/修复/背景移除/风格转换)
 * - 文件上传管理
 */

class LiblibController
{
    private $logger;
    private $config;
    private $mockData;
    private $errorCodes;
    private $platform = 'liblib';

    public function __construct()
    {
        global $aiapi_config;
        $this->logger = new Logger();
        $this->config = $aiapi_config['platforms'][$this->platform];
        $this->mockData = $aiapi_config['mock_response_data'][$this->platform];
        $this->errorCodes = $aiapi_config['error_codes'];
    }
    
    // ==================== 🔥 LongDev1新增 - IMG1智能算法接口 ====================

    /**
     * IMG1智能算法文生图接口 (官方路径)
     * POST /api/open/img1/text2img
     * 🔥 LongDev1新增 - 新一代AI算法，提供更高质量的图像生成
     */
    public function img1_text2img($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证IMG1参数
            $this->validateIMG1Parameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', 'IMG1文生图失败，请重试');
            }

            // 生成IMG1响应
            $response = $this->generateIMG1Text2ImgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/img1/text2img',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/img1/text2img', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * IMG1智能算法图生图接口 (官方路径)
     * POST /api/open/img1/img2img
     * 🔥 LongDev1新增 - IMG1图像到图像转换
     */
    public function img1_img2img($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt', 'imageUrl']);

            // 验证IMG1参数
            $this->validateIMG1Parameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', 'IMG1图生图失败，请重试');
            }

            // 生成IMG1响应
            $response = $this->generateIMG1Img2ImgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/img1/img2img',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/img1/img2img', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * IMG1局部重绘接口 (官方路径)
     * POST /api/open/img1/inpaint
     * 🔥 LongDev1新增 - 支持蒙版重绘，精确控制重绘区域
     */
    public function img1_inpaint($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt', 'imageUrl', 'maskUrl']);

            // 验证IMG1参数
            $this->validateIMG1Parameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', 'IMG1局部重绘失败，请重试');
            }

            // 生成IMG1响应
            $response = $this->generateIMG1InpaintResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/img1/inpaint',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/img1/inpaint', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * IMG1任务结果查询接口 (官方路径)
     * GET /api/open/img1/query
     * 🔥 LongDev1新增 - 查询IMG1生图结果
     */
    public function img1_query($params = [])
    {
        $startTime = microtime(true);

        try {
            $taskId = $params['taskId'] ?? $_GET['taskId'] ?? null;

            if (!$taskId) {
                return $this->generateLiblibErrorResponse('MISSING_TASK_ID', '缺少任务ID');
            }

            // 验证任务ID格式
            if (!$this->isValidTaskId($taskId)) {
                return $this->generateLiblibErrorResponse('INVALID_TASK_ID', '无效的任务ID格式');
            }

            // 生成IMG1任务状态响应
            $response = $this->generateIMG1TaskStatusResponse($taskId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/img1/query',
                'GET',
                ['task_id' => $taskId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/img1/query', $e->getMessage(), ['task_id' => $taskId ?? 'unknown']);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 星流文生图接口 (官方路径)
     * POST /api/open/xingliu/text2img
     * 🔥 LongDev1修正 - 更新参数格式和响应字段
     */
    public function xingliu_text2img($params = [])
    {
        $startTime = microtime(true);

        try {
            // 获取请求数据
            $data = HttpHelper::getRequestBody();

            // 验证必需参数 - 星流文生图只需要prompt
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证提示词
            $prompt = $data['prompt'];
            if (empty($prompt)) {
                return $this->generateLiblibErrorResponse('INVALID_PROMPT', '提示词不能为空');
            }

            // 🔥 LongDev1新增 - 验证星流参数
            $this->validateXingliuParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('SERVICE_UNAVAILABLE', '服务暂时不可用，请稍后重试');
            }

            // 生成星流文生图响应
            $response = $this->generateXingliuText2ImgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/xingliu/text2img',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/xingliu/text2img', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }
    
    // ==================== 🔥 LongDev1新增 - 视频生成接口 ====================

    /**
     * 图像生成视频接口 (官方路径)
     * POST /v1/videos/img2video
     * 🔥 LongDev1新增 - 将图像转换为视频
     */
    public function img2video($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['image']);

            // 验证视频参数
            $this->validateVideoParameters($data);

            // 模拟网络延迟 (视频生成需要更长时间)
            HttpHelper::simulateDelay($this->platform, 2.0);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '图像生成视频失败，请重试');
            }

            // 生成视频响应
            $response = $this->generateImg2VideoResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/videos/img2video',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/videos/img2video', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 文本生成视频接口 (官方路径)
     * POST /v1/videos/text2video
     * 🔥 LongDev1新增 - 根据文本描述生成视频
     */
    public function text2video($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证视频参数
            $this->validateVideoParameters($data);

            // 模拟网络延迟 (视频生成需要更长时间)
            HttpHelper::simulateDelay($this->platform, 2.5);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '文本生成视频失败，请重试');
            }

            // 生成视频响应
            $response = $this->generateText2VideoResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/videos/text2video',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/videos/text2video', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 视频生成状态查询接口 (官方路径)
     * GET /v1/videos/status/{taskId}
     * 🔥 LongDev1新增 - 查询视频生成状态
     */
    public function video_status($params = [])
    {
        $startTime = microtime(true);

        try {
            $taskId = $params['taskId'] ?? $_GET['taskId'] ?? null;

            if (!$taskId) {
                return $this->generateLiblibErrorResponse('MISSING_TASK_ID', '缺少任务ID');
            }

            // 验证任务ID格式
            if (!$this->isValidTaskId($taskId)) {
                return $this->generateLiblibErrorResponse('INVALID_TASK_ID', '无效的任务ID格式');
            }

            // 生成视频任务状态响应
            $response = $this->generateVideoTaskStatusResponse($taskId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/videos/status/' . $taskId,
                'GET',
                ['task_id' => $taskId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/videos/status', $e->getMessage(), ['task_id' => $taskId ?? 'unknown']);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 星流Star-3 Alpha图像生成
     * POST /liblib/star/generate
     * 🔥 LongDev1修正 - 保持向后兼容
     */
    public function starGenerate($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证参数范围
            $this->validateStarParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '图像生成失败，请重试');
            }

            // 生成星流响应
            $response = $this->generateStarResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'star/generate',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'star/generate', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }
    
    // ==================== 🔥 LongDev1新增 - 图像处理接口 ====================

    /**
     * 图像放大接口 (官方路径)
     * POST /v1/images/upscale
     * 🔥 LongDev1新增 - 图像超分辨率放大
     */
    public function upscale_image($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['image']);

            // 验证放大参数
            $this->validateUpscaleParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '图像放大失败，请重试');
            }

            // 生成放大响应
            $response = $this->generateUpscaleResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/images/upscale',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/images/upscale', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 图像修复接口 (官方路径)
     * POST /v1/images/inpaint
     * 🔥 LongDev1新增 - 图像修复和填充
     */
    public function inpaint_image($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['image', 'mask', 'prompt']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '图像修复失败，请重试');
            }

            // 生成修复响应
            $response = $this->generateInpaintResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/images/inpaint',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/images/inpaint', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 背景移除接口 (官方路径)
     * POST /v1/images/remove-bg
     * 🔥 LongDev1新增 - 智能背景移除
     */
    public function remove_background($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['image']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '背景移除失败，请重试');
            }

            // 生成背景移除响应
            $response = $this->generateRemoveBgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/images/remove-bg',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/images/remove-bg', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 风格转换接口 (官方路径)
     * POST /v1/images/style-transfer
     * 🔥 LongDev1新增 - 图像风格迁移
     */
    public function style_transfer($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['content_image', 'style_image']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '风格转换失败，请重试');
            }

            // 生成风格转换响应
            $response = $this->generateStyleTransferResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/images/style-transfer',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/images/style-transfer', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 任务状态查询
     * GET /liblib/tasks/{taskId}
     * 🔥 LongDev1修正 - 保持向后兼容
     */
    public function getTaskStatus($params = [])
    {
        $startTime = microtime(true);

        try {
            $taskId = $params['taskId'] ?? null;

            if (!$taskId) {
                return $this->generateLiblibErrorResponse('MISSING_TASK_ID', '缺少任务ID');
            }

            // 验证任务ID格式
            if (!$this->isValidTaskId($taskId)) {
                return $this->generateLiblibErrorResponse('INVALID_TASK_ID', '无效的任务ID格式');
            }

            // 模拟任务状态
            $response = $this->generateTaskStatusResponse($taskId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'tasks/' . $taskId,
                'GET',
                ['task_id' => $taskId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'tasks/status', $e->getMessage(), ['task_id' => $taskId ?? 'unknown']);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 生成工作流响应
     */
    private function generateWorkflowResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('liblib');
        $workflowId = $data['workflow_id'];
        $prompt = $data['prompt'];
        
        // 根据工作流类型生成不同的响应
        $workflowType = $this->getWorkflowType($workflowId);
        
        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'workflow_id' => $workflowId,
                'workflow_type' => $workflowType,
                'status' => 'submitted',
                'prompt' => $prompt,
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => $this->getEstimatedTime($workflowType),
                'queue_position' => rand(1, 15),
                'parameters' => [
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 1024,
                    'steps' => $data['steps'] ?? 20,
                    'cfg_scale' => $data['cfg_scale'] ?? 7.5,
                    'seed' => $data['seed'] ?? rand(1000000, 9999999),
                    'batch_size' => $data['batch_size'] ?? 1
                ]
            ]
        ];
    }
    
    // ==================== 🔥 LongDev1新增 - IMG1响应生成方法 ====================

    /**
     * 生成IMG1文生图响应
     * 🔥 LongDev1新增 - IMG1智能算法文生图响应
     */
    private function generateIMG1Text2ImgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('img1');

        return [
            'code' => 0,
            'message' => 'IMG1 task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'IMG1-v2.0',
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'negative_prompt' => $data['negativePrompt'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(20, 60), // IMG1需要更长时间
                'queue_position' => rand(1, 12),
                'parameters' => [
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 1024,
                    'imageNum' => $data['imageNum'] ?? 1,
                    'steps' => $data['steps'] ?? 20,
                    'scale' => $data['scale'] ?? 7.5,
                    'sampler' => $data['sampler'] ?? 'DPM++ 2M Karras',
                    'style' => $data['style'] ?? 'realistic',
                    'quality' => $data['quality'] ?? 'high'
                ],
                // 🔥 LongDev1新增 - 官方文档要求的字段
                'pointsCost' => rand(15, 35),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成IMG1图生图响应
     * 🔥 LongDev1新增 - IMG1智能算法图生图响应
     */
    private function generateIMG1Img2ImgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('img1');

        return [
            'code' => 0,
            'message' => 'IMG1 img2img task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'IMG1-v2.0',
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'imageUrl' => $data['imageUrl'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(25, 70),
                'parameters' => [
                    'strength' => $data['strength'] ?? 0.75,
                    'steps' => $data['steps'] ?? 20,
                    'scale' => $data['scale'] ?? 7.5
                ],
                'pointsCost' => rand(18, 40),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成IMG1局部重绘响应
     * 🔥 LongDev1新增 - IMG1局部重绘响应
     */
    private function generateIMG1InpaintResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('img1');

        return [
            'code' => 0,
            'message' => 'IMG1 inpaint task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'IMG1-v2.0',
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'imageUrl' => $data['imageUrl'],
                'maskUrl' => $data['maskUrl'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(30, 80),
                'parameters' => [
                    'strength' => $data['strength'] ?? 0.8,
                    'inpaintMode' => $data['inpaintMode'] ?? 'fill'
                ],
                'pointsCost' => rand(20, 45),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成IMG1任务状态响应
     * 🔥 LongDev1新增 - IMG1任务状态查询响应
     */
    private function generateIMG1TaskStatusResponse($taskId)
    {
        // 模拟任务进度
        $statuses = ['processing', 'completed', 'failed'];
        $weights = [25, 70, 5]; // 处理中25%，完成70%，失败5%

        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => $status,
                'model' => 'IMG1-v2.0',
                'created_at' => date('Y-m-d H:i:s', time() - rand(30, 300)),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        switch ($status) {
            case 'processing':
                $baseResponse['data']['progress'] = rand(15, 85);
                $baseResponse['data']['estimated_remaining'] = rand(15, 90) . '秒';
                break;

            case 'completed':
                $baseResponse['data']['completed_at'] = date('Y-m-d H:i:s');
                $baseResponse['data']['results'] = $this->generateImageResults($taskId);
                $baseResponse['data']['pointsCost'] = rand(15, 35);
                $baseResponse['data']['accountBalance'] = rand(100, 1000);
                break;

            case 'failed':
                $baseResponse['data']['error'] = [
                    'code' => 'IMG1_GENERATION_FAILED',
                    'message' => 'IMG1图像生成失败，请检查参数或重试'
                ];
                break;
        }

        return $baseResponse;
    }

    /**
     * 生成星流响应
     * 🔥 LongDev1修正 - 更新响应格式
     */
    private function generateStarResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('star');

        return [
            'code' => 0,
            'message' => 'Task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'star-3-alpha',
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'negative_prompt' => $data['negative_prompt'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(15, 45), // 15-45秒
                'queue_position' => rand(1, 8),
                'parameters' => [
                    'style' => $data['style'] ?? 'realistic',
                    'quality' => $data['quality'] ?? 'high',
                    'aspect_ratio' => $data['aspect_ratio'] ?? '1:1',
                    'creativity' => $data['creativity'] ?? 0.7
                ],
                // 🔥 LongDev1新增 - 官方文档要求的字段
                'pointsCost' => rand(8, 25),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }
    
    // ==================== 🔥 LongDev1新增 - 视频生成响应方法 ====================

    /**
     * 生成图像到视频响应
     * 🔥 LongDev1新增 - 图像生成视频响应
     */
    private function generateImg2VideoResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('video');

        return [
            'code' => 0,
            'message' => 'Video generation task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'liblib-video-v1',
                'status' => 'processing',
                'image' => $data['image'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(120, 300), // 视频生成需要2-5分钟
                'queue_position' => rand(1, 20),
                'parameters' => [
                    'duration' => $data['duration'] ?? 5,
                    'fps' => $data['fps'] ?? 24,
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 576,
                    'motion_strength' => $data['motion_strength'] ?? 0.8
                ],
                'pointsCost' => rand(50, 120),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成文本到视频响应
     * 🔥 LongDev1新增 - 文本生成视频响应
     */
    private function generateText2VideoResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('video');

        return [
            'code' => 0,
            'message' => 'Text to video task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'liblib-video-v1',
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'negative_prompt' => $data['negative_prompt'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(180, 400), // 文本生成视频需要3-7分钟
                'queue_position' => rand(1, 25),
                'parameters' => [
                    'duration' => $data['duration'] ?? 5,
                    'fps' => $data['fps'] ?? 24,
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 576,
                    'guidance_scale' => $data['guidance_scale'] ?? 7.5,
                    'num_inference_steps' => $data['num_inference_steps'] ?? 20
                ],
                'pointsCost' => rand(80, 150),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成视频任务状态响应
     * 🔥 LongDev1新增 - 视频任务状态查询响应
     */
    private function generateVideoTaskStatusResponse($taskId)
    {
        // 模拟视频任务进度
        $statuses = ['processing', 'completed', 'failed'];
        $weights = [40, 55, 5]; // 视频处理时间更长，处理中40%，完成55%，失败5%

        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => $status,
                'type' => 'video',
                'created_at' => date('Y-m-d H:i:s', time() - rand(60, 600)),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        switch ($status) {
            case 'processing':
                $baseResponse['data']['progress'] = rand(5, 80);
                $baseResponse['data']['estimated_remaining'] = rand(60, 300) . '秒';
                $baseResponse['data']['current_stage'] = $this->getVideoProcessingStage();
                break;

            case 'completed':
                $baseResponse['data']['completed_at'] = date('Y-m-d H:i:s');
                $baseResponse['data']['results'] = $this->generateVideoResults($taskId);
                $baseResponse['data']['pointsCost'] = rand(50, 150);
                $baseResponse['data']['accountBalance'] = rand(100, 1000);
                break;

            case 'failed':
                $baseResponse['data']['error'] = [
                    'code' => 'VIDEO_GENERATION_FAILED',
                    'message' => '视频生成失败，请检查输入参数或重试'
                ];
                break;
        }

        return $baseResponse;
    }

    /**
     * 获取视频处理阶段
     * 🔥 LongDev1新增 - 视频处理阶段描述
     */
    private function getVideoProcessingStage()
    {
        $stages = [
            '正在分析输入图像...',
            '正在生成关键帧...',
            '正在插值中间帧...',
            '正在优化视频质量...',
            '正在渲染最终视频...'
        ];

        return $stages[array_rand($stages)];
    }

    /**
     * 生成视频结果
     * 🔥 LongDev1新增 - 视频生成结果
     */
    private function generateVideoResults($taskId)
    {
        return [
            [
                'id' => 'video_' . uniqid(),
                'url' => HttpHelper::generateMockFileUrl('video', 'mp4'),
                'thumbnail_url' => HttpHelper::generateMockFileUrl('video_thumb', 'jpg'),
                'duration' => rand(3, 10),
                'fps' => 24,
                'width' => rand(1024, 1920),
                'height' => rand(576, 1080),
                'format' => 'mp4',
                'size' => rand(5000000, 50000000), // 5-50MB
                'metadata' => [
                    'codec' => 'h264',
                    'bitrate' => rand(2000, 8000) . 'kbps',
                    'model_version' => 'liblib-video-v1.2'
                ]
            ]
        ];
    }

    /**
     * 生成任务状态响应
     * 🔥 LongDev1修正 - 更新响应格式
     */
    private function generateTaskStatusResponse($taskId)
    {
        // 模拟任务进度
        $statuses = ['processing', 'completed', 'failed'];
        $weights = [30, 65, 5]; // 处理中30%，完成65%，失败5%

        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s', time() - rand(30, 300)),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        switch ($status) {
            case 'processing':
                $baseResponse['data']['progress'] = rand(10, 90);
                $baseResponse['data']['estimated_remaining'] = rand(10, 60) . '秒';
                break;

            case 'completed':
                $baseResponse['data']['completed_at'] = date('Y-m-d H:i:s');
                $baseResponse['data']['results'] = $this->generateImageResults($taskId);
                // 🔥 LongDev1新增 - 官方文档要求的字段
                $baseResponse['data']['pointsCost'] = rand(10, 50);
                $baseResponse['data']['accountBalance'] = rand(100, 1000);
                break;

            case 'failed':
                $baseResponse['data']['error'] = [
                    'code' => 'GENERATION_FAILED',
                    'message' => '图像生成失败，请检查提示词或重试'
                ];
                break;
        }

        return $baseResponse;
    }
    
    // ==================== 🔥 LongDev1新增 - 参数验证方法 ====================

    /**
     * 验证IMG1参数
     * 🔥 LongDev1新增 - IMG1智能算法参数验证
     */
    private function validateIMG1Parameters($data)
    {
        // 验证图像尺寸
        if (isset($data['width']) && ($data['width'] < 512 || $data['width'] > 2048)) {
            throw new Exception('图像宽度必须在512-2048之间');
        }

        if (isset($data['height']) && ($data['height'] < 512 || $data['height'] > 2048)) {
            throw new Exception('图像高度必须在512-2048之间');
        }

        // 验证生成数量
        if (isset($data['imageNum']) && ($data['imageNum'] < 1 || $data['imageNum'] > 4)) {
            throw new Exception('图像数量必须在1-4之间');
        }

        // 验证采样步数
        if (isset($data['steps']) && ($data['steps'] < 1 || $data['steps'] > 50)) {
            throw new Exception('采样步数必须在1-50之间');
        }

        // 验证引导系数
        if (isset($data['scale']) && ($data['scale'] < 1 || $data['scale'] > 20)) {
            throw new Exception('引导系数必须在1-20之间');
        }

        // 验证质量设置
        $validQualities = ['low', 'medium', 'high', 'ultra'];
        if (isset($data['quality']) && !in_array($data['quality'], $validQualities)) {
            throw new Exception('不支持的质量设置: ' . $data['quality']);
        }
    }

    /**
     * 验证星流参数
     * 🔥 LongDev1新增 - 星流专用参数验证
     */
    private function validateXingliuParameters($data)
    {
        // 验证模板UUID
        if (isset($data['templateUuid']) && !preg_match('/^[a-f0-9-]{36}$/', $data['templateUuid'])) {
            throw new Exception('无效的模板UUID格式');
        }

        // 验证采样器
        $validSamplers = ['DPM++ 2M Karras', 'Euler', 'Euler Ancestral', 'DDIM', 'PLMS'];
        if (isset($data['sampler']) && !in_array($data['sampler'], $validSamplers)) {
            throw new Exception('不支持的采样器: ' . $data['sampler']);
        }

        // 验证重绘强度 (仅图生图)
        if (isset($data['strength']) && ($data['strength'] < 0.1 || $data['strength'] > 1.0)) {
            throw new Exception('重绘强度必须在0.1-1.0之间');
        }
    }

    /**
     * 验证视频参数
     * 🔥 LongDev1新增 - 视频生成参数验证
     */
    private function validateVideoParameters($data)
    {
        // 验证视频时长
        if (isset($data['duration']) && ($data['duration'] < 1 || $data['duration'] > 30)) {
            throw new Exception('视频时长必须在1-30秒之间');
        }

        // 验证帧率
        if (isset($data['fps']) && ($data['fps'] < 12 || $data['fps'] > 60)) {
            throw new Exception('帧率必须在12-60之间');
        }

        // 验证运动强度
        if (isset($data['motion_strength']) && ($data['motion_strength'] < 0.1 || $data['motion_strength'] > 1.0)) {
            throw new Exception('运动强度必须在0.1-1.0之间');
        }

        // 验证视频模型
        $validModels = ['liblib-video-v1', 'liblib-motion-v1'];
        if (isset($data['model']) && !in_array($data['model'], $validModels)) {
            throw new Exception('不支持的视频模型: ' . $data['model']);
        }
    }

    /**
     * 验证放大参数
     * 🔥 LongDev1新增 - 图像放大参数验证
     */
    private function validateUpscaleParameters($data)
    {
        // 验证放大倍数
        $validScales = [2, 4, 8];
        if (isset($data['scale']) && !in_array($data['scale'], $validScales)) {
            throw new Exception('不支持的放大倍数，支持: 2x, 4x, 8x');
        }

        // 验证放大模型
        $validModels = ['real-esrgan', 'esrgan-4x', 'waifu2x', 'swinir'];
        if (isset($data['model']) && !in_array($data['model'], $validModels)) {
            throw new Exception('不支持的放大模型: ' . $data['model']);
        }
    }

    /**
     * 生成图像结果
     * 🔥 LongDev1修正 - 添加新的响应字段
     */
    private function generateImageResults($taskId)
    {
        $imageCount = rand(1, 4);
        $results = [];

        for ($i = 0; $i < $imageCount; $i++) {
            $results[] = [
                'id' => 'img_' . uniqid(),
                'url' => HttpHelper::generateMockFileUrl('image', 'jpg'),
                'thumbnail_url' => HttpHelper::generateMockFileUrl('thumbnail', 'jpg'),
                'width' => rand(1024, 2048),
                'height' => rand(1024, 2048),
                'format' => 'jpg',
                'size' => rand(800000, 3000000), // 0.8-3MB
                // 🔥 LongDev1新增 - 添加官方文档要求的字段
                'pointsCost' => rand(10, 50),
                'accountBalance' => rand(100, 1000),
                'metadata' => [
                    'seed' => rand(1000000, 9999999),
                    'steps' => rand(15, 30),
                    'cfg_scale' => round(rand(50, 150) / 10, 1),
                    'model_version' => 'star-3-alpha-v' . rand(1, 5),
                    'algorithm' => 'IMG1-v2.0'  // 🔥 LongDev1新增
                ]
            ];
        }

        return $results;
    }
    
    // ==================== 🔥 LongDev1新增 - 图像处理响应方法 ====================

    /**
     * 生成图像放大响应
     * 🔥 LongDev1新增 - 图像放大响应
     */
    private function generateUpscaleResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('upscale');

        return [
            'code' => 0,
            'message' => 'Image upscale task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => $data['model'] ?? 'real-esrgan',
                'status' => 'processing',
                'image' => $data['image'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(30, 90),
                'parameters' => [
                    'scale' => $data['scale'] ?? 2,
                    'face_enhance' => $data['face_enhance'] ?? false
                ],
                'pointsCost' => rand(5, 20),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成图像修复响应
     * 🔥 LongDev1新增 - 图像修复响应
     */
    private function generateInpaintResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('inpaint');

        return [
            'code' => 0,
            'message' => 'Image inpaint task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'liblib-inpaint-v1',
                'status' => 'processing',
                'image' => $data['image'],
                'mask' => $data['mask'],
                'prompt' => $data['prompt'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(45, 120),
                'parameters' => [
                    'strength' => $data['strength'] ?? 0.8,
                    'guidance_scale' => $data['guidance_scale'] ?? 7.5,
                    'num_inference_steps' => $data['num_inference_steps'] ?? 20
                ],
                'pointsCost' => rand(15, 35),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成背景移除响应
     * 🔥 LongDev1新增 - 背景移除响应
     */
    private function generateRemoveBgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('removebg');

        return [
            'code' => 0,
            'message' => 'Background removal task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => $data['model'] ?? 'u2net',
                'status' => 'processing',
                'image' => $data['image'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(15, 45),
                'pointsCost' => rand(3, 12),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成风格转换响应
     * 🔥 LongDev1新增 - 风格转换响应
     */
    private function generateStyleTransferResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('style');

        return [
            'code' => 0,
            'message' => 'Style transfer task created successfully',
            'data' => [
                'task_id' => $taskId,
                'model' => 'liblib-style-v1',
                'status' => 'processing',
                'content_image' => $data['content_image'],
                'style_image' => $data['style_image'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(60, 150),
                'parameters' => [
                    'strength' => $data['strength'] ?? 0.8,
                    'preserve_content' => $data['preserve_content'] ?? true
                ],
                'pointsCost' => rand(20, 40),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 验证工作流ID
     * 🔥 LongDev1修正 - 保持向后兼容
     */
    private function isValidWorkflowId($workflowId)
    {
        $validWorkflows = array_keys($this->getSupportedWorkflows());
        return in_array($workflowId, $validWorkflows);
    }

    /**
     * 获取支持的工作流
     * 🔥 LongDev1修正 - 扩展支持的工作流
     */
    private function getSupportedWorkflows()
    {
        return [
            'comfyui_basic' => '基础ComfyUI工作流',
            'comfyui_controlnet' => 'ControlNet控制工作流',
            'comfyui_lora' => 'LoRA风格工作流',
            'comfyui_inpainting' => '图像修复工作流',
            'comfyui_upscale' => '图像放大工作流',
            'star_realistic' => '星流写实风格',
            'star_anime' => '星流动漫风格',
            'star_artistic' => '星流艺术风格',
            // 🔥 LongDev1新增 - 新的工作流支持
            'img1_basic' => 'IMG1基础工作流',
            'img1_advanced' => 'IMG1高级工作流',
            'f1_kontext' => 'F.1 Kontext工作流'
        ];
    }
    
    /**
     * 获取工作流类型
     */
    private function getWorkflowType($workflowId)
    {
        if (strpos($workflowId, 'comfyui') === 0) {
            return 'comfyui';
        } elseif (strpos($workflowId, 'star') === 0) {
            return 'star';
        }
        return 'unknown';
    }
    
    /**
     * 获取预估时间
     */
    private function getEstimatedTime($workflowType)
    {
        switch ($workflowType) {
            case 'comfyui':
                return rand(30, 120); // 30-120秒
            case 'star':
                return rand(15, 45);  // 15-45秒
            default:
                return rand(20, 60);  // 20-60秒
        }
    }
    
    /**
     * 验证星流参数
     */
    private function validateStarParameters($data)
    {
        // 验证风格
        $validStyles = ['realistic', 'anime', 'artistic', 'portrait', 'landscape'];
        if (isset($data['style']) && !in_array($data['style'], $validStyles)) {
            throw new Exception('不支持的风格: ' . $data['style']);
        }
        
        // 验证质量
        $validQualities = ['low', 'medium', 'high', 'ultra'];
        if (isset($data['quality']) && !in_array($data['quality'], $validQualities)) {
            throw new Exception('不支持的质量设置: ' . $data['quality']);
        }
        
        // 验证宽高比
        $validRatios = ['1:1', '4:3', '3:4', '16:9', '9:16'];
        if (isset($data['aspect_ratio']) && !in_array($data['aspect_ratio'], $validRatios)) {
            throw new Exception('不支持的宽高比: ' . $data['aspect_ratio']);
        }
    }
    
    /**
     * 验证任务ID格式
     */
    private function isValidTaskId($taskId)
    {
        return preg_match('/^(liblib|star)_[a-f0-9]+_\d+$/', $taskId);
    }
    
    /**
     * 加权随机选择
     */
    private function weightedRandom($values, $weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        
        $currentWeight = 0;
        for ($i = 0; $i < count($values); $i++) {
            $currentWeight += $weights[$i];
            if ($random <= $currentWeight) {
                return $values[$i];
            }
        }
        
        return $values[0];
    }
    
    /**
     * 生成LiblibAI格式的错误响应 - 严格按照官方文档格式
     */
    private function generateLiblibErrorResponse($code, $message, $details = null)
    {
        // 根据错误类型设置正确的HTTP状态码
        $httpCode = 400;
        switch ($code) {
            case 'UNAUTHORIZED':
            case 'AUTH_FAILED':
                $httpCode = 401;
                break;
            case 'FORBIDDEN':
                $httpCode = 403;
                break;
            case 'NOT_FOUND':
            case 'MISSING_TASK_ID':
                $httpCode = 404;
                break;
            case 'RATE_LIMIT_EXCEEDED':
                $httpCode = 429;
                break;
            case 'INTERNAL_ERROR':
            case 'PROCESSING_ERROR':
                $httpCode = 500;
                break;
            default:
                $httpCode = 400;
        }

        http_response_code($httpCode);

        // 按照LiblibAI官方文档格式返回错误
        return [
            'code' => $httpCode,
            'message' => $message,
            'error' => [
                'code' => $code,
                'message' => $message,
                'details' => $details
            ],
            'data' => null,
            'timestamp' => time(),
            'request_id' => generateRequestId()
        ];
    }

    /**
     * 星流图生图接口 (官方路径)
     * POST /api/open/xingliu/img2img
     */
    public function xingliu_img2img($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt', 'image_url']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', '星流图生图失败，请重试');
            }

            // 生成响应
            $response = $this->generateXingliuImg2ImgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/xingliu/img2img',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/xingliu/img2img', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * LiblibAI文生图接口 (官方路径)
     * POST /api/open/liblib/text2img
     */
    public function liblib_text2img($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', 'LiblibAI文生图失败，请重试');
            }

            // 生成响应
            $response = $this->generateLiblibText2ImgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/liblib/text2img',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/liblib/text2img', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * LiblibAI图生图接口 (官方路径)
     * POST /api/open/liblib/img2img
     */
    public function liblib_img2img($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt', 'image_url']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', 'LiblibAI图生图失败，请重试');
            }

            // 生成响应
            $response = $this->generateLiblibImg2ImgResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/liblib/img2img',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/liblib/img2img', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * ComfyUI工作流执行接口 (官方路径)
     * POST /api/open/comfyui/run
     */
    public function comfyui_run($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['workflow_id', 'prompt']);

            // 验证工作流ID
            $workflowId = $data['workflow_id'];
            if (!$this->isValidWorkflowId($workflowId)) {
                return $this->generateLiblibErrorResponse(
                    'INVALID_WORKFLOW',
                    '不支持的工作流ID: ' . $workflowId
                );
            }

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateLiblibErrorResponse('GENERATION_FAILED', 'ComfyUI工作流执行失败，请重试');
            }

            // 生成响应
            $response = $this->generateComfyUIResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/comfyui/run',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/comfyui/run', $e->getMessage(), $data ?? []);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * ComfyUI任务状态查询接口 (官方路径)
     * GET /api/open/comfyui/task/{taskId}
     */
    public function comfyui_task_status($params = [])
    {
        $startTime = microtime(true);

        try {
            $taskId = $params['taskId'] ?? null;

            if (!$taskId) {
                return $this->generateLiblibErrorResponse('MISSING_TASK_ID', '缺少任务ID');
            }

            // 验证任务ID格式
            if (!$this->isValidTaskId($taskId)) {
                return $this->generateLiblibErrorResponse('INVALID_TASK_ID', '无效的任务ID格式');
            }

            // 模拟任务状态
            $response = $this->generateTaskStatusResponse($taskId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/comfyui/task/' . $taskId,
                'GET',
                ['task_id' => $taskId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'comfyui/task/status', $e->getMessage(), ['task_id' => $taskId ?? 'unknown']);

            return $this->generateLiblibErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 文件上传接口 (官方路径)
     * POST /api/open/upload
     */
    public function upload_file($params = [])
    {
        $startTime = microtime(true);

        try {
            // 模拟文件上传
            $response = $this->generateFileUploadResponse();

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'api/open/upload',
                'POST',
                ['file_upload' => true],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'api/open/upload', $e->getMessage(), []);

            return $this->generateLiblibErrorResponse('UPLOAD_FAILED', '文件上传失败');
        }
    }

    /**
     * 生成星流文生图响应
     * 🔥 LongDev1修正 - 更新响应格式，添加官方字段
     */
    private function generateXingliuText2ImgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('xingliu');

        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'negativePrompt' => $data['negativePrompt'] ?? $data['negative_prompt'] ?? '',
                'model' => 'xingliu-v1',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(15, 45),
                'queue_position' => rand(1, 8),
                'parameters' => [
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 1024,
                    'imageNum' => $data['imageNum'] ?? 1,
                    'seed' => $data['seed'] ?? -1,
                    'steps' => $data['steps'] ?? 20,
                    'scale' => $data['scale'] ?? 7.5,
                    'sampler' => $data['sampler'] ?? 'DPM++ 2M Karras',
                    'templateUuid' => $data['templateUuid'] ?? null,
                    'subjectReference' => $data['subjectReference'] ?? null, // 🔥 F.1 Kontext支持
                    'styleReference' => $data['styleReference'] ?? null,     // 🔥 F.1 Kontext支持
                    'style' => $data['style'] ?? 'realistic',
                    'quality' => $data['quality'] ?? 'high',
                    'aspect_ratio' => $data['aspect_ratio'] ?? '1:1'
                ],
                // 🔥 LongDev1新增 - 官方文档要求的字段
                'pointsCost' => rand(8, 25),
                'accountBalance' => rand(100, 1000)
            ]
        ];
    }

    /**
     * 生成星流图生图响应
     */
    private function generateXingliuImg2ImgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('xingliu');

        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'image_url' => $data['image_url'],
                'model' => 'xingliu-v1',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(20, 60),
                'parameters' => [
                    'strength' => $data['strength'] ?? 0.7,
                    'style' => $data['style'] ?? 'realistic'
                ]
            ]
        ];
    }

    /**
     * 生成LiblibAI文生图响应
     */
    private function generateLiblibText2ImgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('liblib');

        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'model' => 'liblib-v2',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(30, 90),
                'parameters' => [
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 1024,
                    'steps' => $data['steps'] ?? 20,
                    'cfg_scale' => $data['cfg_scale'] ?? 7.5
                ]
            ]
        ];
    }

    /**
     * 生成LiblibAI图生图响应
     */
    private function generateLiblibImg2ImgResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('liblib');

        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'image_url' => $data['image_url'],
                'model' => 'liblib-v2',
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(25, 75),
                'parameters' => [
                    'strength' => $data['strength'] ?? 0.8,
                    'steps' => $data['steps'] ?? 20
                ]
            ]
        ];
    }

    /**
     * 生成ComfyUI响应
     */
    private function generateComfyUIResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('comfyui');
        $workflowId = $data['workflow_id'];

        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'task_id' => $taskId,
                'workflow_id' => $workflowId,
                'status' => 'submitted',
                'prompt' => $data['prompt'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => $this->getEstimatedTime('comfyui'),
                'queue_position' => rand(1, 15),
                'parameters' => [
                    'width' => $data['width'] ?? 1024,
                    'height' => $data['height'] ?? 1024,
                    'steps' => $data['steps'] ?? 20,
                    'cfg_scale' => $data['cfg_scale'] ?? 7.5,
                    'seed' => $data['seed'] ?? rand(1000000, 9999999)
                ]
            ]
        ];
    }

    /**
     * 生成文件上传响应
     */
    private function generateFileUploadResponse()
    {
        return [
            'code' => 0,
            'message' => 'success',
            'data' => [
                'file_id' => 'file_' . uniqid(),
                'filename' => 'uploaded_file_' . time(),
                'size' => rand(1024, 10485760),
                'upload_time' => date('Y-m-d H:i:s'),
                'file_url' => HttpHelper::generateMockFileUrl('upload', 'bin'),
                'status' => 'uploaded'
            ]
        ];
    }
}
