<?php

/**
 * 测试项目状态更新的调试脚本
 * 模拟 ProjectStoryboardService 调用 ProjectService::updateProject 的场景
 */

require_once __DIR__ . '/php/api/vendor/autoload.php';

// 初始化 Laravel 应用
$app = require_once __DIR__ . '/php/api/bootstrap/app.php';

use App\Services\PyApi\ProjectService;
use App\Models\Project;
use App\Models\User;
use Illuminate\Support\Facades\Log;

echo "=== 项目状态更新调试测试 ===\n\n";

try {
    // 1. 查找测试用户和项目
    $user = User::first();
    if (!$user) {
        echo "❌ 错误：没有找到测试用户\n";
        exit(1);
    }
    
    $project = Project::where('user_id', $user->id)->first();
    if (!$project) {
        echo "❌ 错误：没有找到测试项目\n";
        exit(1);
    }
    
    echo "✅ 找到测试数据: 用户ID={$user->id}, 项目ID={$project->id}\n";
    echo "当前项目状态: {$project->status}\n\n";

    // 2. 模拟 ProjectStoryboardService 传递的数据结构
    $analysisResult = [
        'project_info' => [
            'title' => '测试项目 - 状态调试',
            'description' => '用于调试状态更新的测试项目',
            'project_config' => ['test' => true, 'debug' => true],
            'story_content' => '这是一个测试故事内容',
            'status' => 'in_progress'  // 关键：这里设置状态
        ]
    ];
    
    echo "--- 模拟数据结构 ---\n";
    echo "project_info 包含的字段: " . implode(', ', array_keys($analysisResult['project_info'])) . "\n";
    echo "status 值: " . $analysisResult['project_info']['status'] . "\n\n";

    // 3. 调用 updateProject 方法
    echo "--- 调用 updateProject ---\n";
    $projectService = new ProjectService();
    $result = $projectService->updateProject(
        $project->id, 
        $user->id, 
        $analysisResult['project_info']
    );
    
    // 4. 检查结果
    echo "更新结果:\n";
    echo "Code: {$result['code']}\n";
    echo "Message: {$result['message']}\n";
    
    if ($result['code'] === 200) {
        // 重新加载项目数据
        $project->refresh();
        echo "✅ 更新成功\n";
        echo "数据库中的新状态: {$project->status}\n";
        
        if ($project->status === 'in_progress') {
            echo "🎉 状态更新成功！\n";
        } else {
            echo "❌ 状态更新失败！期望: in_progress, 实际: {$project->status}\n";
        }
    } else {
        echo "❌ 更新失败\n";
        print_r($result);
    }

} catch (\Exception $e) {
    echo "❌ 测试过程中发生异常:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "请查看日志文件: php/api/storage/logs/lumen-" . date('Y-m-d') . ".log\n";
