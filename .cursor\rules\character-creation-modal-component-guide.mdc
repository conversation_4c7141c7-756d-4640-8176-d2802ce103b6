# Py视频创作工具 - 角色创建罩层组件使用指南

## 📋 组件概述

角色创建罩层组件是Py视频创作工具中统一的角色创建解决方案，为所有需要角色创建功能的业务场景提供一致的用户体验和标准化的业务流程。

### 🎯 设计理念

- **统一性**: 所有角色创建功能使用同一个组件，确保UI和交互的一致性
- **可复用性**: 通过参数化配置支持不同业务场景的需求
- **标准化**: 遵循统一的业务流程和技术规范
- **用户友好**: 提供直观的操作界面和及时的反馈机制

### 🏗️ 组件架构

```
角色创建罩层组件
├── UI层 (Py视频创作工具前端统一处理)
├── 业务逻辑层 (参考 diagram-character-creation-shared.html)
├── API交互层 (标准化API调用)
└── 状态管理层 (罩层生命周期管理)
```

## 🔧 组件调用方法

### 基础调用

```javascript
// 最简单的调用方式
PyVideoTool.CharacterCreationModal.open({
    mode: 'full',
    callbacks: {
        onSuccess: (result) => {
            console.log('角色创建成功:', result.data.character);
            // 处理创建成功的角色数据
        },
        onCancel: () => {
            console.log('用户取消了角色创建');
        }
    }
});
```

### 高级调用

```javascript
// 完整配置的调用方式
PyVideoTool.CharacterCreationModal.open({
    // 基础配置
    mode: 'filtered',
    title: '为项目创建角色',
    
    // 筛选条件
    filters: {
        projectType: 'video',
        styles: ['写实风3.0', '动漫可爱3.0'],
        tags: ['现代', '都市'],
        permissions: ['create', 'publish']
    },
    
    // 显示配置
    display: {
        showStyleSelection: true,
        showModeSelection: true,
        showPreview: true
    },
    
    // 功能配置
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enablePublish: false
    },
    
    // 回调函数
    callbacks: {
        onSuccess: handleSuccess,
        onCancel: handleCancel,
        onError: handleError,
        onProgress: updateProgress
    }
});
```

## 📋 参数配置详解

### mode 参数

| 模式 | 描述 | 适用场景 |
|------|------|----------|
| `full` | 显示所有可用角色，无筛选条件 | 角色库管理、通用角色创建 |
| `filtered` | 根据filters参数筛选显示 | 项目特定角色创建、绑定场景 |
| `quick` | 使用预设配置，简化创建流程 | 快速创建、批量操作 |
| `advanced` | 提供完整的角色创建功能 | 专业用户、高级定制 |

### filters 参数

```javascript
filters: {
    projectType: 'video|animation|story',  // 项目类型
    styles: ['写实风3.0', '动漫可爱3.0'],    // 角色风格数组
    tags: ['男性', '女性', '动物'],         // 角色标签
    permissions: ['create', 'publish'],     // 用户权限
    maxCount: 10,                          // 最大选择数量
    minCount: 1                            // 最小选择数量
}
```

### display 参数

```javascript
display: {
    showStyleSelection: true,    // 显示风格选择
    showModeSelection: true,     // 显示创建模式选择
    showAdvancedOptions: false,  // 显示高级选项
    showPreview: true,           // 显示预览功能
    enableBatchCreate: false     // 启用批量创建
}
```

### features 参数

```javascript
features: {
    enableUpload: true,           // 启用文件上传
    enableAIGeneration: true,     // 启用AI生成
    enableStyleCustomization: true, // 启用风格定制
    enablePublish: false,         // 启用发布功能
    enableSave: true             // 启用保存功能
}
```

## 🔄 事件回调机制

### 回调函数类型

```javascript
callbacks: {
    // 成功回调 - 角色创建成功时调用
    onSuccess: (result) => {
        // result.success: true
        // result.data.character: 角色数据
        // result.data.usage: 使用情况
    },
    
    // 取消回调 - 用户取消操作时调用
    onCancel: () => {
        // 处理用户取消逻辑
    },
    
    // 错误回调 - 创建失败时调用
    onError: (error) => {
        // error.code: 错误代码
        // error.message: 错误信息
        // error.details: 详细信息
    },
    
    // 进度回调 - 创建过程中的进度更新
    onProgress: (progress) => {
        // progress.progress: 进度百分比 (0-100)
        // progress.stage: 当前阶段
        // progress.message: 进度描述
    },
    
    // 验证回调 - 参数验证时调用
    onValidate: (data) => {
        // 返回 true/false 或错误信息
        return true;
    }
}
```

### 事件监听

```javascript
// 全局事件监听
PyVideoTool.CharacterCreationModal.on('creation.success', (result) => {
    console.log('全局监听到角色创建成功:', result);
});

// 移除事件监听
PyVideoTool.CharacterCreationModal.off('creation.success', callback);
```

## 📤 返回结果处理

### 成功结果结构

```javascript
{
    success: true,
    data: {
        character: {
            id: 'char_123456789',
            name: '角色名称',
            description: '角色描述',
            style: '写实风3.0',
            mode: 'ai_created|user_owned',
            imageUrl: 'https://example.com/character.jpg',
            thumbnailUrl: 'https://example.com/thumb.jpg',
            metadata: {
                platform: 'LiblibAI',
                createdAt: '2024-01-01T00:00:00Z',
                fileSize: 1024000,
                dimensions: { width: 1024, height: 1024 },
                tags: ['男性', '现代'],
                permissions: ['use', 'modify']
            }
        },
        usage: {
            pointsUsed: 10,
            timeSpent: 45000,
            platform: 'LiblibAI'
        }
    },
    message: '角色创建成功',
    timestamp: '2024-01-01T00:00:00Z'
}
```

### 失败结果处理

```javascript
{
    success: false,
    error: {
        code: 'AUTH_FAILED|INSUFFICIENT_POINTS|CREATION_FAILED|USER_CANCELLED',
        message: '详细错误信息',
        details: {
            reason: '具体失败原因',
            suggestion: '建议的解决方案',
            retryable: true|false
        }
    },
    timestamp: '2024-01-01T00:00:00Z'
}
```

## ⚠️ 错误处理指南

### 常见错误类型

| 错误代码 | 描述 | 处理建议 |
|---------|------|---------|
| `AUTH_FAILED` | Token验证失败 | 重新登录或刷新Token |
| `INSUFFICIENT_POINTS` | 积分不足 | 引导用户充值积分 |
| `CREATION_FAILED` | 创建失败 | 检查参数，提供重试选项 |
| `USER_CANCELLED` | 用户取消 | 正常流程，无需特殊处理 |
| `TIMEOUT` | 请求超时 | 检查网络，提供重试 |
| `VALIDATION_ERROR` | 参数验证失败 | 检查参数格式和权限 |

### 错误处理最佳实践

```javascript
const handleError = (error) => {
    switch(error.code) {
        case 'AUTH_FAILED':
            // 显示登录提示
            PyVideoTool.Auth.showLogin();
            break;
            
        case 'INSUFFICIENT_POINTS':
            // 显示充值对话框
            PyVideoTool.Points.showRecharge({
                required: error.details.pointsRequired,
                current: error.details.currentPoints
            });
            break;
            
        case 'CREATION_FAILED':
            if (error.details.retryable) {
                // 显示重试选项
                showRetryDialog(error.details.suggestion);
            } else {
                // 显示错误信息
                showErrorMessage(error.message);
            }
            break;
            
        case 'USER_CANCELLED':
            // 用户主动取消，无需处理
            break;
            
        default:
            // 显示通用错误提示
            showErrorMessage(error.message || '创建角色时发生未知错误');
    }
};
```

## 🚀 性能优化建议

### 缓存策略

```javascript
// 启用缓存优化
PyVideoTool.CharacterCreationModal.open({
    mode: 'filtered',
    advanced: {
        cacheEnabled: true,        // 启用缓存
        preloadStyles: true,       // 预加载风格数据
        lazyLoadImages: true       // 懒加载图片
    }
});
```

### 内存管理

```javascript
// 组件使用完毕后清理资源
PyVideoTool.CharacterCreationModal.cleanup();

// 批量操作时的内存优化
PyVideoTool.CharacterCreationModal.open({
    mode: 'quick',
    advanced: {
        batchMode: true,           // 批量模式
        memoryOptimized: true      // 内存优化
    }
});
```

## 📱 响应式设计

### 设备适配

```javascript
// 移动端优化配置
PyVideoTool.CharacterCreationModal.open({
    mode: 'quick',
    display: {
        mobileOptimized: true,     // 移动端优化
        touchFriendly: true,       // 触摸友好
        compactMode: true          // 紧凑模式
    }
});
```

### 屏幕尺寸适配

- **桌面端**: 完整功能，大尺寸罩层
- **平板端**: 响应式布局，适中尺寸
- **移动端**: 简化界面，全屏显示

## 🧪 测试指南

### 单元测试

```javascript
// 测试组件调用
describe('CharacterCreationModal', () => {
    test('should open modal with correct parameters', () => {
        const options = {
            mode: 'full',
            callbacks: { onSuccess: jest.fn() }
        };
        
        PyVideoTool.CharacterCreationModal.open(options);
        expect(modal.isOpen()).toBe(true);
    });
});
```

### 集成测试

```javascript
// 测试完整创建流程
test('complete character creation flow', async () => {
    const result = await PyVideoTool.CharacterCreationModal.open({
        mode: 'quick',
        presets: { style: '写实风3.0' }
    });
    
    expect(result.success).toBe(true);
    expect(result.data.character).toBeDefined();
});
```

## 📚 最佳实践案例

### 项目创建中使用

```javascript
// 在项目创建流程中集成角色创建
const createProjectWithCharacter = async () => {
    try {
        const characterResult = await PyVideoTool.CharacterCreationModal.open({
            mode: 'filtered',
            filters: {
                projectType: 'video',
                styles: getProjectStyles()
            }
        });
        
        if (characterResult.success) {
            // 将角色添加到项目中
            await addCharacterToProject(characterResult.data.character);
        }
    } catch (error) {
        handleError(error);
    }
};
```

### 角色绑定中使用

```javascript
// 在角色绑定流程中集成角色创建
const bindCharacterWithCreation = (storyboardPosition) => {
    PyVideoTool.CharacterCreationModal.open({
        mode: 'filtered',
        filters: {
            projectType: getCurrentProjectType(),
            tags: getPositionRequirements(storyboardPosition)
        },
        callbacks: {
            onSuccess: (result) => {
                // 自动绑定新创建的角色
                bindCharacterToPosition(
                    result.data.character.id, 
                    storyboardPosition.id
                );
            }
        }
    });
};
```

## 🔄 版本兼容性

### 当前版本
- **版本**: v1.0.0
- **发布日期**: 2024-01-01
- **兼容性**: Py视频创作工具 v2.0.0+

### 升级指南

```javascript
// v0.x 到 v1.0 的升级
// 旧版本调用方式
CharacterCreator.show(options, callback);

// 新版本调用方式
PyVideoTool.CharacterCreationModal.open({
    ...options,
    callbacks: { onSuccess: callback }
});
```

## 🆘 常见问题解答

### Q: 如何自定义罩层样式？
A: 通过CSS变量覆盖默认样式：
```css
:root {
    --character-modal-bg: #ffffff;
    --character-modal-border: #e0e0e0;
}
```

### Q: 如何处理网络断开的情况？
A: 组件内置网络状态检测，会自动显示重连提示。

### Q: 是否支持批量创建角色？
A: 支持，通过设置 `features.enableBatchCreate: true` 启用。

### Q: 如何获取创建历史记录？
A: 使用 `PyVideoTool.CharacterCreationModal.getHistory()` 方法。

---

*本指南将随着组件功能的完善持续更新。如有问题，请参考API规范文档或联系开发团队。*
