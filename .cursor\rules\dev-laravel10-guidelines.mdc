---
description: Laravel 10.x 全栈框架开发规范指南
globs: ["php/backend/**/*.php", "**/composer.json", "**/composer.lock", "**/.env*", "**/routes/**", "**/app/**", "**/config/**", "**/database/**", "**/resources/**", "**/tests/**"]
alwaysApply: true
---

## Laravel 10.x 全栈框架开发规范指南

### 框架信息

- **框架版本**: Laravel 10.x
- **PHP要求**: >= 8.1
- **官方文档**: https://laravel.com/docs/10.x
- **中文文档**: https://learnku.com/docs/laravel/10.x
- **适用场景**: 全栈Web应用、管理后台、API服务
- **特点**: 功能完整、生态丰富、开发效率高

### 系统要求

#### 服务器要求
```bash
# 必需扩展
- PHP >= 8.1
- BCMath PHP Extension
- Ctype PHP Extension
- cURL PHP Extension
- DOM PHP Extension
- Fileinfo PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PCRE PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
```

#### 推荐开发环境
```bash
# 使用Composer安装
composer create-project laravel/laravel example-app

# 使用Laravel Installer
composer global require laravel/installer
laravel new example-app

# 本地开发服务器
php artisan serve
```

### 项目结构

#### 标准目录结构
```
app/
├── Broadcasting/
├── Console/
│   └── Commands/
├── Events/
├── Exceptions/
├── Http/
│   ├── Controllers/
│   ├── Middleware/
│   ├── Requests/
│   └── Resources/
├── Jobs/
├── Listeners/
├── Mail/
├── Models/
├── Notifications/
├── Policies/
├── Providers/
├── Rules/
└── Services/

bootstrap/
├── app.php
├── cache/
└── providers.php

config/
├── app.php
├── auth.php
├── cache.php
├── database.php
├── filesystems.php
├── logging.php
├── mail.php
├── queue.php
├── services.php
└── session.php

database/
├── factories/
├── migrations/
└── seeders/

public/
├── css/
├── js/
├── images/
├── index.php
└── .htaccess

resources/
├── css/
├── js/
├── lang/
└── views/

routes/
├── api.php
├── channels.php
├── console.php
└── web.php

storage/
├── app/
├── framework/
└── logs/

tests/
├── Feature/
└── Unit/

vendor/
.env
.env.example
artisan
composer.json
composer.lock
package.json
vite.config.js
```

### 路由系统

#### Web路由 (routes/web.php)
```php
<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return view('welcome');
});

// 认证路由
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// 资源路由
Route::resource('posts', PostController::class);
Route::resource('users', UserController::class)->except(['show']);

// 路由组
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', [AdminController::class, 'index'])->name('index');
    Route::resource('users', AdminUserController::class);
    Route::resource('posts', AdminPostController::class);
});

// 子域名路由
Route::domain('{account}.example.com')->group(function () {
    Route::get('user/{id}', function (string $account, string $id) {
        return "Account: {$account}, User: {$id}";
    });
});

require __DIR__.'/auth.php';
```

#### API路由 (routes/api.php)
```php
<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\PostController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// 公开API路由
Route::prefix('v1')->group(function () {
    // 认证相关
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);

    // 需要认证的路由
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('user', function (Request $request) {
            return $request->user();
        });

        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);

        // 用户管理
        Route::apiResource('users', UserController::class);

        // 文章管理
        Route::apiResource('posts', PostController::class);

        // 文件上传
        Route::post('upload', [FileController::class, 'upload']);
    });

    // 限流路由
    Route::middleware('throttle:60,1')->group(function () {
        Route::get('public-data', [PublicController::class, 'data']);
    });
});
```

#### 路由模型绑定
```php
// 隐式绑定
Route::get('/users/{user}', function (User $user) {
    return $user->email;
});

// 自定义键绑定
Route::get('/posts/{post:slug}', function (Post $post) {
    return $post;
});

// 作用域绑定
Route::get('/users/{user}/posts/{post}', function (User $user, Post $post) {
    return $post;
})->scopeBindings();

// 显式绑定 (在RouteServiceProvider中)
public function boot(): void
{
    Route::model('user', User::class);

    Route::bind('user', function (string $value) {
        return User::where('name', $value)->firstOrFail();
    });
}
```

#### 路由缓存
```bash
# 生成路由缓存
php artisan route:cache

# 清除路由缓存
php artisan route:clear

# 查看路由列表
php artisan route:list

# 查看特定路由
php artisan route:list --path=api
php artisan route:list --name=user
```

### 控制器开发

#### 基础控制器
```php
<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    /**
     * 成功响应
     */
    protected function success($data = null, string $message = 'Success', int $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->format('c')
        ], $code);
    }

    /**
     * 错误响应
     */
    protected function error(string $message = 'Error', int $code = 400, $errors = null): JsonResponse
    {
        return response()->json([
            'success' => false,
            'code' => $code,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => now()->format('c')
        ], $code);
    }
}
```

#### 资源控制器
```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePostRequest;
use App\Http\Requests\UpdatePostRequest;
use App\Http\Resources\PostResource;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $posts = Post::with(['user', 'tags'])
            ->when($request->search, function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->latest()
            ->paginate($request->per_page ?? 15);

        return PostResource::collection($posts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePostRequest $request): PostResource
    {
        $post = Post::create([
            'title' => $request->title,
            'content' => $request->content,
            'status' => $request->status ?? 'draft',
            'user_id' => auth()->id(),
        ]);

        if ($request->has('tags')) {
            $post->tags()->sync($request->tags);
        }

        return new PostResource($post->load(['user', 'tags']));
    }

    /**
     * Display the specified resource.
     */
    public function show(Post $post): PostResource
    {
        return new PostResource($post->load(['user', 'tags', 'comments.user']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePostRequest $request, Post $post): PostResource
    {
        $this->authorize('update', $post);

        $post->update($request->validated());

        if ($request->has('tags')) {
            $post->tags()->sync($request->tags);
        }

        return new PostResource($post->load(['user', 'tags']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Post $post): JsonResponse
    {
        $this->authorize('delete', $post);

        $post->delete();

        return response()->json(['message' => '文章删除成功']);
    }
}
```

### 请求验证

#### 表单请求类
```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
            'status' => ['sometimes', Rule::in(['draft', 'published', 'archived'])],
            'tags' => ['sometimes', 'array'],
            'tags.*' => ['integer', 'exists:tags,id'],
            'featured_image' => ['sometimes', 'image', 'max:2048'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'title' => '标题',
            'content' => '内容',
            'status' => '状态',
            'tags' => '标签',
            'featured_image' => '特色图像',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'title.required' => '标题不能为空',
            'title.max' => '标题不能超过255个字符',
            'content.required' => '内容不能为空',
            'status.in' => '状态值无效',
            'tags.*.exists' => '选择的标签不存在',
            'featured_image.image' => '特色图像必须是图像文件',
            'featured_image.max' => '特色图像大小不能超过2MB',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'slug' => \Str::slug($this->title),
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->somethingElseIsInvalid()) {
                $validator->errors()->add('field', 'Something is wrong with this field!');
            }
        });
    }

    private function somethingElseIsInvalid(): bool
    {
        return false;
    }
}
```

#### 自定义验证规则
```php
<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class Uppercase implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strtoupper($value) !== $value) {
            $fail('The :attribute must be uppercase.');
        }
    }
}

// 使用自定义规则
public function rules(): array
{
    return [
        'name' => ['required', new Uppercase],
    ];
}
```

### 中间件开发

#### 自定义中间件
```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAge
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, int $age = 18): Response
    {
        if ($request->age <= $age) {
            return redirect('home');
        }

        return $next($request);
    }
}

// 注册中间件 (在app/Http/Kernel.php中)
protected $middlewareAliases = [
    'age' => \App\Http\Middleware\CheckAge::class,
];

// 使用中间件
Route::get('admin/profile', function () {
    // ...
})->middleware('age:21');
```

#### 全局中间件
```php
// 在app/Http/Kernel.php中
protected $middleware = [
    \App\Http\Middleware\TrustProxies::class,
    \Fruitcake\Cors\HandleCors::class,
    \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
    \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
    \App\Http\Middleware\TrimStrings::class,
    \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
];
```

### 模型开发

#### Eloquent模型
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'status',
        'featured_image',
        'user_id',
        'published_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'user_id',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user that owns the post.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the comments for the post.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * The tags that belong to the post.
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    /**
     * Scope a query to only include published posts.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include posts of a given type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the post's excerpt.
     */
    public function getExcerptAttribute($value)
    {
        return $value ?: \Str::limit(strip_tags($this->content), 160);
    }

    /**
     * Set the post's title.
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = \Str::slug($value);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
```

### 资源类开发

#### API资源类
```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'excerpt' => $this->excerpt,
            'content' => $this->when($request->routeIs('posts.show'), $this->content),
            'status' => $this->status,
            'featured_image' => $this->featured_image,
            'published_at' => $this->published_at?->format('c'),
            'created_at' => $this->created_at->format('c'),
            'updated_at' => $this->updated_at->format('c'),

            // 关联资源
            'user' => new UserResource($this->whenLoaded('user')),
            'tags' => TagResource::collection($this->whenLoaded('tags')),
            'comments' => CommentResource::collection($this->whenLoaded('comments')),

            // 条件字段
            'edit_url' => $this->when(
                $request->user()?->can('update', $this->resource),
                route('posts.edit', $this->id)
            ),

            // 计算字段
            'comments_count' => $this->whenCounted('comments'),
            'reading_time' => $this->reading_time,
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'version' => '1.0',
                'api_url' => $request->url(),
            ],
        ];
    }
}
```

#### 资源集合类
```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class PostCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'links' => [
                'self' => 'link-value',
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'total_posts' => $this->collection->count(),
                'published_posts' => $this->collection->where('status', 'published')->count(),
            ],
        ];
    }
}
```

### 数据库操作

#### 数据库迁移
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('content');
            $table->text('excerpt')->nullable();
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->string('featured_image')->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'published_at']);
            $table->index(['user_id', 'status']);
            $table->fullText(['title', 'content']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('posts');
    }
};
```

#### 数据填充
```php
<?php

namespace Database\Seeders;

use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Seeder;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->info('No users found. Creating users first...');
            User::factory(10)->create();
            $users = User::all();
        }

        $users->each(function ($user) {
            Post::factory()
                ->count(rand(1, 5))
                ->for($user)
                ->create();
        });

        $this->command->info('Posts created successfully!');
    }
}
```

#### 模型工厂
```php
<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $title = $this->faker->sentence();

        return [
            'title' => $title,
            'slug' => \Str::slug($title),
            'content' => $this->faker->paragraphs(5, true),
            'excerpt' => $this->faker->paragraph(),
            'status' => $this->faker->randomElement(['draft', 'published', 'archived']),
            'featured_image' => $this->faker->imageUrl(800, 600, 'posts'),
            'user_id' => User::factory(),
            'published_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Indicate that the post is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'published_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ]);
    }

    /**
     * Indicate that the post is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }
}
```

### 服务类开发

#### 服务类示例
```php
<?php

namespace App\Services;

use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class PostService
{
    /**
     * Get paginated posts with filters.
     */
    public function getPaginatedPosts(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Post::with(['user', 'tags'])
            ->when($filters['search'] ?? null, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('title', 'like', "%{$search}%")
                          ->orWhere('content', 'like', "%{$search}%");
                });
            })
            ->when($filters['status'] ?? null, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($filters['user_id'] ?? null, function ($query, $userId) {
                $query->where('user_id', $userId);
            })
            ->when($filters['tag'] ?? null, function ($query, $tag) {
                $query->whereHas('tags', function ($query) use ($tag) {
                    $query->where('name', $tag);
                });
            });

        return $query->latest('published_at')->paginate($perPage);
    }

    /**
     * Create a new post.
     */
    public function createPost(array $data, User $user): Post
    {
        return DB::transaction(function () use ($data, $user) {
            $post = $user->posts()->create([
                'title' => $data['title'],
                'content' => $data['content'],
                'excerpt' => $data['excerpt'] ?? null,
                'status' => $data['status'] ?? 'draft',
                'featured_image' => $data['featured_image'] ?? null,
                'published_at' => $data['status'] === 'published' ? now() : null,
            ]);

            if (!empty($data['tags'])) {
                $post->tags()->sync($data['tags']);
            }

            // 清除相关缓存
            $this->clearPostCache();

            return $post->load(['user', 'tags']);
        });
    }

    /**
     * Update an existing post.
     */
    public function updatePost(Post $post, array $data): Post
    {
        return DB::transaction(function () use ($post, $data) {
            $updateData = array_filter([
                'title' => $data['title'] ?? $post->title,
                'content' => $data['content'] ?? $post->content,
                'excerpt' => $data['excerpt'] ?? $post->excerpt,
                'status' => $data['status'] ?? $post->status,
                'featured_image' => $data['featured_image'] ?? $post->featured_image,
            ]);

            // 如果状态改为已发布且之前未发布，设置发布时间
            if (($data['status'] ?? $post->status) === 'published' && !$post->published_at) {
                $updateData['published_at'] = now();
            }

            $post->update($updateData);

            if (isset($data['tags'])) {
                $post->tags()->sync($data['tags']);
            }

            // 清除相关缓存
            $this->clearPostCache();

            return $post->load(['user', 'tags']);
        });
    }

    /**
     * Get popular posts.
     */
    public function getPopularPosts(int $limit = 10): Collection
    {
        return Cache::remember('popular_posts', 3600, function () use ($limit) {
            return Post::published()
                ->withCount('comments')
                ->orderBy('comments_count', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Clear post-related cache.
     */
    private function clearPostCache(): void
    {
        Cache::forget('popular_posts');
        Cache::tags(['posts'])->flush();
    }
}
```

### 事件和监听器

#### 事件类
```php
<?php

namespace App\Events;

use App\Models\Post;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostPublished
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Post $post
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
```

#### 监听器类
```php
<?php

namespace App\Listeners;

use App\Events\PostPublished;
use App\Mail\PostPublishedMail;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendPostPublishedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PostPublished $event): void
    {
        // 获取所有订阅者
        $subscribers = User::whereNotNull('email_verified_at')
            ->where('subscribed_to_notifications', true)
            ->get();

        // 发送邮件通知
        foreach ($subscribers as $subscriber) {
            Mail::to($subscriber)->queue(new PostPublishedMail($event->post, $subscriber));
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(PostPublished $event, Throwable $exception): void
    {
        // 处理失败的任务
        \Log::error('Failed to send post published notification', [
            'post_id' => $event->post->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
```

### 队列和任务

#### 任务类
```php
<?php

namespace App\Jobs;

use App\Models\Post;
use App\Services\PyApi\ImageOptimizationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OptimizePostImages implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Post $post
    ) {}

    /**
     * Execute the job.
     */
    public function handle(ImageOptimizationService $service): void
    {
        if ($this->post->featured_image) {
            $optimizedPath = $service->optimize($this->post->featured_image);

            $this->post->update([
                'featured_image' => $optimizedPath
            ]);
        }

        // 优化内容中的图像
        $optimizedContent = $service->optimizeContentImages($this->post->content);

        $this->post->update([
            'content' => $optimizedContent
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        \Log::error('Failed to optimize post images', [
            'post_id' => $this->post->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
```

### 测试开发

#### 功能测试
```php
<?php

namespace Tests\Feature;

use App\Models\Post;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PostTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that a user can view posts.
     */
    public function test_user_can_view_posts(): void
    {
        $posts = Post::factory()->count(3)->create();

        $response = $this->get('/posts');

        $response->assertStatus(200);
        $response->assertViewIs('posts.index');
        $response->assertViewHas('posts');
    }

    /**
     * Test that authenticated user can create a post.
     */
    public function test_authenticated_user_can_create_post(): void
    {
        $user = User::factory()->create();

        $postData = [
            'title' => $this->faker->sentence(),
            'content' => $this->faker->paragraphs(3, true),
            'status' => 'published',
        ];

        $response = $this->actingAs($user)->post('/posts', $postData);

        $response->assertRedirect();
        $this->assertDatabaseHas('posts', [
            'title' => $postData['title'],
            'user_id' => $user->id,
        ]);
    }

    /**
     * Test that guest cannot create a post.
     */
    public function test_guest_cannot_create_post(): void
    {
        $postData = [
            'title' => $this->faker->sentence(),
            'content' => $this->faker->paragraphs(3, true),
        ];

        $response = $this->post('/posts', $postData);

        $response->assertRedirect('/login');
        $this->assertDatabaseMissing('posts', $postData);
    }

    /**
     * Test post validation.
     */
    public function test_post_validation(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/posts', []);

        $response->assertSessionHasErrors(['title', 'content']);
    }

    /**
     * Test user can only edit their own posts.
     */
    public function test_user_can_only_edit_own_posts(): void
    {
        $user = User::factory()->create();
        $otherUser = User::factory()->create();
        $post = Post::factory()->for($otherUser)->create();

        $response = $this->actingAs($user)->get("/posts/{$post->id}/edit");

        $response->assertStatus(403);
    }
}
```

#### 单元测试
```php
<?php

namespace Tests\Unit;

use App\Models\Post;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PostModelTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test post belongs to user.
     */
    public function test_post_belongs_to_user(): void
    {
        $user = User::factory()->create();
        $post = Post::factory()->for($user)->create();

        $this->assertInstanceOf(User::class, $post->user);
        $this->assertEquals($user->id, $post->user->id);
    }

    /**
     * Test post slug is generated from title.
     */
    public function test_post_slug_is_generated_from_title(): void
    {
        $post = Post::factory()->create(['title' => 'This is a Test Title']);

        $this->assertEquals('this-is-a-test-title', $post->slug);
    }

    /**
     * Test published scope.
     */
    public function test_published_scope(): void
    {
        Post::factory()->create(['status' => 'published']);
        Post::factory()->create(['status' => 'draft']);
        Post::factory()->create(['status' => 'archived']);

        $publishedPosts = Post::published()->get();

        $this->assertCount(1, $publishedPosts);
        $this->assertEquals('published', $publishedPosts->first()->status);
    }

    /**
     * Test excerpt accessor.
     */
    public function test_excerpt_accessor(): void
    {
        $content = str_repeat('This is a long content. ', 50);
        $post = Post::factory()->create([
            'content' => $content,
            'excerpt' => null,
        ]);

        $this->assertNotNull($post->excerpt);
        $this->assertLessThanOrEqual(160, strlen($post->excerpt));
    }
}
```

### 缓存策略

#### 缓存使用示例
```php
use Illuminate\Support\Facades\Cache;

// 基本缓存
$posts = Cache::remember('posts.all', 3600, function () {
    return Post::published()->with('user')->get();
});

// 缓存标签
Cache::tags(['posts', 'users'])->put('posts.featured', $featuredPosts, 3600);
Cache::tags(['posts'])->flush();

// 分布式锁
$lock = Cache::lock('post.update.' . $postId, 10);

if ($lock->get()) {
    try {
        // 执行更新操作
        $post->update($data);
    } finally {
        $lock->release();
    }
}

// 原子操作
$views = Cache::increment('post.views.' . $postId);
Cache::decrement('post.likes.' . $postId);
```

### 配置管理

#### 环境配置
```ini
# .env
APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:your-app-key
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
```

### 部署和优化

#### 生产环境优化命令
```bash
# 配置缓存
php artisan config:cache

# 路由缓存
php artisan route:cache

# 视图缓存
php artisan view:cache

# 事件缓存
php artisan event:cache

# 清除所有缓存
php artisan optimize:clear

# 生产环境优化
php artisan optimize

# 数据库迁移
php artisan migrate --force

# 队列工作进程
php artisan queue:work --daemon --tries=3
```

#### Nginx配置
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name example.com;
    root /srv/example.com/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 最佳实践

#### 1. 代码组织
- 使用服务类处理业务逻辑
- 控制器保持简洁，只处理HTTP请求
- 使用资源类格式化API响应
- 合理使用事件和监听器解耦代码

#### 2. 性能优化
- 使用Eloquent预加载避免N+1查询
- 合理使用缓存策略
- 使用队列处理耗时任务
- 数据库索引优化

#### 3. 安全考虑
- 使用表单请求验证输入
- 实施CSRF保护
- 使用中间件进行认证和授权
- 定期更新依赖包

#### 4. 测试策略
- 编写功能测试覆盖主要业务流程
- 单元测试覆盖关键业务逻辑
- 使用工厂类生成测试数据
- 模拟外部服务依赖

Laravel 10.x 提供了完整的全栈开发解决方案，遵循这些规范将确保项目的可维护性、可扩展性和安全性。
```