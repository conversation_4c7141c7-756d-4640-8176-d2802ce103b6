<?php

require 'vendor/autoload.php';
require 'bootstrap/app.php';

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

try {
    echo "开始插入角色测试数据...\n";
    
    // 1. 先插入角色分类数据（如果不存在）
    $categories = [
        ['name' => '现代都市', 'slug' => 'modern-urban', 'description' => '现代都市背景角色', 'sort_order' => 1],
        ['name' => '古代武侠', 'slug' => 'ancient-martial', 'description' => '古代武侠背景角色', 'sort_order' => 2],
        ['name' => '科幻未来', 'slug' => 'sci-fi-future', 'description' => '科幻未来背景角色', 'sort_order' => 3],
        ['name' => '奇幻魔法', 'slug' => 'fantasy-magic', 'description' => '奇幻魔法背景角色', 'sort_order' => 4],
    ];
    
    foreach ($categories as $category) {
        $existing = DB::table('character_categories')
            ->where('name', $category['name'])
            ->first();

        if (!$existing) {
            DB::table('character_categories')->insert([
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'sort_order' => $category['sort_order'],
                'is_active' => true,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
            echo "插入角色分类: {$category['name']}\n";
        } else {
            echo "角色分类已存在: {$category['name']}\n";
        }
    }
    
    // 获取分类ID
    $modernCategory = DB::table('character_categories')->where('name', '现代都市')->first();
    $ancientCategory = DB::table('character_categories')->where('name', '古代武侠')->first();
    
    // 2. 插入角色数据
    $characters = [
        [
            'name' => '林雨萱',
            'description' => '一位温柔善良的现代都市女性，拥有清澈的眼神和甜美的笑容。她是一名优秀的设计师，热爱生活，善于倾听他人的心声。',
            'category_id' => $modernCategory->id,
            'gender' => '女',
            'age_range' => '25-30',
            'personality' => '温柔、善良、细心、有责任感、富有同情心',
            'background' => '出生于书香门第，从小接受良好教育。大学毕业后成为一名室内设计师，用自己的专业技能为客户打造温馨的家居环境。',
            'appearance' => '身材修长，五官精致，长发飘逸，气质优雅。喜欢穿着简约而不失时尚的服装。',
            'avatar' => 'characters/avatars/female_character_01.jpg',
            'images' => json_encode([
                'characters/images/female_character_01_full.jpg'
            ]),
            'voice_config' => json_encode([
                'voice_type' => 'female_sweet',
                'voice_sample' => 'characters/voices/female_voice_01.mp3',
                'pitch' => 'medium-high',
                'speed' => 'normal',
                'emotion' => 'gentle'
            ]),
            'style_preferences' => json_encode([
                'preferred_styles' => ['现代简约', '温馨浪漫', '清新自然'],
                'color_preferences' => ['粉色', '白色', '浅蓝色'],
                'scene_types' => ['日常生活', '工作场景', '休闲时光']
            ]),
            'tags' => json_encode(['温柔', '设计师', '现代女性', '善良', '专业']),
            'is_active' => true,
            'is_premium' => false,
            'is_featured' => true,
            'sort_order' => 1,
            'rating' => 4.8,
            'rating_count' => 156,
            'created_by' => 1,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ],
        [
            'name' => '陈浩然',
            'description' => '一位成熟稳重的现代都市男性，拥有坚毅的面容和深邃的眼神。他是一名成功的企业家，做事果断，但内心温暖。',
            'category_id' => $modernCategory->id,
            'gender' => '男',
            'age_range' => '30-35',
            'personality' => '成熟、稳重、果断、有领导力、内心温暖',
            'background' => '白手起家的企业家，通过自己的努力建立了一家科技公司。虽然工作繁忙，但始终保持着对家人和朋友的关爱。',
            'appearance' => '身材高大，轮廓分明，短发整齐，眼神坚定。穿着得体的商务装，散发着成功人士的气质。',
            'avatar' => 'characters/avatars/male_character_01.jpg',
            'images' => json_encode([
                'characters/images/male_character_01_full.jpg'
            ]),
            'voice_config' => json_encode([
                'voice_type' => 'male_mature',
                'voice_sample' => 'characters/voices/male_voice_01.mp3',
                'pitch' => 'medium-low',
                'speed' => 'normal',
                'emotion' => 'confident'
            ]),
            'style_preferences' => json_encode([
                'preferred_styles' => ['商务正式', '现代简约', '高端大气'],
                'color_preferences' => ['黑色', '深蓝色', '灰色'],
                'scene_types' => ['商务会议', '办公场景', '社交活动']
            ]),
            'tags' => json_encode(['成熟', '企业家', '领导者', '稳重', '成功']),
            'is_active' => true,
            'is_premium' => true,
            'is_featured' => true,
            'sort_order' => 2,
            'rating' => 4.9,
            'rating_count' => 203,
            'created_by' => 1,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]
    ];
    
    foreach ($characters as $character) {
        $existing = DB::table('character_library')
            ->where('name', $character['name'])
            ->first();

        if (!$existing) {
            $id = DB::table('character_library')->insertGetId($character);
            echo "插入角色: {$character['name']} (ID: {$id})\n";
        } else {
            echo "角色已存在: {$character['name']}\n";
        }
    }
    
    echo "\n角色测试数据插入完成！\n";
    
    // 显示插入的数据
    $characters = DB::table('character_library')
        ->join('character_categories', 'character_library.category_id', '=', 'character_categories.id')
        ->select('character_library.*', 'character_categories.name as category_name')
        ->get();
        
    echo "\n当前角色库数据：\n";
    foreach ($characters as $char) {
        echo "ID: {$char->id}, 名称: {$char->name}, 分类: {$char->category_name}, 性别: {$char->gender}\n";
        echo "头像: {$char->avatar}\n";
        echo "评分: {$char->rating} ({$char->rating_count}人评价)\n";
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}
