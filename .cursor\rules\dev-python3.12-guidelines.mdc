---
description: Python 3.12 Development Guidelines - High-level programming language
globs: ["python/**/*.py", "**/*.py"]
alwaysApply: true
---

# PYTHON 3.12 开发指南

## 语言概述

Python是一种高级、解释型、通用的编程语言。其设计哲学强调代码的可读性，并使用显著的缩进。Python支持多种编程范式，包括结构化、面向对象和函数式编程。

## 核心思维原则应用

在Python开发中应用以下思维原则：
- **系统思维**: 从模块设计到包管理进行整体规划
- **辩证思维**: 权衡代码简洁性与性能优化
- **创新思维**: 利用Python生态系统的丰富库
- **批判性思维**: 评估算法效率和代码可维护性

## Python 3.12新特性

### 改进的错误消息
- 更精确的语法错误位置指示
- 改进的类型错误消息
- 更好的异常回溯信息

### 性能改进
- 更快的启动时间
- 优化的字节码生成
- 改进的内存使用

### 新语法特性
```python
# 改进的f-string语法
name = "world"
message = f"Hello, {name}!"

# 类型提示改进
from typing import TypeVar, Generic

T = TypeVar('T')

class Stack(Generic[T]):
    def __init__(self) -> None:
        self._items: list[T] = []
    
    def push(self, item: T) -> None:
        self._items.append(item)
    
    def pop(self) -> T:
        return self._items.pop()
```

## 基础语法

### 变量和数据类型
```python
# 基本数据类型
integer_var = 42
float_var = 3.14
string_var = "Hello, World!"
boolean_var = True
list_var = [1, 2, 3, 4, 5]
tuple_var = (1, 2, 3)
dict_var = {"key": "value", "number": 42}
set_var = {1, 2, 3, 4, 5}

# 类型注解
name: str = "Alice"
age: int = 30
scores: list[int] = [85, 90, 78]
user_data: dict[str, str] = {"name": "Bob", "email": "<EMAIL>"}
```

### 控制流
```python
# 条件语句
if condition:
    # 执行代码
    pass
elif another_condition:
    # 执行其他代码
    pass
else:
    # 默认执行代码
    pass

# 循环
for item in iterable:
    # 处理每个项目
    pass

for i in range(10):
    # 执行10次
    pass

while condition:
    # 循环执行
    pass

# 列表推导式
squares = [x**2 for x in range(10)]
even_squares = [x**2 for x in range(10) if x % 2 == 0]

# 字典推导式
word_lengths = {word: len(word) for word in ["hello", "world", "python"]}
```

### 函数定义
```python
# 基本函数
def greet(name: str) -> str:
    """返回问候消息"""
    return f"Hello, {name}!"

# 带默认参数的函数
def greet_with_title(name: str, title: str = "Mr.") -> str:
    return f"Hello, {title} {name}!"

# 可变参数
def sum_all(*args: int) -> int:
    return sum(args)

def print_info(**kwargs: str) -> None:
    for key, value in kwargs.items():
        print(f"{key}: {value}")

# Lambda函数
square = lambda x: x**2
numbers = [1, 2, 3, 4, 5]
squared_numbers = list(map(square, numbers))
```

## 面向对象编程

### 类定义
```python
from typing import Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

class Person:
    """人员基类"""
    
    def __init__(self, name: str, age: int) -> None:
        self.name = name
        self.age = age
    
    def introduce(self) -> str:
        return f"Hi, I'm {self.name} and I'm {self.age} years old."
    
    def __str__(self) -> str:
        return f"Person(name='{self.name}', age={self.age})"
    
    def __repr__(self) -> str:
        return self.__str__()

# 继承
class Student(Person):
    def __init__(self, name: str, age: int, student_id: str) -> None:
        super().__init__(name, age)
        self.student_id = student_id
    
    def study(self, subject: str) -> str:
        return f"{self.name} is studying {subject}"

# 数据类
@dataclass
class Point:
    x: float
    y: float
    
    def distance_from_origin(self) -> float:
        return (self.x**2 + self.y**2)**0.5

# 抽象基类
class Shape(ABC):
    @abstractmethod
    def area(self) -> float:
        pass
    
    @abstractmethod
    def perimeter(self) -> float:
        pass

class Rectangle(Shape):
    def __init__(self, width: float, height: float) -> None:
        self.width = width
        self.height = height
    
    def area(self) -> float:
        return self.width * self.height
    
    def perimeter(self) -> float:
        return 2 * (self.width + self.height)
```

### 属性和装饰器
```python
class Temperature:
    def __init__(self, celsius: float = 0) -> None:
        self._celsius = celsius
    
    @property
    def celsius(self) -> float:
        return self._celsius
    
    @celsius.setter
    def celsius(self, value: float) -> None:
        if value < -273.15:
            raise ValueError("Temperature cannot be below absolute zero")
        self._celsius = value
    
    @property
    def fahrenheit(self) -> float:
        return (self._celsius * 9/5) + 32
    
    @fahrenheit.setter
    def fahrenheit(self, value: float) -> None:
        self.celsius = (value - 32) * 5/9

# 类方法和静态方法
class MathUtils:
    PI = 3.14159
    
    @classmethod
    def circle_area(cls, radius: float) -> float:
        return cls.PI * radius**2
    
    @staticmethod
    def add(a: float, b: float) -> float:
        return a + b
```

## 异常处理

### 基本异常处理
```python
try:
    # 可能出错的代码
    result = 10 / 0
except ZeroDivisionError as e:
    print(f"Division by zero error: {e}")
except ValueError as e:
    print(f"Value error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
else:
    # 没有异常时执行
    print("Operation successful")
finally:
    # 总是执行
    print("Cleanup code")

# 自定义异常
class CustomError(Exception):
    """自定义异常类"""
    def __init__(self, message: str, error_code: int = 0) -> None:
        super().__init__(message)
        self.error_code = error_code

def validate_age(age: int) -> None:
    if age < 0:
        raise CustomError("Age cannot be negative", error_code=1001)
    if age > 150:
        raise CustomError("Age seems unrealistic", error_code=1002)
```

## 文件操作

### 文件读写
```python
from pathlib import Path
import json
import csv

# 文本文件操作
def read_text_file(filename: str) -> str:
    """读取文本文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"File {filename} not found")
        return ""

def write_text_file(filename: str, content: str) -> None:
    """写入文本文件"""
    with open(filename, 'w', encoding='utf-8') as file:
        file.write(content)

# JSON操作
def save_to_json(data: dict, filename: str) -> None:
    """保存数据到JSON文件"""
    with open(filename, 'w', encoding='utf-8') as file:
        json.dump(data, file, indent=2, ensure_ascii=False)

def load_from_json(filename: str) -> dict:
    """从JSON文件加载数据"""
    with open(filename, 'r', encoding='utf-8') as file:
        return json.load(file)

# CSV操作
def read_csv_file(filename: str) -> list[dict]:
    """读取CSV文件"""
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        return list(reader)

def write_csv_file(filename: str, data: list[dict]) -> None:
    """写入CSV文件"""
    if not data:
        return
    
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)

# 使用pathlib
def process_directory(directory_path: str) -> None:
    """处理目录中的文件"""
    path = Path(directory_path)
    
    if not path.exists():
        print(f"Directory {directory_path} does not exist")
        return
    
    for file_path in path.iterdir():
        if file_path.is_file() and file_path.suffix == '.txt':
            print(f"Processing: {file_path.name}")
            # 处理文件
```

## 模块和包

### 模块导入
```python
# 标准库导入
import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Optional, Union

# 第三方库导入
import requests
import pandas as pd
import numpy as np

# 本地模块导入
from .utils import helper_function
from ..models import User, Product
from mypackage.database import DatabaseConnection

# 条件导入
try:
    import optional_module
except ImportError:
    optional_module = None

def use_optional_feature():
    if optional_module is not None:
        return optional_module.some_function()
    else:
        return "Optional feature not available"
```

### 包结构
```
mypackage/
    __init__.py
    core/
        __init__.py
        models.py
        utils.py
    api/
        __init__.py
        views.py
        serializers.py
    tests/
        __init__.py
        test_models.py
        test_api.py
```

## 常用内置函数和方法

### 字符串操作
```python
text = "Hello, World!"

# 常用字符串方法
print(text.lower())           # hello, world!
print(text.upper())           # HELLO, WORLD!
print(text.strip())           # 去除首尾空白
print(text.replace("World", "Python"))  # Hello, Python!
print(text.split(","))        # ['Hello', ' World!']
print("".join(["a", "b", "c"]))  # abc

# 字符串格式化
name = "Alice"
age = 30
print(f"Name: {name}, Age: {age}")
print("Name: {}, Age: {}".format(name, age))
print("Name: {name}, Age: {age}".format(name=name, age=age))
```

### 列表和字典操作
```python
# 列表操作
numbers = [1, 2, 3, 4, 5]
numbers.append(6)           # 添加元素
numbers.extend([7, 8])      # 扩展列表
numbers.insert(0, 0)        # 插入元素
numbers.remove(3)           # 删除指定值
popped = numbers.pop()      # 弹出最后一个元素
numbers.sort()              # 排序
numbers.reverse()           # 反转

# 字典操作
data = {"name": "Alice", "age": 30}
data["email"] = "<EMAIL>"  # 添加键值对
data.update({"city": "New York"})    # 更新字典
value = data.get("phone", "N/A")     # 安全获取值
keys = list(data.keys())             # 获取所有键
values = list(data.values())         # 获取所有值
items = list(data.items())           # 获取所有键值对
```

## 最佳实践

### 代码风格
- 遵循PEP 8编码规范
- 使用有意义的变量和函数名
- 添加适当的注释和文档字符串
- 保持函数简短和专一
- 使用类型提示提高代码可读性

### 性能优化
```python
# 使用生成器表达式而不是列表推导式（当处理大数据时）
sum_of_squares = sum(x**2 for x in range(1000000))

# 使用集合进行快速查找
valid_ids = {1, 2, 3, 4, 5}
if user_id in valid_ids:  # O(1) 查找
    process_user(user_id)

# 使用字典的get方法避免KeyError
count = counter.get(key, 0) + 1

# 使用enumerate而不是range(len())
for i, item in enumerate(items):
    print(f"{i}: {item}")
```

### 测试
```python
import unittest
from unittest.mock import patch, MagicMock

class TestCalculator(unittest.TestCase):
    def setUp(self):
        self.calc = Calculator()
    
    def test_addition(self):
        result = self.calc.add(2, 3)
        self.assertEqual(result, 5)
    
    def test_division_by_zero(self):
        with self.assertRaises(ZeroDivisionError):
            self.calc.divide(10, 0)
    
    @patch('requests.get')
    def test_api_call(self, mock_get):
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "success"}
        mock_get.return_value = mock_response
        
        result = self.api_client.fetch_data()
        self.assertEqual(result["status"], "success")

if __name__ == '__main__':
    unittest.main()
```

这些指南基于Python 3.12官方文档，为Python开发提供了全面的技术规范和最佳实践。
