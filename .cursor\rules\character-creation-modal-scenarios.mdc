# Py视频创作工具 - 角色创建罩层场景化配置实现

## 📋 概述

本文档详细说明了角色创建罩层在Py视频创作工具不同业务场景下的参数化配置实现方案，确保同一个组件能够适应各种不同的使用需求。

## 🎯 场景分类

### 1. 全角色列表模式 (Full Mode)

**适用场景**: 角色库管理、通用角色浏览、角色选择

```javascript
// 场景1: 角色库管理页面
PyVideoTool.CharacterCreationModal.open({
    mode: 'full',
    title: '角色库管理',
    display: {
        showStyleSelection: true,
        showModeSelection: true,
        showAdvancedOptions: true,
        showPreview: true,
        enableBatchCreate: true
    },
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enableStyleCustomization: true,
        enablePublish: true,
        enableSave: true
    },
    callbacks: {
        onSuccess: (result) => {
            // 添加到角色库
            addToCharacterLibrary(result.data.character);
            refreshCharacterList();
        }
    }
});
```

### 2. 条件筛选模式 (Filtered Mode)

**适用场景**: 项目特定角色创建、角色绑定、场景匹配

```javascript
// 场景2: 项目创建时需要特定角色
PyVideoTool.CharacterCreationModal.open({
    mode: 'filtered',
    title: '为项目创建角色',
    filters: {
        projectType: 'video',
        styles: ['写实风3.0', '动漫可爱3.0', '武侠古风3.0'],
        tags: ['现代', '都市', '职场'],
        permissions: ['create', 'use'],
        maxCount: 5
    },
    display: {
        showStyleSelection: true,
        showModeSelection: true,
        showPreview: true
    },
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enablePublish: false  // 项目内使用，不需要发布
    },
    callbacks: {
        onSuccess: (result) => {
            // 直接添加到当前项目
            addCharacterToProject(result.data.character);
        }
    }
});

// 场景3: 分镜绑定时创建角色
PyVideoTool.CharacterCreationModal.open({
    mode: 'filtered',
    title: '为分镜创建角色',
    filters: {
        projectType: getCurrentProject().type,
        styles: getProjectCompatibleStyles(),
        tags: getStoryboardRequirements(),
        permissions: ['create', 'bind']
    },
    display: {
        showStyleSelection: true,
        showModeSelection: false,  // 简化界面
        showPreview: true
    },
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enablePublish: false
    },
    callbacks: {
        onSuccess: (result) => {
            // 自动绑定到分镜位置
            bindCharacterToStoryboard(
                result.data.character.id,
                getCurrentStoryboardPosition()
            );
        }
    }
});
```

### 3. 快速创建模式 (Quick Mode)

**适用场景**: 批量操作、快速原型、预设配置

```javascript
// 场景4: 快速创建常用角色
PyVideoTool.CharacterCreationModal.open({
    mode: 'quick',
    title: '快速创建角色',
    presets: {
        style: '写实风3.0',
        mode: 'ai_created',
        platform: 'LiblibAI',
        quickSettings: {
            gender: 'male',
            age: 'adult',
            style: 'modern'
        }
    },
    display: {
        showStyleSelection: false,  // 使用预设
        showModeSelection: false,   // 使用预设
        showPreview: true,
        compactMode: true          // 紧凑界面
    },
    features: {
        enableUpload: false,       // 仅AI生成
        enableAIGeneration: true,
        enablePublish: false
    },
    callbacks: {
        onSuccess: (result) => {
            // 快速添加到工作区
            addToWorkspace(result.data.character);
        }
    }
});

// 场景5: 批量创建角色
PyVideoTool.CharacterCreationModal.open({
    mode: 'quick',
    title: '批量创建角色',
    presets: {
        batchConfig: {
            count: 5,
            styles: ['写实风3.0', '动漫可爱3.0'],
            variations: ['male', 'female']
        }
    },
    display: {
        enableBatchCreate: true,
        showProgress: true,
        compactMode: true
    },
    features: {
        enableAIGeneration: true,
        enableBatchProcessing: true
    },
    callbacks: {
        onSuccess: (results) => {
            // 处理批量创建结果
            results.forEach(result => {
                addToCharacterPool(result.data.character);
            });
        },
        onProgress: (progress) => {
            updateBatchProgress(progress);
        }
    }
});
```

### 4. 高级创建模式 (Advanced Mode)

**适用场景**: 专业用户、高级定制、复杂需求

```javascript
// 场景6: 专业角色定制
PyVideoTool.CharacterCreationModal.open({
    mode: 'advanced',
    title: '高级角色定制',
    display: {
        showStyleSelection: true,
        showModeSelection: true,
        showAdvancedOptions: true,
        showPreview: true,
        showParameterTuning: true,  // 高级参数调节
        showExpertMode: true        // 专家模式
    },
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enableStyleCustomization: true,
        enableParameterTuning: true,
        enableAdvancedFilters: true,
        enableExportOptions: true,
        enableVersionControl: true
    },
    advanced: {
        allowCustomPrompts: true,
        enableFinetuning: true,
        showTechnicalDetails: true,
        enableAPIAccess: true
    },
    callbacks: {
        onSuccess: (result) => {
            // 保存到专业作品集
            saveToPortfolio(result.data.character);
        },
        onParameterChange: (params) => {
            // 实时预览参数变化
            updatePreview(params);
        }
    }
});
```

### 5. 只读预览模式 (Preview Mode)

**适用场景**: 角色展示、审核流程、信息查看

```javascript
// 场景7: 角色审核预览
PyVideoTool.CharacterCreationModal.open({
    mode: 'preview',
    title: '角色审核',
    data: {
        characterId: 'char_123456',
        readonly: true
    },
    display: {
        showStyleSelection: false,
        showModeSelection: false,
        showPreview: true,
        showDetails: true,
        showMetadata: true
    },
    features: {
        enableUpload: false,
        enableAIGeneration: false,
        enableEdit: false,
        enableApproval: true,      // 审核功能
        enableReject: true
    },
    callbacks: {
        onApprove: (characterId) => {
            approveCharacter(characterId);
        },
        onReject: (characterId, reason) => {
            rejectCharacter(characterId, reason);
        }
    }
});
```

### 6. 批量操作模式 (Batch Mode)

**适用场景**: 大量角色处理、批量导入、批量编辑

```javascript
// 场景8: 批量角色导入
PyVideoTool.CharacterCreationModal.open({
    mode: 'batch',
    title: '批量角色导入',
    batch: {
        maxCount: 50,
        supportedFormats: ['jpg', 'png', 'webp'],
        autoProcessing: true
    },
    display: {
        showBatchUpload: true,
        showProgress: true,
        showBatchPreview: true
    },
    features: {
        enableBatchUpload: true,
        enableBatchProcessing: true,
        enableBatchValidation: true,
        enableProgressTracking: true
    },
    callbacks: {
        onBatchSuccess: (results) => {
            // 处理批量导入结果
            processBatchResults(results);
        },
        onBatchProgress: (progress) => {
            updateBatchProgress(progress);
        },
        onBatchError: (errors) => {
            handleBatchErrors(errors);
        }
    }
});
```

## 🎛️ 权限级别配置

### 基础用户权限

```javascript
const basicUserConfig = {
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enableStyleCustomization: false,  // 限制高级功能
        enablePublish: false,
        enableAdvancedOptions: false
    },
    limits: {
        maxCreationsPerDay: 10,
        maxFileSize: '5MB',
        availableStyles: ['写实风3.0', '动漫可爱3.0']
    }
};
```

### 高级用户权限

```javascript
const premiumUserConfig = {
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enableStyleCustomization: true,
        enablePublish: true,
        enableAdvancedOptions: true,
        enableBatchCreate: true
    },
    limits: {
        maxCreationsPerDay: 100,
        maxFileSize: '20MB',
        availableStyles: 'all'
    }
};
```

### 专业用户权限

```javascript
const professionalUserConfig = {
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enableStyleCustomization: true,
        enablePublish: true,
        enableAdvancedOptions: true,
        enableBatchCreate: true,
        enableAPIAccess: true,
        enableCustomPrompts: true
    },
    limits: {
        maxCreationsPerDay: 'unlimited',
        maxFileSize: '100MB',
        availableStyles: 'all',
        enableCustomStyles: true
    }
};
```

## 🎨 UI主题配置

### 默认主题

```javascript
const defaultTheme = {
    colors: {
        primary: '#2196F3',
        secondary: '#FFC107',
        background: '#FFFFFF',
        surface: '#F5F5F5',
        text: '#333333'
    },
    layout: {
        modalWidth: '800px',
        modalHeight: '600px',
        borderRadius: '8px',
        padding: '20px'
    }
};
```

### 深色主题

```javascript
const darkTheme = {
    colors: {
        primary: '#1976D2',
        secondary: '#FF9800',
        background: '#121212',
        surface: '#1E1E1E',
        text: '#FFFFFF'
    },
    layout: {
        modalWidth: '800px',
        modalHeight: '600px',
        borderRadius: '8px',
        padding: '20px'
    }
};
```

### 自定义主题

```javascript
PyVideoTool.CharacterCreationModal.open({
    mode: 'full',
    theme: {
        name: 'custom',
        colors: {
            primary: '#9C27B0',
            secondary: '#4CAF50',
            background: '#FAFAFA',
            surface: '#FFFFFF',
            text: '#212121'
        },
        layout: {
            modalWidth: '900px',
            modalHeight: '700px',
            borderRadius: '12px',
            padding: '24px'
        }
    }
});
```

## 📱 设备适配配置

### 桌面端配置

```javascript
const desktopConfig = {
    display: {
        modalSize: 'large',
        showAllFeatures: true,
        enableKeyboardShortcuts: true,
        enableDragDrop: true
    },
    layout: {
        columns: 3,
        gridView: true,
        sidePanel: true
    }
};
```

### 平板端配置

```javascript
const tabletConfig = {
    display: {
        modalSize: 'medium',
        showEssentialFeatures: true,
        enableTouchGestures: true,
        responsiveLayout: true
    },
    layout: {
        columns: 2,
        gridView: true,
        sidePanel: false
    }
};
```

### 移动端配置

```javascript
const mobileConfig = {
    display: {
        modalSize: 'fullscreen',
        showBasicFeatures: true,
        enableTouchGestures: true,
        mobileOptimized: true
    },
    layout: {
        columns: 1,
        listView: true,
        bottomSheet: true
    }
};
```

## 🔧 配置工厂函数

### 场景配置生成器

```javascript
class CharacterModalConfigFactory {
    static createProjectConfig(projectType, userLevel) {
        const baseConfig = {
            mode: 'filtered',
            filters: { projectType }
        };
        
        return this.applyUserLevel(baseConfig, userLevel);
    }
    
    static createBindingConfig(storyboardData) {
        return {
            mode: 'filtered',
            title: '为分镜创建角色',
            filters: {
                projectType: storyboardData.projectType,
                tags: storyboardData.requirements,
                styles: storyboardData.compatibleStyles
            },
            callbacks: {
                onSuccess: (result) => {
                    bindToStoryboard(result, storyboardData.positionId);
                }
            }
        };
    }
    
    static createQuickConfig(presets) {
        return {
            mode: 'quick',
            presets,
            display: {
                compactMode: true,
                showPreview: true
            }
        };
    }
    
    static applyUserLevel(config, userLevel) {
        const levelConfigs = {
            basic: basicUserConfig,
            premium: premiumUserConfig,
            professional: professionalUserConfig
        };
        
        return { ...config, ...levelConfigs[userLevel] };
    }
}
```

### 使用示例

```javascript
// 为视频项目创建角色配置
const videoProjectConfig = CharacterModalConfigFactory.createProjectConfig(
    'video', 
    'premium'
);

// 为分镜绑定创建配置
const bindingConfig = CharacterModalConfigFactory.createBindingConfig({
    projectType: 'animation',
    positionId: 'pos_123',
    requirements: ['female', 'young'],
    compatibleStyles: ['动漫可爱3.0']
});

// 快速创建配置
const quickConfig = CharacterModalConfigFactory.createQuickConfig({
    style: '写实风3.0',
    gender: 'male',
    age: 'adult'
});
```

## 📊 配置验证

### 参数验证器

```javascript
class ConfigValidator {
    static validate(config) {
        const errors = [];
        
        // 验证必需参数
        if (!config.mode) {
            errors.push('mode参数是必需的');
        }
        
        // 验证模式特定参数
        if (config.mode === 'filtered' && !config.filters) {
            errors.push('filtered模式需要filters参数');
        }
        
        // 验证权限
        if (!this.validatePermissions(config)) {
            errors.push('用户权限不足');
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    static validatePermissions(config) {
        const userLevel = getCurrentUserLevel();
        const requiredFeatures = Object.keys(config.features || {});
        
        return requiredFeatures.every(feature => 
            this.hasPermission(userLevel, feature)
        );
    }
}
```

---

*本文档提供了角色创建罩层在各种业务场景下的完整配置方案，确保组件能够灵活适应不同的使用需求。*
