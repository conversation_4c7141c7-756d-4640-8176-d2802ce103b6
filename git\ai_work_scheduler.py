#!/usr/bin/env python3
"""
多AI程序员工作分配和调度系统
AI Work Allocation and Scheduling System
"""

import json
import time
import hashlib
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

class TaskStatus(Enum):
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    BLOCKED = "blocked"

class AIStatus(Enum):
    IDLE = "idle"
    BUSY = "busy"
    OFFLINE = "offline"
    ERROR = "error"

@dataclass
class Task:
    """任务数据类"""
    id: str
    title: str
    description: str
    file_paths: List[str]
    dependencies: List[str]
    priority: int  # 1-10, 10最高
    estimated_time: int  # 预估时间（分钟）
    status: TaskStatus
    assigned_ai: Optional[str]
    created_at: datetime
    assigned_at: Optional[datetime]
    completed_at: Optional[datetime]
    tags: List[str]

@dataclass
class AIAgent:
    """AI代理数据类"""
    id: str
    name: str
    capabilities: List[str]  # 能力标签
    current_task: Optional[str]
    status: AIStatus
    last_heartbeat: datetime
    completed_tasks: int
    success_rate: float
    avg_completion_time: float  # 平均完成时间（分钟）
    specialties: List[str]  # 专长领域

class AIWorkScheduler:
    """AI工作调度器"""
    
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.tasks_file = self.workspace_path / "ai_tasks.json"
        self.agents_file = self.workspace_path / "ai_agents.json"
        self.schedule_file = self.workspace_path / "ai_schedule.json"
        self.lock_file = self.workspace_path / "scheduler.lock"
        
        self.tasks: Dict[str, Task] = {}
        self.agents: Dict[str, AIAgent] = {}
        self.file_locks: Dict[str, str] = {}  # 文件路径 -> AI ID
        
        self.load_data()
        self._lock = threading.Lock()
    
    def load_data(self):
        """加载数据"""
        # 加载任务
        if self.tasks_file.exists():
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)
                for task_data in tasks_data:
                    task_data['created_at'] = datetime.fromisoformat(task_data['created_at'])
                    if task_data['assigned_at']:
                        task_data['assigned_at'] = datetime.fromisoformat(task_data['assigned_at'])
                    if task_data['completed_at']:
                        task_data['completed_at'] = datetime.fromisoformat(task_data['completed_at'])
                    task_data['status'] = TaskStatus(task_data['status'])
                    
                    task = Task(**task_data)
                    self.tasks[task.id] = task
        
        # 加载AI代理
        if self.agents_file.exists():
            with open(self.agents_file, 'r', encoding='utf-8') as f:
                agents_data = json.load(f)
                for agent_data in agents_data:
                    agent_data['last_heartbeat'] = datetime.fromisoformat(agent_data['last_heartbeat'])
                    agent_data['status'] = AIStatus(agent_data['status'])
                    
                    agent = AIAgent(**agent_data)
                    self.agents[agent.id] = agent
    
    def save_data(self):
        """保存数据"""
        # 保存任务
        tasks_data = []
        for task in self.tasks.values():
            task_dict = asdict(task)
            task_dict['created_at'] = task.created_at.isoformat()
            task_dict['assigned_at'] = task.assigned_at.isoformat() if task.assigned_at else None
            task_dict['completed_at'] = task.completed_at.isoformat() if task.completed_at else None
            task_dict['status'] = task.status.value
            tasks_data.append(task_dict)
        
        with open(self.tasks_file, 'w', encoding='utf-8') as f:
            json.dump(tasks_data, f, indent=2, ensure_ascii=False)
        
        # 保存AI代理
        agents_data = []
        for agent in self.agents.values():
            agent_dict = asdict(agent)
            agent_dict['last_heartbeat'] = agent.last_heartbeat.isoformat()
            agent_dict['status'] = agent.status.value
            agents_data.append(agent_dict)
        
        with open(self.agents_file, 'w', encoding='utf-8') as f:
            json.dump(agents_data, f, indent=2, ensure_ascii=False)
    
    def register_ai(self, ai_id: str, name: str, capabilities: List[str], specialties: List[str] = None):
        """注册AI代理"""
        with self._lock:
            if ai_id not in self.agents:
                self.agents[ai_id] = AIAgent(
                    id=ai_id,
                    name=name,
                    capabilities=capabilities,
                    current_task=None,
                    status=AIStatus.IDLE,
                    last_heartbeat=datetime.now(),
                    completed_tasks=0,
                    success_rate=1.0,
                    avg_completion_time=30.0,
                    specialties=specialties or []
                )
            else:
                # 更新现有AI的信息
                agent = self.agents[ai_id]
                agent.name = name
                agent.capabilities = capabilities
                agent.specialties = specialties or agent.specialties
                agent.last_heartbeat = datetime.now()
                agent.status = AIStatus.IDLE
            
            self.save_data()
    
    def create_task(self, title: str, description: str, file_paths: List[str], 
                   dependencies: List[str] = None, priority: int = 5, 
                   estimated_time: int = 30, tags: List[str] = None) -> str:
        """创建新任务"""
        task_id = hashlib.md5(f"{title}_{datetime.now().isoformat()}".encode()).hexdigest()[:12]
        
        with self._lock:
            task = Task(
                id=task_id,
                title=title,
                description=description,
                file_paths=file_paths,
                dependencies=dependencies or [],
                priority=priority,
                estimated_time=estimated_time,
                status=TaskStatus.PENDING,
                assigned_ai=None,
                created_at=datetime.now(),
                assigned_at=None,
                completed_at=None,
                tags=tags or []
            )
            
            self.tasks[task_id] = task
            self.save_data()
        
        return task_id
    
    def get_available_tasks(self, ai_id: str) -> List[Task]:
        """获取可分配给指定AI的任务"""
        agent = self.agents.get(ai_id)
        if not agent:
            return []
        
        available_tasks = []
        
        for task in self.tasks.values():
            if task.status != TaskStatus.PENDING:
                continue
            
            # 检查依赖是否完成
            if not self._are_dependencies_completed(task):
                continue
            
            # 检查文件锁定
            if self._are_files_locked(task.file_paths, ai_id):
                continue
            
            # 检查AI能力匹配
            if self._is_ai_capable(agent, task):
                available_tasks.append(task)
        
        # 按优先级和AI专长排序
        available_tasks.sort(key=lambda t: (
            -t.priority,  # 优先级高的在前
            -self._calculate_ai_task_match_score(agent, t)  # 匹配度高的在前
        ))
        
        return available_tasks
    
    def assign_task(self, task_id: str, ai_id: str) -> bool:
        """分配任务给AI"""
        with self._lock:
            task = self.tasks.get(task_id)
            agent = self.agents.get(ai_id)
            
            if not task or not agent:
                return False
            
            if task.status != TaskStatus.PENDING:
                return False
            
            if agent.status != AIStatus.IDLE:
                return False
            
            # 检查文件锁定
            if self._are_files_locked(task.file_paths, ai_id):
                return False
            
            # 分配任务
            task.status = TaskStatus.ASSIGNED
            task.assigned_ai = ai_id
            task.assigned_at = datetime.now()
            
            agent.current_task = task_id
            agent.status = AIStatus.BUSY
            
            # 锁定文件
            for file_path in task.file_paths:
                self.file_locks[file_path] = ai_id
            
            self.save_data()
            return True
    
    def start_task(self, task_id: str, ai_id: str) -> bool:
        """开始执行任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task or task.assigned_ai != ai_id:
                return False
            
            task.status = TaskStatus.IN_PROGRESS
            self.save_data()
            return True
    
    def complete_task(self, task_id: str, ai_id: str, success: bool = True) -> bool:
        """完成任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            agent = self.agents.get(ai_id)
            
            if not task or not agent or task.assigned_ai != ai_id:
                return False
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
            task.completed_at = datetime.now()
            
            # 更新AI状态
            agent.current_task = None
            agent.status = AIStatus.IDLE
            agent.completed_tasks += 1
            
            # 计算完成时间和成功率
            if task.assigned_at:
                completion_time = (task.completed_at - task.assigned_at).total_seconds() / 60
                agent.avg_completion_time = (agent.avg_completion_time * (agent.completed_tasks - 1) + completion_time) / agent.completed_tasks
            
            if success:
                agent.success_rate = (agent.success_rate * (agent.completed_tasks - 1) + 1.0) / agent.completed_tasks
            else:
                agent.success_rate = (agent.success_rate * (agent.completed_tasks - 1) + 0.0) / agent.completed_tasks
            
            # 释放文件锁
            for file_path in task.file_paths:
                if self.file_locks.get(file_path) == ai_id:
                    del self.file_locks[file_path]
            
            self.save_data()
            return True
    
    def heartbeat(self, ai_id: str):
        """AI心跳更新"""
        with self._lock:
            agent = self.agents.get(ai_id)
            if agent:
                agent.last_heartbeat = datetime.now()
                if agent.status == AIStatus.OFFLINE:
                    agent.status = AIStatus.IDLE
                self.save_data()
    
    def auto_schedule(self) -> Dict[str, List[str]]:
        """自动调度任务"""
        assignments = {}
        
        with self._lock:
            # 获取空闲的AI
            idle_agents = [agent for agent in self.agents.values() if agent.status == AIStatus.IDLE]
            
            for agent in idle_agents:
                available_tasks = self.get_available_tasks(agent.id)
                if available_tasks:
                    best_task = available_tasks[0]  # 已经按优先级排序
                    if self.assign_task(best_task.id, agent.id):
                        assignments[agent.id] = [best_task.id]
        
        return assignments
    
    def _are_dependencies_completed(self, task: Task) -> bool:
        """检查任务依赖是否完成"""
        for dep_id in task.dependencies:
            dep_task = self.tasks.get(dep_id)
            if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                return False
        return True
    
    def _are_files_locked(self, file_paths: List[str], ai_id: str) -> bool:
        """检查文件是否被其他AI锁定"""
        for file_path in file_paths:
            locked_by = self.file_locks.get(file_path)
            if locked_by and locked_by != ai_id:
                return True
        return False
    
    def _is_ai_capable(self, agent: AIAgent, task: Task) -> bool:
        """检查AI是否有能力完成任务"""
        # 基于文件类型和任务标签判断
        required_capabilities = set()
        
        for file_path in task.file_paths:
            if file_path.endswith('.py'):
                required_capabilities.add('python')
            elif file_path.endswith(('.js', '.ts')):
                required_capabilities.add('javascript')
            elif file_path.endswith('.php'):
                required_capabilities.add('php')
            elif file_path.endswith(('.html', '.css')):
                required_capabilities.add('frontend')
        
        for tag in task.tags:
            required_capabilities.add(tag.lower())
        
        agent_capabilities = set(cap.lower() for cap in agent.capabilities)
        return required_capabilities.issubset(agent_capabilities)
    
    def _calculate_ai_task_match_score(self, agent: AIAgent, task: Task) -> float:
        """计算AI与任务的匹配分数"""
        score = 0.0
        
        # 专长匹配
        for specialty in agent.specialties:
            if specialty.lower() in [tag.lower() for tag in task.tags]:
                score += 2.0
        
        # 成功率权重
        score += agent.success_rate * 1.0
        
        # 完成时间权重（时间越短分数越高）
        if agent.avg_completion_time > 0:
            score += max(0, (60 - agent.avg_completion_time) / 60) * 1.0
        
        return score
    
    def get_status_report(self) -> Dict:
        """获取状态报告"""
        with self._lock:
            total_tasks = len(self.tasks)
            completed_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED])
            pending_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING])
            in_progress_tasks = len([t for t in self.tasks.values() if t.status == TaskStatus.IN_PROGRESS])
            
            active_agents = len([a for a in self.agents.values() if a.status != AIStatus.OFFLINE])
            busy_agents = len([a for a in self.agents.values() if a.status == AIStatus.BUSY])
            
            return {
                "timestamp": datetime.now().isoformat(),
                "tasks": {
                    "total": total_tasks,
                    "completed": completed_tasks,
                    "pending": pending_tasks,
                    "in_progress": in_progress_tasks,
                    "completion_rate": completed_tasks / total_tasks if total_tasks > 0 else 0
                },
                "agents": {
                    "total": len(self.agents),
                    "active": active_agents,
                    "busy": busy_agents,
                    "utilization": busy_agents / active_agents if active_agents > 0 else 0
                },
                "file_locks": len(self.file_locks)
            }

def main():
    """主函数 - 调度器守护进程"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI工作调度器")
    parser.add_argument("--workspace", default=".", help="工作空间路径")
    parser.add_argument("--interval", type=int, default=30, help="调度间隔（秒）")
    
    args = parser.parse_args()
    
    scheduler = AIWorkScheduler(args.workspace)
    
    print("启动AI工作调度器...")
    
    try:
        while True:
            # 自动调度
            assignments = scheduler.auto_schedule()
            if assignments:
                print(f"[{datetime.now()}] 新分配: {assignments}")
            
            # 状态报告
            status = scheduler.get_status_report()
            print(f"[{datetime.now()}] 状态: {status['tasks']['pending']}待处理, "
                  f"{status['tasks']['in_progress']}进行中, "
                  f"{status['agents']['busy']}/{status['agents']['active']}AI忙碌")
            
            time.sleep(args.interval)
            
    except KeyboardInterrupt:
        print("停止调度器")

if __name__ == "__main__":
    main()
