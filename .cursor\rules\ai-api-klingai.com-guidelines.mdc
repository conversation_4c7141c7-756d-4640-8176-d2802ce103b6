---
alwaysApply: false
---
## 可灵AI (KlingAI) API 接口开发规范指南 (完整版)

### API概述

**官方文档**: https://app.klingai.com/cn/dev/document-api/
**API基础URL**: `https://api.klingai.com`
**认证URL**: `https://api.klingai.com/v1/auth`
**主要功能**: AI图像生成、视频生成、图像编辑、虚拟试穿、运镜控制
**适用场景**: 高质量分镜图像生成、专业视频制作、电影级内容创作

### 认证配置

#### JWT标准鉴权 (官方标准)
**官方文档**: https://app.klingai.com/cn/dev/document-api/apiReference/commonInfo

```php
<?php

class KlingAIConfig
{
    const BASE_URL = 'https://api.klingai.com';
    const TIMEOUT = 300;
    const MAX_RETRIES = 3;

    private $accessKey;
    private $secretKey;

    public function __construct($accessKey, $secretKey)
    {
        $this->accessKey = $accessKey;
        $this->secretKey = $secretKey;
    }

    /**
     * 生成JWT Token (遵循官方JWT标准)
     * 官方要求: Header + Payload + Signature
     */
    public function generateJWTToken()
    {
        // JWT Header
        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        // JWT Payload
        $payload = [
            'iss' => $this->accessKey,           // 签发者 (Access Key)
            'exp' => time() + 1800,              // 过期时间 (当前时间+30分钟)
            'nbf' => time() - 5                  // 开始生效时间 (当前时间-5秒)
        ];

        // 编码Header和Payload
        $headerEncoded = $this->base64UrlEncode(json_encode($header));
        $payloadEncoded = $this->base64UrlEncode(json_encode($payload));

        // 生成签名
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->secretKey, true);
        $signatureEncoded = $this->base64UrlEncode($signature);

        // 组装JWT Token
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    /**
     * 获取请求头 (官方标准格式)
     */
    public function getHeaders()
    {
        $jwtToken = $this->generateJWTToken();

        return [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $jwtToken,  // 注意Bearer和Token之间有空格
            'User-Agent: KlingAI-PHP-Client/2.1'
        ];
    }

    /**
     * Base64 URL安全编码
     */
    private function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
}
```

### 完整API接口列表 (基于官方文档)

#### 1. 图像生成接口

**接口地址**: `POST /v1/images/generations`
**官方文档**: https://app.klingai.com/cn/dev/document-api/apiReference/model/imageGeneration

```php
<?php

class KlingImageAPI
{
    private $config;

    public function __construct(KlingAIConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 创建图像生成任务
     * 支持模型: kling-v1, kling-v1-5, kling-v2
     */
    public function createImageTask($prompt, $options = [])
    {
        $defaultOptions = [
            'model_name' => 'kling-v1',           // 模型名称
            'prompt' => $prompt,                   // 正向文本提示词 (必填，不超过2500字符)
            'negative_prompt' => '',               // 负向文本提示词 (可选，不超过2500字符)
            'image' => null,                       // 参考图像 (Base64编码或URL)
            'image_reference' => null,             // 图像参考类型: subject/face (仅kling-v1-5支持)
            'image_fidelity' => 0.5,              // 图像参考强度 [0,1]
            'human_fidelity' => 0.45,             // 面部参考强度 [0,1] (仅subject模式)
            'n' => 1,                             // 生成图像数量 [1,9]
            'aspect_ratio' => '16:9',             // 画面纵横比
            'callback_url' => null                // 回调通知地址
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/images/generations', $payload);
    }

    /**
     * 查询单个图像生成任务
     */
    public function getImageTask($taskId)
    {
        return $this->makeRequest("/v1/images/generations/{$taskId}", [], 'GET');
    }

    /**
     * 查询图像生成任务列表
     */
    public function listImageTasks($pageNum = 1, $pageSize = 30)
    {
        $params = [
            'pageNum' => $pageNum,    // 页码 [1,1000]
            'pageSize' => $pageSize   // 每页数据量 [1,500]
        ];

        $url = '/v1/images/generations?' . http_build_query($params);
        return $this->makeRequest($url, [], 'GET');
    }
    
    /**
     * 图像变体生成
     */
    public function imageVariation($imageUrl, $prompt = '', $options = [])
    {
        $defaultOptions = [
            'model' => 'kling-v1',
            'image' => $imageUrl,
            'prompt' => $prompt,
            'strength' => 0.7,
            'guidance_scale' => 7.5,
            'num_inference_steps' => 30,
            'num_images' => 1
        ];
        
        $payload = array_merge($defaultOptions, $options);
        
        return $this->makeRequest('/v1/images/img2img', $payload);
    }
    
    /**
     * 图像修复/编辑
     */
    public function imageInpainting($imageUrl, $maskUrl, $prompt, $options = [])
    {
        $defaultOptions = [
            'model' => 'kling-inpaint-v1',
            'image' => $imageUrl,
            'mask' => $maskUrl,
            'prompt' => $prompt,
            'guidance_scale' => 7.5,
            'num_inference_steps' => 30
        ];
        
        $payload = array_merge($defaultOptions, $options);
        
        return $this->makeRequest('/v1/images/inpaint', $payload);
    }
    
    /**
     * 图像超分辨率
     */
    public function upscaleImage($imageUrl, $scale = 2)
    {
        $payload = [
            'image' => $imageUrl,
            'scale' => $scale,
            'model' => 'kling-upscale-v1'
        ];
        
        return $this->makeRequest('/v1/images/upscale', $payload);
    }
    
    private function makeRequest($endpoint, $data = [], $method = 'POST')
    {
        $url = KlingAIConfig::BASE_URL . $endpoint;

        $ch = curl_init();
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => KlingAIConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ];

        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }

        curl_setopt_array($ch, $options);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // 使用官方错误码进行错误处理
        if ($httpCode !== 200) {
            $responseData = json_decode($response, true);
            $businessCode = $responseData['code'] ?? 0;
            $errorInfo = KlingErrorCodes::getErrorInfo($httpCode, $businessCode);

            throw new Exception(
                "KlingAI API请求失败: HTTP {$httpCode}, 业务码 {$businessCode} - " .
                $errorInfo['name'] . ": " . $errorInfo['description'] .
                " (建议: " . $errorInfo['solution'] . ")"
            );
        }

        return json_decode($response, true);
    }
}
```

#### 2. 视频生成API

##### 2.1 文生视频接口

**接口地址**: `POST /v1/videos/text2video`
**官方文档**: https://app.klingai.com/cn/dev/document-api/apiReference/model/textToVideo

```php
<?php

class KlingVideoAPI
{
    private $config;

    public function __construct(KlingAIConfig $config)
    {
        $this->config = $config;
    }
    
    /**
     * 创建文生视频任务
     * 支持模型: kling-v1, kling-v1-6, kling-v2-master, kling-v2-1-master
     * 官方文档: https://app.klingai.com/cn/dev/document-api/apiReference/model/textToVideo
     */
    public function createTextToVideoTask($prompt, $options = [])
    {
        $defaultOptions = [
            'model_name' => 'kling-v1',           // 模型名称 (必填)
            'prompt' => $prompt,                   // 正向文本提示词 (必填，不超过2500字)
            'negative_prompt' => '',               // 负向文本提示词 (可选，不超过2500字符)
            'cfg_scale' => 0.5,                   // 生成自由度 [0,1]
            'mode' => 'std',                      // 生成模式: std(标准)/pro(专家)
            'camera_control' => null,             // 摄像机运动控制 (详细配置见下方)
            'aspect_ratio' => '16:9',             // 画面纵横比: 16:9, 9:16, 1:1
            'duration' => '5',                    // 视频时长: 5, 10 (秒)
            'callback_url' => null,               // 回调通知地址
            'external_task_id' => null            // 自定义任务ID (用户自定义，支持查询)
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/videos/text2video', $payload);
    }

    /**
     * 创建摄像机控制配置
     * 官方支持的运镜类型和参数
     */
    public function createCameraControl($type = 'simple', $config = [])
    {
        $cameraControl = ['type' => $type];

        switch ($type) {
            case 'simple':
                // 简单运镜，需要在config中6选1进行运镜
                $defaultConfig = [
                    'horizontal' => 0,    // 水平运镜 [-10, 10] 负值左移，正值右移
                    'vertical' => 0,      // 垂直运镜 [-10, 10] 负值下移，正值上移
                    'pan' => 0,           // 水平摇镜 [-10, 10] 负值左旋，正值右旋
                    'tilt' => 0,          // 垂直摇镜 [-10, 10] 负值下旋，正值上旋
                    'roll' => 0,          // 旋转运镜 [-10, 10] 负值逆时针，正值顺时针
                    'zoom' => 0           // 变焦 [-10, 10] 负值拉远，正值推近
                ];
                // 注意：6个参数中只能有一个不为0，其余必须为0
                $cameraControl['config'] = array_merge($defaultConfig, $config);
                break;

            case 'down_back':
                // 镜头下压并后退 (下移拉远) - config参数无需填写
                break;

            case 'forward_up':
                // 镜头前进并上仰 (推进上移) - config参数无需填写
                break;

            case 'right_turn_forward':
                // 先右旋转后前进 (右旋推进) - config参数无需填写
                break;

            case 'left_turn_forward':
                // 先左旋并前进 (左旋推进) - config参数无需填写
                break;

            default:
                throw new Exception("不支持的运镜类型: {$type}");
        }

        return $cameraControl;
    }

    /**
     * 查询单个文生视频任务
     */
    public function getTextToVideoTask($taskId, $externalTaskId = null)
    {
        $url = "/v1/videos/text2video/{$taskId}";
        if ($externalTaskId) {
            $url .= "?external_task_id={$externalTaskId}";
        }
        return $this->makeRequest($url, [], 'GET');
    }

    /**
     * 查询文生视频任务列表
     */
    public function listTextToVideoTasks($pageNum = 1, $pageSize = 30)
    {
        $params = [
            'pageNum' => $pageNum,    // 页码 [1,1000]
            'pageSize' => $pageSize   // 每页数据量 [1,500]
        ];

        $url = '/v1/videos/text2video?' . http_build_query($params);
        return $this->makeRequest($url, [], 'GET');
    }
    
    /**
     * 图像生成视频
     */
    public function imageToVideo($imageUrl, $options = [])
    {
        $defaultOptions = [
            'model' => 'kling-video-v1',
            'image' => $imageUrl,
            'duration' => 5,
            'fps' => 24,
            'motion_strength' => 0.8,
            'camera_movement' => 'static',
            'loop' => false
        ];
        
        $payload = array_merge($defaultOptions, $options);
        
        return $this->makeRequest('/v1/videos/img2video', $payload);
    }
    
    /**
     * 视频扩展
     */
    public function extendVideo($videoUrl, $direction = 'forward', $duration = 3)
    {
        $payload = [
            'video' => $videoUrl,
            'direction' => $direction, // forward, backward
            'duration' => $duration,
            'model' => 'kling-video-extend-v1'
        ];
        
        return $this->makeRequest('/v1/videos/extend', $payload);
    }
    
    /**
     * 查询视频生成状态
     */
    public function getVideoStatus($taskId)
    {
        return $this->makeRequest('/v1/videos/status/' . $taskId, [], 'GET');
    }
    
    private function makeRequest($endpoint, $data = [], $method = 'POST')
    {
        $url = KlingAIConfig::BASE_URL . $endpoint;
        
        $ch = curl_init();
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => KlingAIConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ];
        
        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }
        
        curl_setopt_array($ch, $options);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("KlingAI API请求失败: HTTP {$httpCode}");
        }
        
        return json_decode($response, true);
    }
}
```

### 官方错误码对照表

#### HTTP状态码和业务码对应关系
**官方文档**: https://app.klingai.com/cn/dev/document-api/apiReference/commonInfo

```php
class KlingErrorCodes
{
    const ERROR_CODES = [
        // 成功
        200 => [
            0 => ['name' => '请求成功', 'description' => '-', 'solution' => '-']
        ],

        // 身份验证失败 (401)
        401 => [
            1000 => ['name' => '身份验证失败', 'description' => '身份验证失败', 'solution' => '检查Authorization是否正确'],
            1001 => ['name' => '身份验证失败', 'description' => 'Authorization为空', 'solution' => '在RequestHeader中填写正确的Authorization'],
            1002 => ['name' => '身份验证失败', 'description' => 'Authorization值非法', 'solution' => '在RequestHeader中填写正确的Authorization'],
            1003 => ['name' => '身份验证失败', 'description' => 'Authorization未到有效时间', 'solution' => '检查token的开始生效时间，等待生效或重新签发'],
            1004 => ['name' => '身份验证失败', 'description' => 'Authorization已失效', 'solution' => '检查token的有效期，重新签发']
        ],

        // 请求参数非法 (400)
        400 => [
            1200 => ['name' => '请求参数非法', 'description' => '请求参数非法', 'solution' => '检查请求参数是否正确'],
            1201 => ['name' => '请求参数非法', 'description' => '参数非法，如key写错或value非法', 'solution' => '参考返回体中message字段的具体信息，修改请求参数'],
            1300 => ['name' => '触发策略', 'description' => '触发平台策略', 'solution' => '检查是否触发平台策略'],
            1301 => ['name' => '触发策略', 'description' => '触发平台的内容安全策略', 'solution' => '检查输入内容，修改后重新发起请求']
        ],

        // 权限问题 (403)
        403 => [
            1103 => ['name' => '账户异常', 'description' => '请求的资源无权限，如接口/模型', 'solution' => '检查账户权限']
        ],

        // 资源不存在 (404)
        404 => [
            1202 => ['name' => '请求参数非法', 'description' => '请求的method无效', 'solution' => '查看接口文档，使用正确的requestmethod'],
            1203 => ['name' => '请求参数非法', 'description' => '请求的资源不存在，如模型', 'solution' => '参考返回体中message字段的具体信息，修改请求参数']
        ],

        // 频率限制 (429)
        429 => [
            1100 => ['name' => '账户异常', 'description' => '账户异常', 'solution' => '检查账户配置信息'],
            1101 => ['name' => '账户异常', 'description' => '账户欠费 (后付费场景)', 'solution' => '进行账户充值，确保余额充足'],
            1102 => ['name' => '账户异常', 'description' => '资源包已用完/已过期（预付费场景）', 'solution' => '购买额外的资源包，或开通后付费服务（如有）'],
            1302 => ['name' => '触发策略', 'description' => 'API请求过快，超过平台速率限制', 'solution' => '降低请求频率、稍后重试，或联系客服增加限额'],
            1303 => ['name' => '触发策略', 'description' => '并发或QPS超出预付费资源包限制', 'solution' => '降低请求频率、稍后重试，或联系客服增加限额'],
            1304 => ['name' => '触发策略', 'description' => '触发平台的IP白名单策略', 'solution' => '联系客服']
        ],

        // 服务器错误 (500)
        500 => [
            5000 => ['name' => '内部错误', 'description' => '服务器内部错误', 'solution' => '稍后重试，或联系客服']
        ],

        // 服务不可用 (503)
        503 => [
            5001 => ['name' => '内部错误', 'description' => '服务器暂时不可用，通常是在维护', 'solution' => '稍后重试，或联系客服']
        ],

        // 网关超时 (504)
        504 => [
            5002 => ['name' => '内部错误', 'description' => '服务器内部超时，通常是发生积压', 'solution' => '稍后重试，或联系客服']
        ]
    ];

    /**
     * 获取错误信息
     */
    public static function getErrorInfo($httpCode, $businessCode)
    {
        return self::ERROR_CODES[$httpCode][$businessCode] ?? [
            'name' => '未知错误',
            'description' => "HTTP {$httpCode}, 业务码 {$businessCode}",
            'solution' => '联系客服或查看官方文档'
        ];
    }
}
```

### 模型和参数配置

#### 1. 官方支持的模型 (基于最新官方文档)
```php
class KlingModels
{
    // 图像生成模型 (官方最新版本)
    const IMAGE_MODELS = [
        'kling-v1' => '通用图像生成模型 (默认)',
        'kling-v1-5' => '增强图像生成模型 (支持图像参考)',
        'kling-v2' => '最新图像生成模型'
    ];

    // 视频生成模型 (官方最新版本)
    const VIDEO_MODELS = [
        'kling-v1' => '标准视频生成模型 (默认)',
        'kling-v1-6' => '增强视频生成模型',
        'kling-v2-master' => '专业视频生成模型',
        'kling-v2-1-master' => '最新视频生成模型'
    ];

    // 图像生成支持的纵横比 (官方完整列表)
    const IMAGE_ASPECT_RATIOS = [
        '16:9' => '宽屏 (默认)',
        '9:16' => '竖屏',
        '1:1' => '正方形',
        '4:3' => '标准',
        '3:4' => '竖版标准',
        '3:2' => '经典',
        '2:3' => '竖版经典',
        '21:9' => '超宽屏'
    ];

    // 视频生成支持的纵横比
    const VIDEO_ASPECT_RATIOS = [
        '16:9' => '宽屏 (默认)',
        '9:16' => '竖屏',
        '1:1' => '正方形'
    ];

    // 图像参考类型 (仅kling-v1-5支持)
    const IMAGE_REFERENCE_TYPES = [
        'subject' => '角色特征参考',
        'face' => '人物长相参考 (需仅含1张人脸)'
    ];

    // 视频生成模式
    const VIDEO_MODES = [
        'std' => '标准模式 - 基础模式，性价比高',
        'pro' => '专家模式 - 高表现模式，生成视频质量更佳'
    ];

    // 摄像机运镜类型 (官方支持)
    const CAMERA_CONTROL_TYPES = [
        'simple' => '简单运镜 - 可在config中六选一进行运镜',
        'down_back' => '下移拉远 - 镜头下压并后退',
        'forward_up' => '推进上移 - 镜头前进并上仰',
        'right_turn_forward' => '右旋推进 - 先右旋转后前进',
        'left_turn_forward' => '左旋推进 - 先左旋并前进'
    ];
}
```

#### 2. 高级提示词模板
```php
class KlingPrompts
{
    /**
     * 电影级分镜提示词
     */
    public static function cinematicShot($scene, $shot, $lighting, $mood)
    {
        return "cinematic {$shot} shot of {$scene}, {$lighting} lighting, {$mood} mood, professional cinematography, film grain, depth of field, high detail, 8k resolution";
    }
    
    /**
     * 角色动作提示词
     */
    public static function characterAction($character, $action, $emotion, $environment)
    {
        return "{$character} {$action} with {$emotion} expression, in {$environment}, dynamic pose, detailed facial features, realistic lighting, high quality";
    }
    
    /**
     * 场景转换提示词
     */
    public static function sceneTransition($fromScene, $toScene, $transitionType)
    {
        return "smooth {$transitionType} transition from {$fromScene} to {$toScene}, cinematic flow, professional editing, seamless blend";
    }
    
    /**
     * 视频运镜提示词
     */
    public static function cameraMovement($subject, $movement, $speed = 'medium')
    {
        return "{$subject}, {$movement} camera movement, {$speed} speed, smooth motion, professional cinematography, stable footage";
    }
    
    /**
     * 负面提示词
     */
    public static function negativePrompt()
    {
        return "low quality, blurry, distorted, deformed, ugly, bad anatomy, bad proportions, extra limbs, malformed, gross proportions, missing arms, missing legs, extra arms, extra legs, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, bad hands, fused fingers, too many fingers, disconnected limbs, malformed hands, long neck, long body, ugly, disgusting, poorly drawn, childish, mutilated, mangled, old, surreal, watermark, text, signature, username, artist name, trademark, title, multiple views, Reference sheet, curvy, plump, fat, strabismus, clothing cutout, side slit, worst quality, low quality, normal quality, lowres, bad anatomy, bad hands, normal quality, monochrome, grayscale, collapsed eyeshadow, multiple eyebrows, vaginas in breasts, holes on breasts, fleckles, stretched nipples, gigantic penis, nipples on buttocks, analog, analogphoto, signatre, logo, two faces";
    }
}
```

### 任务管理和工作流

#### 1. 高级任务管理器
```php
class KlingTaskManager
{
    private $imageAPI;
    private $videoAPI;
    private $maxConcurrent = 5;
    
    public function __construct($config)
    {
        $this->imageAPI = new KlingImageAPI($config);
        $this->videoAPI = new KlingVideoAPI($config);
    }
    
    /**
     * 批量图像生成
     */
    public function batchImageGeneration($prompts, $options = [])
    {
        $tasks = [];
        $chunks = array_chunk($prompts, $this->maxConcurrent);
        
        foreach ($chunks as $chunk) {
            $batchTasks = [];
            
            foreach ($chunk as $index => $prompt) {
                $response = $this->imageAPI->textToImage($prompt, $options);
                $batchTasks[] = [
                    'task_id' => $response['task_id'],
                    'prompt_index' => $index,
                    'prompt' => $prompt,
                    'type' => 'image'
                ];
            }
            
            // 等待当前批次完成
            $batchResults = $this->waitForBatch($batchTasks);
            $tasks = array_merge($tasks, $batchResults);
        }
        
        return $tasks;
    }
    
    /**
     * 分镜序列生成
     */
    public function generateStoryboardSequence($scenes, $style = 'cinematic')
    {
        $imageResults = [];
        
        foreach ($scenes as $index => $scene) {
            $prompt = KlingPrompts::cinematicShot(
                $scene['description'],
                $scene['shot_type'],
                $scene['lighting'],
                $scene['mood']
            );
            
            $options = [
                'style' => $style,
                'aspect_ratio' => $scene['aspect_ratio'] ?? '16:9',
                'quality' => 'high',
                'negative_prompt' => KlingPrompts::negativePrompt()
            ];
            
            $response = $this->imageAPI->textToImage($prompt, $options);
            $task = $this->waitForCompletion($response['task_id'], 'image');
            
            $imageResults[] = [
                'scene_index' => $index,
                'scene_data' => $scene,
                'image_url' => $task['result']['image_url'],
                'task_id' => $response['task_id']
            ];
        }
        
        return $imageResults;
    }
    
    /**
     * 图像序列转视频
     */
    public function imagesToVideoSequence($imageResults, $videoOptions = [])
    {
        $videoTasks = [];
        
        foreach ($imageResults as $imageResult) {
            $options = array_merge([
                'duration' => 3,
                'fps' => 24,
                'motion_strength' => 0.6,
                'camera_movement' => $imageResult['scene_data']['camera_movement'] ?? 'static'
            ], $videoOptions);
            
            $response = $this->videoAPI->imageToVideo($imageResult['image_url'], $options);
            
            $videoTasks[] = [
                'task_id' => $response['task_id'],
                'scene_index' => $imageResult['scene_index'],
                'source_image' => $imageResult['image_url'],
                'scene_data' => $imageResult['scene_data']
            ];
        }
        
        return $this->waitForVideoBatch($videoTasks);
    }
    
    private function waitForBatch($tasks)
    {
        $results = [];
        
        foreach ($tasks as $task) {
            try {
                $result = $this->waitForCompletion($task['task_id'], $task['type']);
                $result['prompt_index'] = $task['prompt_index'];
                $result['prompt'] = $task['prompt'];
                $results[] = $result;
            } catch (Exception $e) {
                $results[] = [
                    'task_id' => $task['task_id'],
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'prompt_index' => $task['prompt_index']
                ];
            }
        }
        
        return $results;
    }
    
    private function waitForVideoBatch($tasks)
    {
        $results = [];
        
        foreach ($tasks as $task) {
            try {
                $result = $this->waitForCompletion($task['task_id'], 'video', 900); // 视频生成需要更长时间
                $result['scene_index'] = $task['scene_index'];
                $result['source_image'] = $task['source_image'];
                $result['scene_data'] = $task['scene_data'];
                $results[] = $result;
            } catch (Exception $e) {
                $results[] = [
                    'task_id' => $task['task_id'],
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'scene_index' => $task['scene_index']
                ];
            }
        }
        
        return $results;
    }
    
    private function waitForCompletion($taskId, $type = 'image', $maxWaitTime = 300)
    {
        $startTime = time();
        
        while (time() - $startTime < $maxWaitTime) {
            if ($type === 'video') {
                $status = $this->videoAPI->getVideoStatus($taskId);
            } else {
                $status = $this->imageAPI->getImageStatus($taskId);
            }
            
            if ($status['status'] === 'completed') {
                return $status;
            } elseif ($status['status'] === 'failed') {
                throw new Exception('任务执行失败: ' . ($status['error'] ?? '未知错误'));
            }
            
            sleep($type === 'video' ? 10 : 5); // 视频任务检查间隔更长
        }
        
        throw new Exception('任务执行超时');
    }
}
```

### 高级功能应用

#### 1. 智能分镜生成器
```php
class IntelligentStoryboardGenerator
{
    private $taskManager;
    
    public function __construct($config)
    {
        $this->taskManager = new KlingTaskManager($config);
    }
    
    /**
     * 根据剧本自动生成分镜
     */
    public function generateFromScript($script, $style = 'cinematic')
    {
        // 解析剧本，提取场景信息
        $scenes = $this->parseScript($script);
        
        // 为每个场景生成最佳的拍摄参数
        $optimizedScenes = $this->optimizeScenes($scenes);
        
        // 生成分镜图像
        $storyboard = $this->taskManager->generateStoryboardSequence($optimizedScenes, $style);
        
        // 生成视频片段
        $videos = $this->taskManager->imagesToVideoSequence($storyboard, [
            'duration' => 4,
            'motion_strength' => 0.7
        ]);
        
        return [
            'storyboard' => $storyboard,
            'videos' => $videos,
            'metadata' => [
                'total_scenes' => count($scenes),
                'style' => $style,
                'generated_at' => date('Y-m-d H:i:s')
            ]
        ];
    }
    
    private function parseScript($script)
    {
        // 简化的剧本解析逻辑
        $scenes = [];
        $lines = explode("\n", $script);
        
        foreach ($lines as $line) {
            if (strpos($line, 'SCENE:') === 0) {
                $description = trim(substr($line, 6));
                $scenes[] = [
                    'description' => $description,
                    'shot_type' => $this->determineShotType($description),
                    'lighting' => $this->determineLighting($description),
                    'mood' => $this->determineMood($description),
                    'aspect_ratio' => '16:9',
                    'camera_movement' => $this->determineCameraMovement($description)
                ];
            }
        }
        
        return $scenes;
    }
    
    private function optimizeScenes($scenes)
    {
        // 根据场景内容优化拍摄参数
        foreach ($scenes as &$scene) {
            // 根据描述内容智能选择最佳参数
            if (strpos($scene['description'], '对话') !== false) {
                $scene['shot_type'] = 'medium shot';
                $scene['camera_movement'] = 'static';
            } elseif (strpos($scene['description'], '动作') !== false) {
                $scene['shot_type'] = 'wide shot';
                $scene['camera_movement'] = 'pan_right';
            }
        }
        
        return $scenes;
    }
    
    private function determineShotType($description)
    {
        if (strpos($description, '特写') !== false) return 'close up';
        if (strpos($description, '全景') !== false) return 'wide shot';
        return 'medium shot';
    }
    
    private function determineLighting($description)
    {
        if (strpos($description, '夜晚') !== false) return 'low key';
        if (strpos($description, '阳光') !== false) return 'natural';
        return 'soft';
    }
    
    private function determineMood($description)
    {
        if (strpos($description, '紧张') !== false) return 'tense';
        if (strpos($description, '温馨') !== false) return 'warm';
        return 'neutral';
    }
    
    private function determineCameraMovement($description)
    {
        if (strpos($description, '追逐') !== false) return 'dolly_in';
        if (strpos($description, '环视') !== false) return 'pan_left';
        return 'static';
    }
}
```

### 使用示例

#### 完整的电影制作流程
```php
// 初始化 (使用官方JWT鉴权)
$config = new KlingAIConfig('your-access-key', 'your-secret-key');
$generator = new IntelligentStoryboardGenerator($config);

// 剧本内容
$script = "
SCENE: 主角站在未来城市的天台上，俯视着下方的霓虹灯海洋
SCENE: 特写主角坚定的眼神，准备迎接挑战
SCENE: 主角纵身跃下，在空中滑翔的动作场面
SCENE: 主角安全着陆，周围是繁忙的街道
";

// 生成完整的分镜和视频
$result = $generator->generateFromScript($script, 'cyberpunk');

// 输出结果
echo "生成了 " . count($result['storyboard']) . " 个分镜图像\n";
echo "生成了 " . count($result['videos']) . " 个视频片段\n";

foreach ($result['videos'] as $video) {
    echo "场景 {$video['scene_index']}: {$video['result']['video_url']}\n";
}
```

### 最佳实践和注意事项

#### 1. 鉴权和安全
- **使用官方JWT标准**: 严格按照官方文档使用AccessKey+SecretKey生成JWT Token
- **Token有效期管理**: 建议设置30分钟有效期，提前5分钟刷新
- **密钥安全**: 妥善保管AccessKey和SecretKey，不要在客户端暴露

#### 2. 参数优化
- **模型选择**: 根据需求选择合适的模型版本
  - 图像生成: kling-v1-5支持图像参考，kling-v2质量更高
  - 视频生成: kling-v2-master专业级质量
- **摄像机控制**: 使用官方camera_control参数实现专业运镜效果
- **质量模式**: 视频生成使用pro模式获得更佳质量

#### 3. 错误处理
- **完整错误码处理**: 根据HTTP状态码和业务码进行精确错误处理
- **重试策略**: 对于5xx错误实施指数退避重试
- **频率限制**: 遵守API调用频率限制，避免触发1302/1303错误

#### 4. 性能优化
- **批量处理**: 合理安排任务队列，提高生成效率
- **并发控制**: 注意资源包的并发限制
- **回调机制**: 使用callback_url避免频繁轮询

#### 5. 成本管理
- **监控使用量**: 定期检查资源包使用情况
- **模式选择**: 根据需求在std和pro模式间平衡质量和成本
- **参数优化**: 合理设置生成数量和时长

#### 6. 内容安全
- **提示词规范**: 避免触发内容安全策略(错误码1301)
- **图像格式**: 确保上传图像符合格式和尺寸要求
- **Base64编码**: 使用纯Base64编码，不要添加data:前缀

可灵AI提供了业界领先的图像和视频生成能力，严格遵循官方API规范能够确保最佳的使用体验和稳定性。
