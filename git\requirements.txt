# 多AI程序员协同开发系统依赖

# 核心依赖
GitPython>=3.1.40
requests>=2.31.0
python-dateutil>=2.8.2

# 代码质量工具
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
bandit>=1.7.5
pylint>=2.17.0

# 测试工具
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
coverage>=7.2.0

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 配置管理
pyyaml>=6.0
toml>=0.10.2

# 日志和监控
structlog>=23.1.0
colorlog>=6.7.0

# 网络和API
httpx>=0.24.0
websockets>=11.0.0

# 安全
cryptography>=41.0.0
bcrypt>=4.0.0

# 开发工具
pre-commit>=3.3.0
mypy>=1.4.0
types-requests>=2.31.0
types-PyYAML>=6.0.0

# 可选依赖（根据项目类型）
# PHP项目支持
# phpcs
# psalm

# JavaScript项目支持
# eslint
# prettier
# jest

# 数据库支持（如果需要）
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0
# pymongo>=4.4.0

# 消息队列（如果需要）
# celery>=5.3.0
# redis>=4.6.0

# 监控和告警（如果需要）
# prometheus-client>=0.17.0
# grafana-api>=1.0.3
