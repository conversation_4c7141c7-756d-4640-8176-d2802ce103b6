<?php
/**
 * 火山引擎豆包语音API配置示例
 * 
 * 🔧 LongDev1实施说明：
 * - 为"工具api接口服务"提供火山引擎配置示例
 * - 包含完整的配置参数和说明
 * - 遵循LongChec2制定的对接规范
 * 
 * <AUTHOR> (创造者👨‍💻)
 * @reviewer LongChec2 (审判者🕵️‍♂️)
 * @version 1.0
 * @date 2025-07-18
 */

return [
    // ==================== 🎵 火山引擎豆包语音API配置 ====================
    
    'volcengine' => [
        // 基础配置
        'base_url' => env('VOLCENGINE_BASE_URL', 'https://aiapi.tiptop.cn'),
        'api_key' => env('VOLCENGINE_API_KEY', 'volcengine_mock_token_12345'),
        'timeout' => env('VOLCENGINE_TIMEOUT', 30),
        'retry_times' => env('VOLCENGINE_RETRY_TIMES', 3),
        
        // 功能特性
        'features' => [
            'voice_synthesis' => true,      // 语音合成
            'voice_cloning' => true,        // 声音复刻
            'audio_effects' => true,        // 音效处理
            'audio_mixing' => true,         // 音频混合
            'smart_routing' => true,        // 智能路由
            'emotion_control' => true,      // 情感控制
            'multi_language' => true,       // 多语言支持
        ],
        
        // API体系配置
        'api_types' => [
            'bigmodel' => [
                'name' => '大模型API',
                'description' => '高质量语音合成，支持声音复刻和多情感',
                'max_text_length' => 5000,
                'quality' => 'premium',
                'sample_rate' => 24000,
                'cost_per_char' => 0.00028,
                'features' => [
                    'voice_cloning',
                    'emotion_control',
                    'multi_language',
                    'long_text'
                ]
            ],
            'traditional' => [
                'name' => '传统API',
                'description' => '成本更低，适合大批量生成',
                'max_text_length' => 300,
                'quality' => 'standard',
                'sample_rate' => 16000,
                'cost_per_char' => 0.0001,
                'features' => [
                    'emotion_control',
                    'multi_language',
                    'free_voices'
                ]
            ],
            'longtext' => [
                'name' => '精品长文本API',
                'description' => '支持最大10万字符的长文本合成',
                'max_text_length' => 100000,
                'quality' => 'standard',
                'sample_rate' => 16000,
                'processing_mode' => 'async',
                'features' => [
                    'long_text',
                    'async_processing',
                    'status_tracking'
                ]
            ]
        ],
        
        // 音色配置
        'voices' => [
            'bigmodel' => [
                'total_count' => 16,
                'emotion_support' => true,
                'languages' => ['zh', 'en'],
                'categories' => [
                    'emotion' => ['beijing_xiaoye_emotion', 'roumei_nvyou_emotion'],
                    'general' => ['tianmei_taozi', 'vivi_female'],
                    'special' => ['tina_teacher', 'nuanyang_kefu']
                ]
            ],
            'traditional' => [
                'total_count' => 21,
                'free_count' => 21,
                'emotion_support' => true,
                'languages' => ['zh', 'en'],
                'categories' => [
                    'general' => ['general_female', 'general_male'],
                    'character' => ['cancan_v2', 'qingcang_v2'],
                    'professional' => ['news_female', 'service_female']
                ]
            ]
        ],
        
        // 音效配置
        'audio_effects' => [
            'total_count' => 8,
            'max_combinations' => 3,
            'supported_formats' => ['mp3', 'wav', 'pcm', 'ogg_opus'],
            'intensity_range' => [0.1, 1.0],
            'default_intensity' => 0.7,
            'effects' => [
                'robot_voice' => [
                    'name' => '机器人音效',
                    'description' => '将声音转换为机器人风格',
                    'category' => 'voice_transform'
                ],
                'echo_effect' => [
                    'name' => '回声效果',
                    'description' => '添加回声效果，营造空旷感',
                    'category' => 'spatial'
                ],
                'reverb_hall' => [
                    'name' => '混响效果',
                    'description' => '模拟大厅或教堂的声学环境',
                    'category' => 'spatial'
                ],
                'chorus_effect' => [
                    'name' => '合唱效果',
                    'description' => '让单一声音听起来像多人合唱',
                    'category' => 'harmony'
                ]
            ]
        ],
        
        // 音频混合配置
        'audio_mixing' => [
            'max_tracks' => 10,
            'supported_formats' => ['mp3', 'wav', 'pcm', 'ogg_opus'],
            'sample_rates' => [16000, 22050, 24000, 44100, 48000],
            'default_sample_rate' => 44100,
            'features' => [
                'multi_track_mixing',
                'volume_control',
                'fade_in_out',
                'timeline_control'
            ]
        ],
        
        // 智能路由配置
        'smart_routing' => [
            'enabled' => true,
            'decision_factors' => [
                'text_length',
                'quality_requirement',
                'voice_cloning_need',
                'budget_constraint',
                'processing_speed'
            ],
            'rules' => [
                'voice_cloning' => 'bigmodel',
                'text_length_gt_300' => 'bigmodel',
                'quality_premium' => 'bigmodel',
                'budget_low' => 'traditional',
                'default' => 'traditional'
            ],
            'confidence_threshold' => 0.7
        ],
        
        // 性能配置
        'performance' => [
            'cache' => [
                'enabled' => true,
                'voice_list_ttl' => 3600,      // 音色列表缓存1小时
                'preview_ttl' => 14400,        // 预览缓存4小时
                'system_status_ttl' => 300     // 系统状态缓存5分钟
            ],
            'concurrency' => [
                'max_concurrent_requests' => 100,
                'rate_limit_per_minute' => 60,
                'burst_limit' => 10
            ],
            'response_time' => [
                'target_api_response' => 200,  // 目标API响应时间(ms)
                'target_cache_hit_rate' => 0.9, // 目标缓存命中率
                'target_error_rate' => 0.001   // 目标错误率
            ]
        ],
        
        // 错误处理配置
        'error_handling' => [
            'retry' => [
                'enabled' => true,
                'max_attempts' => 3,
                'backoff_strategy' => 'exponential', // linear, exponential
                'base_delay' => 1000, // 基础延迟(ms)
                'max_delay' => 10000  // 最大延迟(ms)
            ],
            'fallback' => [
                'enabled' => true,
                'bigmodel_to_traditional' => true, // 大模型失败时降级到传统
                'traditional_to_cached' => true    // 传统失败时使用缓存
            ],
            'error_codes' => [
                3005 => 'SERVICE_UNAVAILABLE',
                3010 => 'TEXT_TOO_LONG',
                3031 => 'SYNTHESIS_FAILED',
                3050 => 'INVALID_VOICE_TYPE',
                1001 => 'INVALID_PARAMS',
                1103 => 'CLONE_FAILED'
            ]
        ],
        
        // 监控配置
        'monitoring' => [
            'enabled' => true,
            'metrics' => [
                'request_count',
                'response_time',
                'error_rate',
                'cache_hit_rate',
                'api_usage_by_type'
            ],
            'alerts' => [
                'error_rate_threshold' => 0.05,    // 错误率超过5%告警
                'response_time_threshold' => 5000, // 响应时间超过5秒告警
                'cache_miss_threshold' => 0.5      // 缓存命中率低于50%告警
            ]
        ],
        
        // 开发配置
        'development' => [
            'debug' => env('APP_DEBUG', false),
            'log_requests' => env('VOLCENGINE_LOG_REQUESTS', true),
            'log_responses' => env('VOLCENGINE_LOG_RESPONSES', false),
            'mock_mode' => env('VOLCENGINE_MOCK_MODE', true),
            'test_voice_id' => 'beijing_xiaoye_emotion',
            'test_text' => '你好，这是火山引擎豆包语音API的测试。'
        ]
    ]
];

/**
 * 🔧 LongDev1使用说明：
 * 
 * 1. 环境变量配置：
 *    在 .env 文件中设置相应的环境变量
 * 
 * 2. 配置加载：
 *    $config = config('ai_platforms.volcengine');
 * 
 * 3. 服务初始化：
 *    $volcengine = new VolcengineService($config);
 * 
 * 4. 功能检查：
 *    if ($config['features']['voice_cloning']) {
 *        // 支持声音复刻功能
 *    }
 * 
 * 5. 性能监控：
 *    根据 monitoring 配置设置相应的监控指标
 */
?>
