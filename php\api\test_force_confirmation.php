<?php

require 'vendor/autoload.php';
require 'bootstrap/app.php';

echo "=== 测试 --force 参数的三次确认功能 ===\n\n";

echo "当您运行以下命令时：\n";
echo "php artisan storage:init-folders --force\n\n";

echo "系统将会显示以下确认流程：\n\n";

echo "🔥 第一次确认：\n";
echo "⚠️  警告：您正在使用 --force 参数！\n";
echo "⚠️  这将会删除所有现有的存储目录及其内容！\n";
echo "⚠️  包括：项目文件、角色资源、用户上传文件等所有数据！\n\n";

echo "🔥 第一次确认：\n";
echo "这是一个破坏性操作，将永久删除以下目录中的所有文件：\n";
echo "- storage/app/public/projects/ (所有项目文件)\n";
echo "- storage/app/public/characters/ (所有角色资源)\n";
echo "- storage/app/public/users/ (所有用户文件)\n";
echo "- storage/app/public/resources/ (所有生成的资源)\n";
echo "- 以及其他所有存储目录...\n\n";
echo "❓ 您确定要继续吗？这将删除所有现有文件！ (yes/no) [no]:\n";
echo "   👆 用户需要输入 'yes' 才能继续\n\n";

echo "🔥 第二次确认：\n";
echo "请再次确认：您真的要删除所有存储目录中的文件吗？\n";
echo "删除后的文件将无法恢复！\n\n";
echo "❓ 请输入 \"yes\" 确认删除所有文件 (yes/no) [no]:\n";
echo "   👆 用户需要再次输入 'yes' 才能继续\n\n";

echo "🔥 第三次确认（最严格）：\n";
echo "这是最后一次确认机会！\n";
echo "执行后将立即开始删除所有存储目录！\n";
echo "您确定已经备份了重要数据吗？\n\n";
echo "❓ 请输入 \"DELETE ALL FILES\" 来最终确认删除操作（区分大小写）:\n";
echo "   👆 用户必须输入完全匹配的 'DELETE ALL FILES' 才能继续\n\n";

echo "⏳ 确认完成，将在 3 秒后开始删除操作...\n";
echo "按 Ctrl+C 可以取消操作\n";
echo "倒计时: 3 秒...\n";
echo "倒计时: 2 秒...\n";
echo "倒计时: 1 秒...\n\n";

echo "开始执行强制删除操作...\n\n";

echo "=== 安全特性 ===\n";
echo "✅ 三重确认机制\n";
echo "✅ 明确的警告信息\n";
echo "✅ 需要输入特定文本确认\n";
echo "✅ 3秒倒计时给用户反悔时间\n";
echo "✅ 任何时候都可以按 Ctrl+C 取消\n\n";

echo "=== 取消操作的方式 ===\n";
echo "1. 第一次确认时输入 'no' 或直接回车\n";
echo "2. 第二次确认时输入 'no' 或直接回车\n";
echo "3. 第三次确认时输入错误的文本\n";
echo "4. 倒计时期间按 Ctrl+C\n\n";

echo "=== 建议 ===\n";
echo "🛡️  生产环境请谨慎使用 --force 参数\n";
echo "💾 使用前请务必备份重要数据\n";
echo "🧪 建议在测试环境中先试用\n";

echo "\n=== 测试完成 ===\n";
