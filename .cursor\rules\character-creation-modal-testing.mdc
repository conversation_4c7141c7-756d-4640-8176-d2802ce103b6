# Py视频创作工具 - 角色创建罩层测试体系

## 📋 测试体系概述

本文档建立了角色创建罩层组件的完整质量保证体系，包括单元测试、集成测试、性能测试、兼容性测试等多个层面，确保组件的稳定性和可靠性。

## 🎯 测试策略

### 测试金字塔

```
    E2E测试 (10%)
   ↗            ↖
集成测试 (30%)    UI测试 (20%)
   ↗            ↖
单元测试 (40%)
```

### 测试覆盖目标

- **代码覆盖率**: ≥ 90%
- **分支覆盖率**: ≥ 85%
- **功能覆盖率**: 100%
- **场景覆盖率**: ≥ 95%

## 🔧 单元测试

### 测试框架配置

```javascript
// jest.config.js
module.exports = {
    testEnvironment: 'jsdom',
    setupFilesAfterEnv: ['<rootDir>/src/tests/setup.js'],
    collectCoverageFrom: [
        'src/components/CharacterCreationModal/**/*.js',
        '!src/components/CharacterCreationModal/**/*.test.js'
    ],
    coverageThreshold: {
        global: {
            branches: 85,
            functions: 90,
            lines: 90,
            statements: 90
        }
    }
};
```

### 核心功能单元测试

```javascript
// CharacterCreationModal.test.js
import { CharacterCreationModal } from '../CharacterCreationModal';
import { render, fireEvent, waitFor } from '@testing-library/react';

describe('CharacterCreationModal', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Modal Opening', () => {
        test('should open modal with correct parameters', () => {
            const options = {
                mode: 'full',
                title: 'Test Modal',
                callbacks: { onSuccess: jest.fn() }
            };

            CharacterCreationModal.open(options);
            
            expect(document.querySelector('.character-modal')).toBeInTheDocument();
            expect(document.querySelector('.modal-title')).toHaveTextContent('Test Modal');
        });

        test('should validate required parameters', () => {
            expect(() => {
                CharacterCreationModal.open({});
            }).toThrow('mode参数是必需的');
        });

        test('should apply correct mode configuration', () => {
            CharacterCreationModal.open({ mode: 'quick' });
            
            expect(document.querySelector('.quick-mode')).toBeInTheDocument();
            expect(document.querySelector('.advanced-options')).not.toBeInTheDocument();
        });
    });

    describe('Parameter Validation', () => {
        test('should validate filters in filtered mode', () => {
            expect(() => {
                CharacterCreationModal.open({
                    mode: 'filtered'
                    // missing filters
                });
            }).toThrow('filtered模式需要filters参数');
        });

        test('should validate user permissions', () => {
            const mockUser = { level: 'basic' };
            jest.spyOn(UserService, 'getCurrentUser').mockReturnValue(mockUser);

            expect(() => {
                CharacterCreationModal.open({
                    mode: 'advanced',
                    features: { enableAPIAccess: true }
                });
            }).toThrow('用户权限不足');
        });
    });

    describe('Callback Handling', () => {
        test('should call onSuccess callback with correct data', async () => {
            const onSuccess = jest.fn();
            const mockCharacter = { id: 'char_123', name: 'Test Character' };

            CharacterCreationModal.open({
                mode: 'quick',
                callbacks: { onSuccess }
            });

            // 模拟角色创建成功
            await CharacterCreationModal._handleCreationSuccess(mockCharacter);

            expect(onSuccess).toHaveBeenCalledWith({
                success: true,
                data: { character: mockCharacter }
            });
        });

        test('should call onError callback on failure', async () => {
            const onError = jest.fn();
            const mockError = { code: 'CREATION_FAILED', message: 'Test error' };

            CharacterCreationModal.open({
                mode: 'full',
                callbacks: { onError }
            });

            await CharacterCreationModal._handleCreationError(mockError);

            expect(onError).toHaveBeenCalledWith(mockError);
        });
    });
});
```

### 业务逻辑单元测试

```javascript
// CharacterCreationLogic.test.js
describe('CharacterCreationLogic', () => {
    describe('Style Filtering', () => {
        test('should filter styles based on project type', () => {
            const styles = ['写实风3.0', '动漫可爱3.0', '武侠古风3.0'];
            const filtered = filterStylesByProjectType(styles, 'animation');
            
            expect(filtered).toContain('动漫可爱3.0');
            expect(filtered).not.toContain('写实风3.0');
        });
    });

    describe('Permission Checking', () => {
        test('should check user permissions correctly', () => {
            const user = { level: 'premium', permissions: ['create', 'publish'] };
            
            expect(hasPermission(user, 'create')).toBe(true);
            expect(hasPermission(user, 'admin')).toBe(false);
        });
    });

    describe('Configuration Merging', () => {
        test('should merge user level config correctly', () => {
            const baseConfig = { mode: 'full' };
            const userConfig = { features: { enableUpload: true } };
            
            const merged = mergeConfigs(baseConfig, userConfig);
            
            expect(merged).toEqual({
                mode: 'full',
                features: { enableUpload: true }
            });
        });
    });
});
```

## 🔗 集成测试

### API集成测试

```javascript
// CharacterCreationAPI.integration.test.js
describe('Character Creation API Integration', () => {
    beforeEach(() => {
        setupMockAPI();
    });

    test('should complete full character creation flow', async () => {
        const mockAPI = {
            '/py-api/characters/styles': { styles: ['写实风3.0'] },
            '/py-api/ai-models/select-platform': { platform: 'LiblibAI' },
            '/py-api/characters/create': { character: { id: 'char_123' } }
        };

        setupAPIResponses(mockAPI);

        const result = await CharacterCreationModal.open({
            mode: 'quick',
            presets: { style: '写实风3.0' }
        });

        expect(result.success).toBe(true);
        expect(result.data.character.id).toBe('char_123');
    });

    test('should handle API failures gracefully', async () => {
        setupAPIError('/py-api/characters/create', 500);

        const onError = jest.fn();
        await CharacterCreationModal.open({
            mode: 'full',
            callbacks: { onError }
        });

        expect(onError).toHaveBeenCalledWith({
            code: 'CREATION_FAILED',
            message: expect.any(String)
        });
    });
});
```

### WebSocket集成测试

```javascript
// WebSocketIntegration.test.js
describe('WebSocket Integration', () => {
    let mockWebSocket;

    beforeEach(() => {
        mockWebSocket = new MockWebSocket();
        global.WebSocket = jest.fn(() => mockWebSocket);
    });

    test('should receive progress updates via WebSocket', async () => {
        const onProgress = jest.fn();

        CharacterCreationModal.open({
            mode: 'full',
            callbacks: { onProgress }
        });

        // 模拟WebSocket进度消息
        mockWebSocket.emit('message', {
            type: 'progress',
            data: { progress: 50, message: '处理中...' }
        });

        expect(onProgress).toHaveBeenCalledWith({
            progress: 50,
            message: '处理中...'
        });
    });

    test('should handle WebSocket connection errors', async () => {
        const onError = jest.fn();

        CharacterCreationModal.open({
            mode: 'full',
            callbacks: { onError }
        });

        mockWebSocket.emit('error', new Error('Connection failed'));

        expect(onError).toHaveBeenCalledWith({
            code: 'NETWORK_ERROR',
            message: 'WebSocket连接失败'
        });
    });
});
```

## 🚀 性能测试

### 加载性能测试

```javascript
// Performance.test.js
describe('Performance Tests', () => {
    test('should load modal within acceptable time', async () => {
        const startTime = performance.now();

        await CharacterCreationModal.open({ mode: 'full' });

        const loadTime = performance.now() - startTime;
        expect(loadTime).toBeLessThan(1000); // 1秒内加载
    });

    test('should handle large character lists efficiently', async () => {
        const largeCharacterList = generateMockCharacters(1000);
        setupAPIResponse('/py-api/characters/library', largeCharacterList);

        const startTime = performance.now();
        await CharacterCreationModal.open({ mode: 'full' });
        const renderTime = performance.now() - startTime;

        expect(renderTime).toBeLessThan(2000); // 2秒内渲染
    });

    test('should optimize memory usage', async () => {
        const initialMemory = performance.memory?.usedJSHeapSize || 0;

        // 打开和关闭多个模态框
        for (let i = 0; i < 10; i++) {
            await CharacterCreationModal.open({ mode: 'quick' });
            await CharacterCreationModal.close();
        }

        const finalMemory = performance.memory?.usedJSHeapSize || 0;
        const memoryIncrease = finalMemory - initialMemory;

        expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB内存增长限制
    });
});
```

### 并发测试

```javascript
describe('Concurrency Tests', () => {
    test('should handle multiple modal instances', async () => {
        const promises = Array.from({ length: 5 }, () =>
            CharacterCreationModal.open({ mode: 'quick' })
        );

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled');

        expect(successful.length).toBeGreaterThan(0);
    });

    test('should handle rapid open/close operations', async () => {
        for (let i = 0; i < 20; i++) {
            CharacterCreationModal.open({ mode: 'quick' });
            await CharacterCreationModal.close();
        }

        // 应该没有内存泄漏或错误
        expect(document.querySelectorAll('.character-modal')).toHaveLength(0);
    });
});
```

## 🌐 兼容性测试

### 浏览器兼容性测试

```javascript
// BrowserCompatibility.test.js
describe('Browser Compatibility', () => {
    const browsers = ['chrome', 'firefox', 'safari', 'edge'];

    browsers.forEach(browser => {
        test(`should work correctly in ${browser}`, async () => {
            setupBrowserEnvironment(browser);

            const result = await CharacterCreationModal.open({
                mode: 'full'
            });

            expect(result).toBeDefined();
            expect(document.querySelector('.character-modal')).toBeInTheDocument();
        });
    });

    test('should handle older browser gracefully', async () => {
        setupBrowserEnvironment('ie11');

        const consoleSpy = jest.spyOn(console, 'warn');
        await CharacterCreationModal.open({ mode: 'full' });

        expect(consoleSpy).toHaveBeenCalledWith(
            expect.stringContaining('浏览器版本过低')
        );
    });
});
```

### 设备兼容性测试

```javascript
describe('Device Compatibility', () => {
    test('should adapt to mobile devices', async () => {
        setupMobileEnvironment();

        await CharacterCreationModal.open({ mode: 'full' });

        const modal = document.querySelector('.character-modal');
        expect(modal).toHaveClass('mobile-optimized');
        expect(modal.style.width).toBe('100vw');
    });

    test('should work with touch events', async () => {
        setupTouchEnvironment();

        await CharacterCreationModal.open({ mode: 'full' });

        const button = document.querySelector('.create-button');
        fireEvent.touchStart(button);
        fireEvent.touchEnd(button);

        expect(button).toHaveBeenClicked();
    });
});
```

## 👥 用户体验测试

### 可访问性测试

```javascript
// Accessibility.test.js
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
    test('should have no accessibility violations', async () => {
        const { container } = render(<CharacterCreationModal mode="full" />);
        const results = await axe(container);

        expect(results).toHaveNoViolations();
    });

    test('should support keyboard navigation', async () => {
        render(<CharacterCreationModal mode="full" />);

        const firstButton = document.querySelector('button');
        firstButton.focus();

        fireEvent.keyDown(firstButton, { key: 'Tab' });

        const nextElement = document.activeElement;
        expect(nextElement).not.toBe(firstButton);
    });

    test('should have proper ARIA labels', () => {
        render(<CharacterCreationModal mode="full" />);

        const modal = document.querySelector('[role="dialog"]');
        expect(modal).toHaveAttribute('aria-labelledby');
        expect(modal).toHaveAttribute('aria-describedby');
    });
});
```

### 用户交互测试

```javascript
describe('User Interaction Tests', () => {
    test('should provide clear feedback for user actions', async () => {
        render(<CharacterCreationModal mode="full" />);

        const uploadButton = screen.getByText('上传角色图片');
        fireEvent.click(uploadButton);

        await waitFor(() => {
            expect(screen.getByText('请选择图片文件')).toBeInTheDocument();
        });
    });

    test('should validate user input', async () => {
        render(<CharacterCreationModal mode="advanced" />);

        const nameInput = screen.getByLabelText('角色名称');
        fireEvent.change(nameInput, { target: { value: '' } });
        fireEvent.blur(nameInput);

        await waitFor(() => {
            expect(screen.getByText('角色名称不能为空')).toBeInTheDocument();
        });
    });
});
```

## 🔒 安全性测试

### 输入验证测试

```javascript
describe('Security Tests', () => {
    test('should sanitize user input', () => {
        const maliciousInput = '<script>alert("xss")</script>';
        const sanitized = sanitizeInput(maliciousInput);

        expect(sanitized).not.toContain('<script>');
        expect(sanitized).toBe('alert("xss")');
    });

    test('should validate file uploads', async () => {
        const maliciousFile = new File(['malicious content'], 'test.exe', {
            type: 'application/exe'
        });

        const result = await validateUploadFile(maliciousFile);

        expect(result.valid).toBe(false);
        expect(result.error).toContain('不支持的文件类型');
    });

    test('should protect against CSRF attacks', async () => {
        const mockRequest = {
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
            body: { action: 'create_character' }
        };

        const isValid = validateCSRFToken(mockRequest);
        expect(isValid).toBe(true);
    });
});
```

## 📊 测试数据管理

### 测试数据工厂

```javascript
// TestDataFactory.js
class TestDataFactory {
    static createCharacter(overrides = {}) {
        return {
            id: 'char_' + Math.random().toString(36).substr(2, 9),
            name: 'Test Character',
            style: '写实风3.0',
            mode: 'ai_created',
            imageUrl: 'https://example.com/test.jpg',
            ...overrides
        };
    }

    static createUser(level = 'basic') {
        return {
            id: 'user_' + Math.random().toString(36).substr(2, 9),
            level,
            permissions: this.getPermissionsByLevel(level),
            points: 100
        };
    }

    static createProject(type = 'video') {
        return {
            id: 'proj_' + Math.random().toString(36).substr(2, 9),
            type,
            name: 'Test Project',
            characters: []
        };
    }

    static getPermissionsByLevel(level) {
        const permissions = {
            basic: ['create', 'use'],
            premium: ['create', 'use', 'publish'],
            professional: ['create', 'use', 'publish', 'admin']
        };
        return permissions[level] || permissions.basic;
    }
}
```

### Mock服务

```javascript
// MockServices.js
class MockAPIService {
    static setupMocks() {
        jest.spyOn(APIService, 'get').mockImplementation(this.mockGet);
        jest.spyOn(APIService, 'post').mockImplementation(this.mockPost);
        jest.spyOn(WebSocketService, 'connect').mockImplementation(this.mockWebSocket);
    }

    static mockGet(url) {
        const responses = {
            '/py-api/characters/styles': {
                styles: ['写实风3.0', '动漫可爱3.0', '武侠古风3.0']
            },
            '/py-api/characters/library': {
                characters: [
                    TestDataFactory.createCharacter(),
                    TestDataFactory.createCharacter()
                ]
            }
        };

        return Promise.resolve(responses[url] || {});
    }

    static mockPost(url, data) {
        if (url === '/py-api/characters/create') {
            return Promise.resolve({
                character: TestDataFactory.createCharacter(data)
            });
        }
        return Promise.resolve({});
    }

    static mockWebSocket() {
        return new MockWebSocket();
    }
}
```

## 📈 测试报告和监控

### 测试覆盖率报告

```javascript
// coverage.config.js
module.exports = {
    reporters: [
        'default',
        ['jest-html-reporters', {
            publicPath: './test-reports',
            filename: 'coverage-report.html'
        }]
    ],
    collectCoverageFrom: [
        'src/**/*.{js,jsx}',
        '!src/**/*.test.{js,jsx}',
        '!src/tests/**'
    ],
    coverageReporters: ['text', 'lcov', 'html']
};
```

### 持续集成配置

```yaml
# .github/workflows/test.yml
name: Character Creation Modal Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run unit tests
      run: npm run test:unit
      
    - name: Run integration tests
      run: npm run test:integration
      
    - name: Run e2e tests
      run: npm run test:e2e
      
    - name: Generate coverage report
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
```

## 🔄 测试维护策略

### 测试更新流程

1. **功能变更时**: 同步更新相关测试用例
2. **Bug修复时**: 添加回归测试用例
3. **性能优化时**: 更新性能基准测试
4. **API变更时**: 更新集成测试

### 测试质量保证

- **代码审查**: 所有测试代码都需要经过审查
- **测试覆盖率**: 维持90%以上的覆盖率
- **测试稳定性**: 定期检查和修复不稳定的测试
- **测试性能**: 确保测试套件在合理时间内完成

---

*本测试体系确保角色创建罩层组件的高质量和稳定性，为用户提供可靠的功能体验。*
