{"data": {"code": 200, "message": "success", "data": {"voices": [{"voice_id": "beijing_xiaoye_emotion", "volcengine_id": "zh_male_beijing<PERSON><PERSON><PERSON>_emo_v2_mars_bigtts", "name": "北京小爷（多情感）", "gender": "male", "language": ["zh"], "emotions": ["生气", "惊讶", "恐惧", "激动", "冷漠", "中性"], "features": ["语音合成", "多情感"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "rou<PERSON>_n<PERSON><PERSON>_emotion", "volcengine_id": "zh_female_rou<PERSON><PERSON><PERSON><PERSON>_emo_v2_mars_bigtts", "name": "柔美女友（多情感）", "gender": "female", "language": ["zh"], "emotions": ["开心", "悲伤", "生气", "惊讶", "恐惧", "厌恶", "激动", "冷漠", "中性"], "features": ["语音合成", "多情感"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "yangguang_qingnian_emotion", "volcengine_id": "zh_male_yangguangqingnian_emo_v2_mars_bigtts", "name": "阳光青年（多情感）", "gender": "male", "language": ["zh"], "emotions": ["开心", "悲伤", "生气", "恐惧", "激动", "冷漠", "中性"], "features": ["语音合成", "多情感"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "shuang<PERSON><PERSON>_sisi_emotion", "volcengine_id": "zh_female_shuangku<PERSON>isi_emo_v2_mars_bigtts", "name": "爽快思思（多情感）", "gender": "female", "language": ["zh"], "emotions": ["开心", "悲伤", "生气", "惊讶", "激动", "冷漠", "中性"], "features": ["语音合成", "多情感"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "glen_emotion_en", "volcengine_id": "en_male_glen_emo_v2_mars_bigtts", "name": "<PERSON>（英文多情感）", "gender": "male", "language": ["en", "zh"], "emotions": ["深情", "愤怒", "ASMR", "对话/闲聊", "兴奋", "愉悦", "中性", "悲伤", "温暖"], "features": ["语音合成", "多情感", "多语种"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "sylus_emotion_en", "volcengine_id": "en_male_sylus_emo_v2_mars_bigtts", "name": "<PERSON><PERSON><PERSON>（英文多情感）", "gender": "male", "language": ["en", "zh"], "emotions": ["深情", "愤怒", "ASMR", "对话/闲聊", "兴奋", "愉悦", "中性", "悲伤", "温暖"], "features": ["语音合成", "多情感", "多语种"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "candice_emotion_en", "volcengine_id": "en_female_candice_emo_v2_mars_bigtts", "name": "<PERSON><PERSON>（英文多情感）", "gender": "female", "language": ["en", "zh"], "emotions": ["深情", "愤怒", "ASMR", "对话/闲聊", "兴奋", "愉悦", "中性", "温暖"], "features": ["语音合成", "多情感", "多语种"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "tian<PERSON>_ta<PERSON>i", "volcengine_id": "zh_female_tian<PERSON><PERSON><PERSON>i_mars_bigtts", "name": "甜美桃子", "gender": "female", "language": ["zh"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "vivi_female", "volcengine_id": "zh_female_vv_mars_bigtts", "name": "Vivi", "gender": "male", "language": ["zh"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "cancan_shiny", "volcengine_id": "zh_female_cancan_mars_bigtts", "name": "灿灿/Shiny", "gender": "female", "language": ["zh", "en"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "qingxin_nvsheng", "volcengine_id": "zh_female_qingxinnvsheng_mars_bigtts", "name": "清新女声", "gender": "female", "language": ["zh"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "zhixing_nvsheng", "volcengine_id": "zh_female_zhixingnvsheng_mars_bigtts", "name": "知性女声", "gender": "female", "language": ["zh"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "qingshuang_nanda", "volcengine_id": "zh_male_q<PERSON><PERSON><PERSON><PERSON><PERSON>_mars_bigtts", "name": "清爽男大", "gender": "male", "language": ["zh"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "tina_teacher", "volcengine_id": "zh_female_y<PERSON><PERSON><PERSON><PERSON><PERSON>_mars_bigtts", "name": "Tina老师（教育场景）", "gender": "female", "language": ["zh", "en"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成", "教育场景"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "nuanyang_kefu", "volcengine_id": "zh_female_kefunvsheng_mars_bigtts", "name": "暖阳女声（客服场景）", "gender": "female", "language": ["zh"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成", "客服场景"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "morgan_jieshuo", "volcengine_id": "zh_male_ji<PERSON><PERSON><PERSON><PERSON>ng_mars_bigtts", "name": "磁性解说男声/Morgan", "gender": "male", "language": ["zh", "en"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成", "解说配音"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}, {"voice_id": "hope_jitang", "volcengine_id": "zh_female_jitang<PERSON><PERSON>_mars_bigtts", "name": "鸡汤妹妹/Hope", "gender": "female", "language": ["zh", "en"], "emotions": ["中性", "开心", "温暖"], "features": ["语音合成"], "quality": "premium", "api_type": "bigmodel", "pricing": {"input_cost": 0.00014, "output_cost": 0.00028, "currency": "USD"}}], "total_count": 17, "api_type": "bigmodel", "features": {"voice_cloning": true, "emotion_control": true, "multi_language": true, "long_text": true, "max_text_length": 5000}}, "timestamp": 1752865004}, "created_at": 1752865004, "expires_at": 1752868604, "ttl": 3600}