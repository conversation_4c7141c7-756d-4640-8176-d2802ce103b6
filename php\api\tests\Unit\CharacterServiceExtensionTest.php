<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\CharacterLibrary;
use App\Models\UserCharacterBinding;
use App\Models\UserFile;
use App\Models\Project;
use App\Services\PyApi\CharacterService;
use App\Enums\ApiCodeEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;

/**
 * CharacterService扩展功能单元测试
 */
class CharacterServiceExtensionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $characterService;
    protected $user;
    protected $character;
    protected $project;
    protected $file;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->characterService = app(CharacterService::class);
        
        // 创建测试数据
        $this->user = User::factory()->create();
        $this->character = CharacterLibrary::factory()->create([
            'name' => '测试角色',
            'style' => '写实风3.0',
            'status' => 'published'
        ]);
        $this->project = Project::factory()->create([
            'user_id' => $this->user->id
        ]);
        $this->file = UserFile::factory()->create([
            'user_id' => $this->user->id,
            'mime_type' => 'image/jpeg',
            'file_url' => 'https://example.com/test.jpg'
        ]);
    }

    /**
     * 测试扩展的bindCharacter方法
     */
    public function testBindCharacterWithExtendedParams()
    {
        $bindingParams = [
            'reason' => '单元测试绑定',
            'storyboard_position_id' => 'pos_unit_test',
            'binding_context' => 'storyboard',
            'auto_bind' => true,
            'compatibility_check' => true
        ];

        $result = $this->characterService->bindCharacter(
            $this->user->id,
            $this->character->id,
            $bindingParams
        );

        $this->assertEquals(ApiCodeEnum::SUCCESS, $result['code']);
        $this->assertEquals('角色绑定成功', $result['message']);
        $this->assertArrayHasKey('binding_id', $result['data']);
        $this->assertEquals('pos_unit_test', $result['data']['storyboard_position_id']);
        $this->assertEquals('storyboard', $result['data']['binding_context']);

        // 验证数据库记录
        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $this->character->id,
            'storyboard_position_id' => 'pos_unit_test',
            'binding_context' => 'storyboard'
        ]);
    }

    /**
     * 测试角色不存在的情况
     */
    public function testBindCharacterNotFound()
    {
        $result = $this->characterService->bindCharacter(
            $this->user->id,
            99999, // 不存在的角色ID
            ['reason' => '测试']
        );

        $this->assertEquals(ApiCodeEnum::NOT_FOUND, $result['code']);
        $this->assertEquals('角色不存在', $result['message']);
    }

    /**
     * 测试重复绑定检查
     */
    public function testBindCharacterDuplicate()
    {
        // 先创建一个绑定
        UserCharacterBinding::create([
            'user_id' => $this->user->id,
            'character_id' => $this->character->id,
            'binding_name' => $this->character->name,
            'is_active' => true
        ]);

        // 尝试重复绑定
        $result = $this->characterService->bindCharacter(
            $this->user->id,
            $this->character->id,
            ['reason' => '重复绑定测试']
        );

        $this->assertEquals(ApiCodeEnum::FAIL, $result['code']);
        $this->assertEquals('角色已经绑定', $result['message']);
    }

    /**
     * 测试createCharacterFromFile方法
     */
    public function testCreateCharacterFromFile()
    {
        $creationParams = [
            'file_id' => $this->file->id,
            'style' => '动漫可爱3.0',
            'publish_to_library' => true,
            'auto_bind' => false
        ];

        $result = $this->characterService->createCharacterFromFile(
            $this->user->id,
            $this->project->id,
            $creationParams
        );

        $this->assertEquals(ApiCodeEnum::SUCCESS, $result['code']);
        $this->assertEquals('角色创建成功', $result['message']);
        $this->assertArrayHasKey('character_id', $result['data']);
        $this->assertEquals('动漫可爱3.0', $result['data']['style']);

        // 验证数据库记录
        $this->assertDatabaseHas('character_library', [
            'style' => '动漫可爱3.0',
            'project_id' => $this->project->id,
            'source_file_id' => $this->file->id,
            'is_public' => true // publish_to_library = true
        ]);
    }

    /**
     * 测试文件不存在的情况
     */
    public function testCreateCharacterFromFileNotFound()
    {
        $creationParams = [
            'file_id' => 'non_existent_file'
        ];

        $result = $this->characterService->createCharacterFromFile(
            $this->user->id,
            null,
            $creationParams
        );

        $this->assertEquals(ApiCodeEnum::NOT_FOUND, $result['code']);
        $this->assertEquals('文件不存在', $result['message']);
    }

    /**
     * 测试无权访问文件的情况
     */
    public function testCreateCharacterFromFileUnauthorized()
    {
        // 创建其他用户的文件
        $otherUser = User::factory()->create();
        $otherUserFile = UserFile::factory()->create([
            'user_id' => $otherUser->id,
            'mime_type' => 'image/jpeg'
        ]);

        $creationParams = [
            'file_id' => $otherUserFile->id
        ];

        $result = $this->characterService->createCharacterFromFile(
            $this->user->id,
            null,
            $creationParams
        );

        $this->assertEquals(ApiCodeEnum::FORBIDDEN, $result['code']);
        $this->assertEquals('无权访问该文件', $result['message']);
    }

    /**
     * 测试自动绑定功能
     */
    public function testCreateCharacterFromFileWithAutoBind()
    {
        $creationParams = [
            'file_id' => $this->file->id,
            'style' => '写实风3.0',
            'auto_bind' => true,
            'storyboard_position_id' => 'pos_auto_bind'
        ];

        $result = $this->characterService->createCharacterFromFile(
            $this->user->id,
            $this->project->id,
            $creationParams
        );

        $this->assertEquals(ApiCodeEnum::SUCCESS, $result['code']);
        
        $characterId = $result['data']['character_id'];

        // 验证自动绑定是否成功
        $this->assertDatabaseHas('user_character_bindings', [
            'user_id' => $this->user->id,
            'character_id' => $characterId,
            'storyboard_position_id' => 'pos_auto_bind',
            'binding_context' => 'project'
        ]);
    }

    /**
     * 测试兼容性检查方法
     */
    public function testCheckCharacterCompatibility()
    {
        // 使用反射访问私有方法
        $reflection = new \ReflectionClass($this->characterService);
        $method = $reflection->getMethod('checkCharacterCompatibility');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->characterService,
            $this->character,
            'pos_compatibility_test'
        );

        $this->assertIsArray($result);
        $this->assertArrayHasKey('compatible', $result);
        $this->assertArrayHasKey('reason', $result);
        $this->assertArrayHasKey('suggestions', $result);
        
        // 默认情况下应该返回兼容
        $this->assertTrue($result['compatible']);
    }

    /**
     * 测试重新激活已解绑的角色
     */
    public function testReactivateInactiveBinding()
    {
        // 创建一个已解绑的绑定记录
        $inactiveBinding = UserCharacterBinding::create([
            'user_id' => $this->user->id,
            'character_id' => $this->character->id,
            'binding_name' => $this->character->name,
            'is_active' => false
        ]);

        $bindingParams = [
            'reason' => '重新激活测试',
            'storyboard_position_id' => 'pos_reactivate',
            'binding_context' => 'project'
        ];

        $result = $this->characterService->bindCharacter(
            $this->user->id,
            $this->character->id,
            $bindingParams
        );

        $this->assertEquals(ApiCodeEnum::SUCCESS, $result['code']);

        // 验证绑定被重新激活
        $inactiveBinding->refresh();
        $this->assertTrue($inactiveBinding->is_active);
        $this->assertEquals('pos_reactivate', $inactiveBinding->storyboard_position_id);
        $this->assertEquals('project', $inactiveBinding->binding_context);
    }

    /**
     * 测试不同绑定上下文
     */
    public function testDifferentBindingContexts()
    {
        $contexts = ['storyboard', 'project', 'library'];

        foreach ($contexts as $context) {
            // 为每个上下文创建不同的角色
            $character = CharacterLibrary::factory()->create([
                'name' => "测试角色_{$context}",
                'status' => 'published'
            ]);

            $bindingParams = [
                'reason' => "测试{$context}上下文",
                'binding_context' => $context,
                'storyboard_position_id' => "pos_{$context}"
            ];

            $result = $this->characterService->bindCharacter(
                $this->user->id,
                $character->id,
                $bindingParams
            );

            $this->assertEquals(ApiCodeEnum::SUCCESS, $result['code']);
            $this->assertEquals($context, $result['data']['binding_context']);

            // 验证数据库记录
            $this->assertDatabaseHas('user_character_bindings', [
                'user_id' => $this->user->id,
                'character_id' => $character->id,
                'binding_context' => $context
            ]);
        }
    }
}
