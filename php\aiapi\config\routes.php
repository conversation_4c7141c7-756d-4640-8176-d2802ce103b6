<?php
/**
 * 模拟AI的API服务 - 路由配置
 * 定义所有API接口的路由规则
 */

$routes = [
    // ==================== DeepSeek API 路由 ====================
    
    // 对话完成接口
    [
        'method' => 'POST',
        'path' => 'deepseek/chat/completions',
        'controller' => 'DeepSeekController',
        'action' => 'chatCompletions',
        'description' => 'DeepSeek对话完成接口 - 剧情生成和分镜脚本'
    ],
    
    // FIM补全接口
    [
        'method' => 'POST',
        'path' => 'deepseek/completions',
        'controller' => 'DeepSeekController',
        'action' => 'completions',
        'description' => 'DeepSeek代码补全接口'
    ],
    
    // 模型列表
    [
        'method' => 'GET',
        'path' => 'deepseek/models',
        'controller' => 'DeepSeekController',
        'action' => 'listModels',
        'description' => 'DeepSeek模型列表'
    ],
    
    // 余额查询
    [
        'method' => 'GET',
        'path' => 'deepseek/user/balance',
        'controller' => 'DeepSeekController',
        'action' => 'getBalance',
        'description' => 'DeepSeek余额查询'
    ],
    
    // ==================== LiblibAI API 路由 (按官方文档) - 🔥 LongDev1全面修复 ====================

    // 🔥 LongDev1新增 - IMG1智能算法接口
    // IMG1文生图
    [
        'method' => 'POST',
        'path' => 'api/open/img1/text2img',
        'controller' => 'LiblibController',
        'action' => 'img1_text2img',
        'description' => 'LiblibAI IMG1智能算法文生图'
    ],

    // IMG1图生图
    [
        'method' => 'POST',
        'path' => 'api/open/img1/img2img',
        'controller' => 'LiblibController',
        'action' => 'img1_img2img',
        'description' => 'LiblibAI IMG1智能算法图生图'
    ],

    // IMG1局部重绘
    [
        'method' => 'POST',
        'path' => 'api/open/img1/inpaint',
        'controller' => 'LiblibController',
        'action' => 'img1_inpaint',
        'description' => 'LiblibAI IMG1局部重绘'
    ],

    // IMG1任务查询
    [
        'method' => 'GET',
        'path' => 'api/open/img1/query',
        'controller' => 'LiblibController',
        'action' => 'img1_query',
        'description' => 'LiblibAI IMG1任务结果查询'
    ],

    // 🔥 LongDev1新增 - 视频生成接口
    // 图像生成视频
    [
        'method' => 'POST',
        'path' => 'v1/videos/img2video',
        'controller' => 'LiblibController',
        'action' => 'img2video',
        'description' => 'LiblibAI图像生成视频'
    ],

    // 文本生成视频
    [
        'method' => 'POST',
        'path' => 'v1/videos/text2video',
        'controller' => 'LiblibController',
        'action' => 'text2video',
        'description' => 'LiblibAI文本生成视频'
    ],

    // 视频状态查询
    [
        'method' => 'GET',
        'path' => 'v1/videos/status/{taskId}',
        'controller' => 'LiblibController',
        'action' => 'video_status',
        'description' => 'LiblibAI视频生成状态查询'
    ],

    // 🔥 LongDev1新增 - 图像处理接口
    // 图像放大
    [
        'method' => 'POST',
        'path' => 'v1/images/upscale',
        'controller' => 'LiblibController',
        'action' => 'upscale_image',
        'description' => 'LiblibAI图像放大'
    ],

    // 图像修复
    [
        'method' => 'POST',
        'path' => 'v1/images/inpaint',
        'controller' => 'LiblibController',
        'action' => 'inpaint_image',
        'description' => 'LiblibAI图像修复'
    ],

    // 背景移除
    [
        'method' => 'POST',
        'path' => 'v1/images/remove-bg',
        'controller' => 'LiblibController',
        'action' => 'remove_background',
        'description' => 'LiblibAI背景移除'
    ],

    // 风格转换
    [
        'method' => 'POST',
        'path' => 'v1/images/style-transfer',
        'controller' => 'LiblibController',
        'action' => 'style_transfer',
        'description' => 'LiblibAI风格转换'
    ],

    // 🔥 LongDev1修正 - 现有接口保持向后兼容
    // 星流文生图
    [
        'method' => 'POST',
        'path' => 'api/open/xingliu/text2img',
        'controller' => 'LiblibController',
        'action' => 'xingliu_text2img',
        'description' => 'LiblibAI星流文生图 (支持F.1 Kontext)'
    ],

    // 星流图生图
    [
        'method' => 'POST',
        'path' => 'api/open/xingliu/img2img',
        'controller' => 'LiblibController',
        'action' => 'xingliu_img2img',
        'description' => 'LiblibAI星流图生图'
    ],

    // 星流任务查询 (官方路径)
    [
        'method' => 'GET',
        'path' => 'api/open/xingliu/query',
        'controller' => 'LiblibController',
        'action' => 'getTaskStatus',
        'description' => 'LiblibAI星流任务查询'
    ],

    // LiblibAI文生图
    [
        'method' => 'POST',
        'path' => 'api/open/liblib/text2img',
        'controller' => 'LiblibController',
        'action' => 'liblib_text2img',
        'description' => 'LiblibAI文生图'
    ],

    // LiblibAI图生图
    [
        'method' => 'POST',
        'path' => 'api/open/liblib/img2img',
        'controller' => 'LiblibController',
        'action' => 'liblib_img2img',
        'description' => 'LiblibAI图生图'
    ],

    // ComfyUI工作流执行
    [
        'method' => 'POST',
        'path' => 'api/open/comfyui/run',
        'controller' => 'LiblibController',
        'action' => 'comfyui_run',
        'description' => 'LiblibAI ComfyUI工作流执行'
    ],

    // 🔥 LongDev1修正 - ComfyUI查询接口路径统一
    // ComfyUI任务状态查询 (官方路径)
    [
        'method' => 'GET',
        'path' => 'api/open/comfyui/query',
        'controller' => 'LiblibController',
        'action' => 'comfyui_task_status',
        'description' => 'LiblibAI ComfyUI任务状态查询'
    ],

    // ComfyUI任务状态查询 (兼容路径)
    [
        'method' => 'GET',
        'path' => 'api/open/comfyui/task/{taskId}',
        'controller' => 'LiblibController',
        'action' => 'comfyui_task_status',
        'description' => 'LiblibAI ComfyUI任务状态查询 (兼容)'
    ],

    // 文件上传
    [
        'method' => 'POST',
        'path' => 'api/open/upload',
        'controller' => 'LiblibController',
        'action' => 'upload_file',
        'description' => 'LiblibAI文件上传'
    ],

    // 🔥 LongDev1新增 - 通用任务查询接口
    // 通用任务状态查询
    [
        'method' => 'GET',
        'path' => 'api/open/task/{taskId}',
        'controller' => 'LiblibController',
        'action' => 'getTaskStatus',
        'description' => 'LiblibAI通用任务状态查询'
    ],
    
    // ==================== KlingAI API 路由 ====================
    
    // 图像生成
    [
        'method' => 'POST',
        'path' => 'kling/v1/images/generations',
        'controller' => 'KlingController',
        'action' => 'generateImage',
        'description' => 'KlingAI图像生成'
    ],
    
    // 文生视频
    [
        'method' => 'POST',
        'path' => 'kling/v1/videos/text2video',
        'controller' => 'KlingController',
        'action' => 'textToVideo',
        'description' => 'KlingAI文生视频'
    ],
    
    // 图生视频
    [
        'method' => 'POST',
        'path' => 'kling/v1/videos/image2video',
        'controller' => 'KlingController',
        'action' => 'imageToVideo',
        'description' => 'KlingAI图生视频'
    ],
    
    // 视频任务查询
    [
        'method' => 'GET',
        'path' => 'kling/v1/videos/tasks/{taskId}',
        'controller' => 'KlingController',
        'action' => 'getVideoTask',
        'description' => 'KlingAI视频任务查询'
    ],
    
    // 认证接口
    [
        'method' => 'POST',
        'path' => 'kling/v1/auth/token',
        'controller' => 'KlingController',
        'action' => 'getAuthToken',
        'description' => 'KlingAI认证获取token'
    ],

    // 🔧 LongDev1修复：修正路由路径符合官方规范
    // 📋 修复依据：ai-api-klingai.com-guidelines.mdc 官方文档
    // ✅ 官方路径：/v1/images/generations/{taskId}
    // 图像任务查询
    [
        'method' => 'GET',
        'path' => 'kling/v1/images/generations/{taskId}',
        'controller' => 'KlingController',
        'action' => 'getImageTask',
        'description' => 'KlingAI图像任务状态查询'
    ],

    // 图像任务列表
    [
        'method' => 'GET',
        'path' => 'kling/v1/images/generations',
        'controller' => 'KlingController',
        'action' => 'listImageTasks',
        'description' => 'KlingAI图像任务列表'
    ],

    // 图像放大
    [
        'method' => 'POST',
        'path' => 'kling/v1/images/upscale',
        'controller' => 'KlingController',
        'action' => 'upscaleImage',
        'description' => 'KlingAI图像放大'
    ],

    // 🔧 LongDev1修复：新增缺失的官方接口
    // 📋 修复依据：ai-api-klingai.com-guidelines.mdc 官方文档

    // 图像变体生成
    [
        'method' => 'POST',
        'path' => 'kling/v1/images/img2img',
        'controller' => 'KlingController',
        'action' => 'imageVariation',
        'description' => 'KlingAI图像变体生成'
    ],

    // 图像修复/编辑
    [
        'method' => 'POST',
        'path' => 'kling/v1/images/inpaint',
        'controller' => 'KlingController',
        'action' => 'imageInpainting',
        'description' => 'KlingAI图像修复编辑'
    ],

    // 视频扩展
    [
        'method' => 'POST',
        'path' => 'kling/v1/videos/extend',
        'controller' => 'KlingController',
        'action' => 'extendVideo',
        'description' => 'KlingAI视频扩展'
    ],

    // 视频状态查询
    [
        'method' => 'GET',
        'path' => 'kling/v1/videos/status/{taskId}',
        'controller' => 'KlingController',
        'action' => 'getVideoStatus',
        'description' => 'KlingAI视频状态查询'
    ],
    
    // ==================== MiniMax API 路由 ====================
    // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax
    
    // 文本生成
    [
        'method' => 'POST',
        'path' => 'minimax/v1/text/chatcompletion_v2',
        'controller' => 'MiniMaxController',
        'action' => 'chatCompletion',
        'description' => 'MiniMax文本生成和对话' // 🔧 LongDev1修复：统一平台名称
    ],
    
    // 海螺视频生成
    [
        'method' => 'POST',
        'path' => 'minimax/v1/video_generation',
        'controller' => 'MiniMaxController',
        'action' => 'generateVideo',
        'description' => 'MiniMax海螺视频生成' // 🔧 LongDev1修复：统一平台名称
    ],
    
    // 语音合成
    [
        'method' => 'POST',
        'path' => 'minimax/v1/t2a_v2',
        'controller' => 'MiniMaxController',
        'action' => 'textToAudio',
        'description' => 'MiniMax语音合成' // 🔧 LongDev1修复：统一平台名称
    ],
    
    // 文件上传
    [
        'method' => 'POST',
        'path' => 'minimax/v1/files',
        'controller' => 'MiniMaxController',
        'action' => 'uploadFile',
        'description' => 'MiniMax文件上传' // 🔧 LongDev1修复：统一平台名称
    ],

    // 角色扮演对话
    [
        'method' => 'POST',
        'path' => 'minimax/v1/text/chatcompletion_pro',
        'controller' => 'MiniMaxController',
        'action' => 'chatCompletionPro',
        'description' => 'MiniMax角色扮演对话' // 🔧 LongDev1修复：统一平台名称
    ],

    // 音乐生成
    [
        'method' => 'POST',
        'path' => 'minimax/v1/music_generation',
        'controller' => 'MiniMaxController',
        'action' => 'generateMusic',
        'description' => 'MiniMax音乐生成' // 🔧 LongDev1修复：统一平台名称
    ],

    // 文件查询
    [
        'method' => 'GET',
        'path' => 'minimax/v1/files/{file_id}',
        'controller' => 'MiniMaxController',
        'action' => 'getFile',
        'description' => 'MiniMax文件查询' // 🔧 LongDev1修复：统一平台名称
    ],

    // 文件删除
    [
        'method' => 'DELETE',
        'path' => 'minimax/v1/files/{file_id}',
        'controller' => 'MiniMaxController',
        'action' => 'deleteFile',
        'description' => 'MiniMax文件删除' // 🔧 LongDev1修复：统一平台名称
    ],

    // ==================== 🆕 LongDev1新增：MiniMax图像生成接口 ====================
    // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax

    // 文本生成图像
    [
        'method' => 'POST',
        'path' => 'minimax/v1/image_generation',
        'controller' => 'MiniMaxController',
        'action' => 'generateImage',
        'description' => 'MiniMax文本生成图像' // 🔧 LongDev1修复：统一平台名称
    ],

    // 图生图
    [
        'method' => 'POST',
        'path' => 'minimax/v1/image_generation_i2i',
        'controller' => 'MiniMaxController',
        'action' => 'imageToImage',
        'description' => 'MiniMax图生图' // 🔧 LongDev1修复：统一平台名称
    ],

    // 画风控制生图
    [
        'method' => 'POST',
        'path' => 'minimax/v1/image_generation_style',
        'controller' => 'MiniMaxController',
        'action' => 'generateImageWithStyle',
        'description' => 'MiniMax画风控制生图' // 🔧 LongDev1修复：统一平台名称
    ],

    // ==================== 🆕 LongDev1新增：MiniMax完善语音合成接口 ====================
    // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax

    // 异步长文本语音合成
    [
        'method' => 'POST',
        'path' => 'minimax/v1/t2a_async',
        'controller' => 'MiniMaxController',
        'action' => 'asyncTextToAudio',
        'description' => 'MiniMax异步长文本语音合成' // 🔧 LongDev1修复：统一平台名称
    ],

    // 快速复刻
    [
        'method' => 'POST',
        'path' => 'minimax/v1/voice_clone',
        'controller' => 'MiniMaxController',
        'action' => 'voiceClone',
        'description' => 'MiniMax声音快速复刻' // 🔧 LongDev1修复：统一平台名称
    ],

    // 音色设计
    [
        'method' => 'POST',
        'path' => 'minimax/v1/voice_design',
        'controller' => 'MiniMaxController',
        'action' => 'voiceDesign',
        'description' => 'MiniMax音色设计' // 🔧 LongDev1修复：统一平台名称
    ],

    // 查询可用声音ID
    [
        'method' => 'GET',
        'path' => 'minimax/v1/query/voice',
        'controller' => 'MiniMaxController',
        'action' => 'getAvailableVoices',
        'description' => 'MiniMax查询可用声音ID' // 🔧 LongDev1修复：统一平台名称
    ],

    // 删除声音
    [
        'method' => 'DELETE',
        'path' => 'minimax/v1/voice_delete',
        'controller' => 'MiniMaxController',
        'action' => 'deleteVoice',
        'description' => 'MiniMax删除声音' // 🔧 LongDev1修复：统一平台名称
    ],

    // ==================== 🆕 LongDev1新增：MiniMax流式处理接口 ====================
    // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax

    // 流式对话
    [
        'method' => 'POST',
        'path' => 'minimax/v1/text/chatcompletion_v2_stream',
        'controller' => 'MiniMaxController',
        'action' => 'streamChatCompletion',
        'description' => 'MiniMax流式对话' // 🔧 LongDev1修复：统一平台名称
    ],

    // 流式音乐生成
    [
        'method' => 'POST',
        'path' => 'minimax/v1/music_generation_stream',
        'controller' => 'MiniMaxController',
        'action' => 'streamMusicGeneration',
        'description' => 'MiniMax流式音乐生成' // 🔧 LongDev1修复：统一平台名称
    ],

    // ==================== 🆕 LongDev1新增：MiniMax OpenAI兼容接口 ====================
    // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax

    // OpenAI兼容对话接口
    [
        'method' => 'POST',
        'path' => 'minimax/v1/chat/completions',
        'controller' => 'MiniMaxController',
        'action' => 'openaiChatCompletions',
        'description' => 'MiniMax OpenAI兼容对话接口' // 🔧 LongDev1修复：统一平台名称
    ],

    // OpenAI兼容模型列表
    [
        'method' => 'GET',
        'path' => 'minimax/v1/models',
        'controller' => 'MiniMaxController',
        'action' => 'openaiModels',
        'description' => 'MiniMax OpenAI兼容模型列表' // 🔧 LongDev1修复：统一平台名称
    ],

    // ==================== 🆕 LongDev1新增：MiniMax完善文件管理接口 ====================
    // 🔧 LongDev1修复：统一平台名称MiniMaxi→MiniMax

    // 音乐文件上传
    [
        'method' => 'POST',
        'path' => 'minimax/v1/music_upload',
        'controller' => 'MiniMaxController',
        'action' => 'uploadMusicFile',
        'description' => 'MiniMax音乐文件上传' // 🔧 LongDev1修复：统一平台名称
    ],

    // 文件列表查询
    [
        'method' => 'GET',
        'path' => 'minimax/v1/files',
        'controller' => 'MiniMaxController',
        'action' => 'listFiles',
        'description' => 'MiniMax文件列表查询' // 🔧 LongDev1修复：统一平台名称
    ],

    // 视频任务状态查询
    [
        'method' => 'GET',
        'path' => 'minimax/v1/query/video_generation/{task_id}',
        'controller' => 'MiniMaxController',
        'action' => 'getVideoTaskStatus',
        'description' => 'MiniMax视频任务状态查询' // 🔧 LongDev1修复：统一平台名称
    ],
    
    // ==================== 通用管理接口 ====================
    
    // 健康检查
    [
        'method' => 'GET',
        'path' => 'health',
        'controller' => 'SystemController',
        'action' => 'healthCheck',
        'description' => '系统健康检查'
    ],
    
    // 配置信息
    [
        'method' => 'GET',
        'path' => 'config',
        'controller' => 'SystemController',
        'action' => 'getConfig',
        'description' => '获取系统配置信息'
    ],
    
    // 接口列表
    [
        'method' => 'GET',
        'path' => 'routes',
        'controller' => 'SystemController',
        'action' => 'listRoutes',
        'description' => '获取所有接口列表'
    ],
    
    // 切换模式
    [
        'method' => 'POST',
        'path' => 'config/toggle/{platform}',
        'controller' => 'SystemController',
        'action' => 'toggleMode',
        'description' => '切换平台的模拟/真实模式'
    ],

    // 日志统计
    [
        'method' => 'GET',
        'path' => 'logs/stats',
        'controller' => 'SystemController',
        'action' => 'getLogStats',
        'description' => '获取日志统计信息'
    ],
    
    // ==================== 业务流程接口 ====================
    
    // 完整业务流程
    [
        'method' => 'POST',
        'path' => 'workflow/complete',
        'controller' => 'WorkflowController',
        'action' => 'completeWorkflow',
        'description' => '执行完整业务流程：写剧情->绑角色->生成图片->视频编辑'
    ],
    
    // 剧情创作
    [
        'method' => 'POST',
        'path' => 'workflow/story',
        'controller' => 'WorkflowController',
        'action' => 'createStory',
        'description' => '剧情创作和分镜脚本生成'
    ],
    
    // 角色绑定
    [
        'method' => 'POST',
        'path' => 'workflow/character',
        'controller' => 'WorkflowController',
        'action' => 'bindCharacter',
        'description' => '角色绑定到分镜'
    ],
    
    // 图片生成
    [
        'method' => 'POST',
        'path' => 'workflow/image',
        'controller' => 'WorkflowController',
        'action' => 'generateImage',
        'description' => '分镜图片生成'
    ],
    
    // 视频编辑
    [
        'method' => 'POST',
        'path' => 'workflow/video',
        'controller' => 'WorkflowController',
        'action' => 'editVideo',
        'description' => '视频编辑和生成'
    ],



    // ==================== 🎵 火山引擎豆包语音API路由 ====================

    // 🔧 LongDev1实施备注：
    // - 完整整合火山引擎豆包语音API
    // - 支持大模型API和传统API两套体系
    // - 包含音效库和音频混合功能
    // - 提供智能路由选择

    // 🎵 大模型音色库管理
    [
        'method' => 'GET',
        'path' => 'volcengine/bigmodel/voices/list',
        'controller' => 'VolcengineController',
        'action' => 'listBigModelVoices',
        'description' => '火山引擎大模型音色列表'
    ],
    [
        'method' => 'POST',
        'path' => 'volcengine/bigmodel/voices/synthesize',
        'controller' => 'VolcengineController',
        'action' => 'synthesizeBigModel',
        'description' => '火山引擎大模型语音合成'
    ],
    [
        'method' => 'POST',
        'path' => 'volcengine/bigmodel/voices/clone',
        'controller' => 'VolcengineController',
        'action' => 'cloneVoice',
        'description' => '火山引擎声音复刻（仅大模型支持）'
    ],
    [
        'method' => 'GET',
        'path' => 'volcengine/bigmodel/voices/clone/status/{taskId}',
        'controller' => 'VolcengineController',
        'action' => 'getCloneStatus',
        'description' => '火山引擎声音复刻状态查询'
    ],

    // 🎵 传统音色库管理
    [
        'method' => 'GET',
        'path' => 'volcengine/traditional/voices/list',
        'controller' => 'VolcengineController',
        'action' => 'listTraditionalVoices',
        'description' => '火山引擎传统音色列表'
    ],
    [
        'method' => 'POST',
        'path' => 'volcengine/traditional/voices/synthesize',
        'controller' => 'VolcengineController',
        'action' => 'synthesizeTraditional',
        'description' => '火山引擎传统语音合成'
    ],
    [
        'method' => 'POST',
        'path' => 'volcengine/traditional/voices/longtext',
        'controller' => 'VolcengineController',
        'action' => 'synthesizeLongText',
        'description' => '火山引擎精品长文本合成'
    ],

    // 🎛️ 音效库管理
    [
        'method' => 'GET',
        'path' => 'volcengine/audio/effects/list',
        'controller' => 'VolcengineController',
        'action' => 'listAudioEffects',
        'description' => '火山引擎音效列表（8种基础音效）'
    ],
    [
        'method' => 'POST',
        'path' => 'volcengine/audio/effects/apply',
        'controller' => 'VolcengineController',
        'action' => 'applyAudioEffects',
        'description' => '火山引擎音效应用'
    ],
    [
        'method' => 'POST',
        'path' => 'volcengine/audio/process',
        'controller' => 'VolcengineController',
        'action' => 'processAudio',
        'description' => '火山引擎音频处理'
    ],

    // 🎼 音频混合库管理
    [
        'method' => 'POST',
        'path' => 'volcengine/audio/mix',
        'controller' => 'VolcengineController',
        'action' => 'mixAudio',
        'description' => '火山引擎音频混合'
    ],

    // 🧠 智能路由接口
    [
        'method' => 'POST',
        'path' => 'smart/voices/synthesize',
        'controller' => 'VolcengineController',
        'action' => 'smartSynthesize',
        'description' => '智能选择最佳语音API（大模型vs传统）'
    ],

    // 🔧 通用功能
    [
        'method' => 'GET',
        'path' => 'volcengine/voices/preview/{voiceId}',
        'controller' => 'VolcengineController',
        'action' => 'getVoicePreview',
        'description' => '火山引擎音色预览'
    ],
    [
        'method' => 'GET',
        'path' => 'volcengine/system/status',
        'controller' => 'VolcengineController',
        'action' => 'getSystemStatus',
        'description' => '火山引擎系统状态'
    ],

    // ==================== 任务查询路由 ====================

    // 🔧 LongDev1修复标记 - 2025.7.19：删除重复的KlingAI路由定义
    // ❌ 原重复路由已删除：kling/v1/videos/tasks/{taskId}（与第275行重复）
    // ✅ 保留唯一的图生视频任务查询路由

    // KlingAI图生视频任务查询
    [
        'method' => 'GET',
        'path' => 'kling/v1/videos/image2video/tasks/{taskId}',
        'controller' => 'KlingController',
        'action' => 'getVideoTask',
        'description' => 'KlingAI图生视频任务状态查询'
    ]
];

// 路由中间件配置
$routeMiddleware = [
    'auth' => function($request) {
        // API认证中间件
        $token = HttpHelper::getBearerToken();
        if (!$token && in_array($request['path'], ['config/toggle', 'logs/stats'])) {
            return HttpHelper::errorResponse('UNAUTHORIZED', '需要认证token', null, 401);
        }
        return null;
    },

    'rate_limit' => function($request) {
        // 简单的频率限制
        $clientIp = HttpHelper::getClientIp();
        $cacheKey = "rate_limit_{$clientIp}";

        // 这里可以实现更复杂的频率限制逻辑
        return null;
    },

    'validate_params' => function($request) {
        // 参数验证中间件
        if ($request['method'] === 'POST' && empty($request['body'])) {
            return HttpHelper::errorResponse('MISSING_BODY', 'POST请求需要请求体');
        }
        return null;
    }
];

// 导出路由配置
$GLOBALS['aiapi_routes'] = $routes;
$GLOBALS['aiapi_middleware'] = $routeMiddleware;
