<?php
/**
 * MiniMax API控制器
 *
 * 🚨 架构边界规范：
 * ✅ 本控制器仅进行模拟，不会向真实MiniMax平台发起任何网络请求
 * ✅ 严格按照MiniMax官方API文档验证参数和返回响应格式
 * ✅ 支持成功率模拟、延迟模拟、状态模拟
 * ❌ 不产生任何真实费用，不获取真实AI生成内容
 *
 * 业务职责：多模态AI平台
 * 支持功能：文本生成、海螺视频生成、语音合成等
 */

class MiniMaxController
{
    private $logger;
    private $config;
    private $mockData;
    private $errorCodes;
    private $platform = 'minimax';

    public function __construct()
    {
        global $aiapi_config;
        $this->logger = new Logger();
        $this->config = $aiapi_config['platforms'][$this->platform];
        $this->mockData = $aiapi_config['mock_response_data'][$this->platform];
        $this->errorCodes = $aiapi_config['error_codes'];
    }
    
    /**
     * 文本生成和对话接口
     * POST /minimax/v1/text/chatcompletion_v2
     */
    public function chatCompletion($params = [])
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'messages']);
            
            // 验证模型
            $this->validateModel($data['model']);
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('SERVICE_UNAVAILABLE', '服务暂时不可用，请稍后重试');
            }
            
            // 生成响应
            $response = $this->generateChatResponse($data);
            
            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/text/chatcompletion_v2',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/text/chatcompletion_v2', $e->getMessage(), $data ?? []);
            
            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 海螺视频生成接口
     * POST /minimax/v1/video_generation
     */
    public function generateVideo($params = [])
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);
            
            // 验证参数
            $this->validateVideoParameters($data);
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('VIDEO_GENERATION_FAILED', '视频生成失败，请重试');
            }
            
            // 生成响应
            $response = $this->generateVideoResponse($data);
            
            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/video_generation',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/video_generation', $e->getMessage(), $data ?? []);
            
            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 语音合成接口
     * POST /minimax/v1/t2a_v2
     */
    public function textToAudio($params = [])
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['text']);
            
            // 验证参数
            $this->validateAudioParameters($data);
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('AUDIO_GENERATION_FAILED', '语音合成失败，请重试');
            }
            
            // 生成响应
            $response = $this->generateAudioResponse($data);
            
            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/t2a_v2',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/t2a_v2', $e->getMessage(), $data ?? []);
            
            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 文件上传接口
     * POST /minimax/v1/files
     */
    public function uploadFile($params = [])
    {
        $startTime = microtime(true);
        
        try {
            // 模拟文件上传
            $response = $this->generateFileUploadResponse();
            
            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/files',
                'POST',
                ['file_upload' => true],
                $response,
                round($duration, 2)
            );
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/files', $e->getMessage(), []);
            
            return $this->generateMiniMaxErrorResponse('UPLOAD_FAILED', '文件上传失败');
        }
    }
    
    /**
     * 生成对话响应
     */
    private function generateChatResponse($data)
    {
        $model = $data['model'];
        $messages = $data['messages'];
        
        // 生成响应内容
        $responseContent = $this->generateContentByContext($messages);
        
        // 计算token使用量
        $promptTokens = $this->estimateTokens($messages);
        $completionTokens = $this->estimateTokens($responseContent);
        $totalTokens = $promptTokens + $completionTokens;
        
        return [
            'id' => 'chatcmpl_' . uniqid(),
            'object' => 'ChatCompletion',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $responseContent
                    ],
                    'finish_reason' => 'stop'
                ]
            ],
            'usage' => [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $totalTokens
            ],
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ]
        ];
    }
    
    /**
     * 生成视频响应
     */
    private function generateVideoResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');

        // 业务状态模拟 - 增强：添加视频生成的各种状态
        $statuses = ['submitted', 'processing', 'completed', 'failed'];
        $weights = [20, 50, 25, 5]; // 提交20%，处理50%，完成25%，失败5%

        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'request_id' => 'req_' . uniqid(),
            'data' => [
                'task_id' => $taskId,
                'status' => $status,
                'prompt' => $data['prompt'],
                'model' => $data['model'] ?? 'hailuo-02',
                'created_at' => date('Y-m-d H:i:s'),
                'parameters' => [
                    'duration' => $data['duration'] ?? 6,
                    'aspect_ratio' => $data['aspect_ratio'] ?? '16:9',
                    'quality' => $data['quality'] ?? 'high'
                ]
            ]
        ];

        // 根据状态添加特定信息
        switch ($status) {
            case 'submitted':
                $baseResponse['data']['queue_position'] = rand(1, 10);
                $baseResponse['data']['estimated_time'] = rand(120, 300); // 2-5分钟
                $baseResponse['data']['message'] = '任务已提交，排队等待处理';
                break;

            case 'processing':
                $baseResponse['data']['progress'] = rand(10, 90);
                $baseResponse['data']['estimated_time'] = rand(60, 180); // 1-3分钟
                $baseResponse['data']['current_step'] = ['初始化', '场景分析', '视频生成', '后处理'][rand(0, 3)];
                $baseResponse['data']['message'] = '视频生成中...';
                break;

            case 'completed':
                $baseResponse['data']['progress'] = 100;
                $baseResponse['data']['video_url'] = HttpHelper::generateMockFileUrl('video', 'mp4');
                $baseResponse['data']['thumbnail_url'] = HttpHelper::generateMockFileUrl('image', 'jpg');
                $baseResponse['data']['duration'] = $data['duration'] ?? 6;
                $baseResponse['data']['file_size'] = rand(5, 50) . 'MB';
                $baseResponse['data']['message'] = '视频生成完成';
                break;

            case 'failed':
                $errorTypes = [
                    'CONTENT_POLICY_VIOLATION' => '内容违反使用政策',
                    'GENERATION_TIMEOUT' => '生成超时，请重试',
                    'QUOTA_EXCEEDED' => '配额已用完',
                    'INVALID_PROMPT' => '提示词无效或过于复杂',
                    'MODEL_OVERLOADED' => '模型负载过高，请稍后重试'
                ];

                $errorCode = array_rand($errorTypes);
                $baseResponse['data']['error'] = [
                    'code' => $errorCode,
                    'message' => $errorTypes[$errorCode]
                ];
                $baseResponse['data']['message'] = $errorTypes[$errorCode];
                break;
        }

        return $baseResponse;
    }
    
    /**
     * 生成语音响应
     */
    private function generateAudioResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');

        // 业务状态模拟 - 增强：添加语音合成的各种状态
        $statuses = ['processing', 'completed', 'failed', 'quota_exceeded'];
        $weights = [25, 65, 5, 5]; // 处理25%，完成65%，失败5%，配额超限5%

        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'data' => [
                'task_id' => $taskId,
                'status' => $status,
                'voice_settings' => [
                    'voice_id' => $data['voice_id'] ?? 'female-tianmei',
                    'speed' => $data['speed'] ?? 1,
                    'pitch' => $data['pitch'] ?? 0,
                    'volume' => $data['volume'] ?? 1
                ]
            ]
        ];

        // 根据状态添加特定信息
        switch ($status) {
            case 'processing':
                $baseResponse['data']['progress'] = rand(20, 80);
                $baseResponse['data']['estimated_time'] = rand(5, 30) . '秒';
                $baseResponse['data']['message'] = '语音合成中...';
                break;

            case 'completed':
                $baseResponse['data']['audio_url'] = HttpHelper::generateMockFileUrl('audio', 'mp3');
                $baseResponse['data']['duration'] = $this->estimateAudioDuration($data['text']);
                $baseResponse['data']['format'] = 'mp3';
                $baseResponse['data']['sample_rate'] = 44100;
                $baseResponse['data']['bit_rate'] = 128;
                $baseResponse['data']['file_size'] = rand(100, 2000) . 'KB';
                $baseResponse['data']['message'] = '语音合成完成';
                break;

            case 'failed':
                $errorTypes = [
                    'TEXT_TOO_LONG' => '文本长度超出限制',
                    'INVALID_VOICE_ID' => '无效的音色ID',
                    'SYNTHESIS_FAILED' => '语音合成失败，请重试',
                    'CONTENT_FILTERED' => '文本内容被过滤'
                ];

                $errorCode = array_rand($errorTypes);
                $baseResponse['data']['error'] = [
                    'code' => $errorCode,
                    'message' => $errorTypes[$errorCode]
                ];
                $baseResponse['data']['message'] = $errorTypes[$errorCode];
                break;

            case 'quota_exceeded':
                $baseResponse['data']['error'] = [
                    'code' => 'QUOTA_EXCEEDED',
                    'message' => '语音合成配额已用完，请检查账户余额'
                ];
                $baseResponse['data']['message'] = '配额已用完';
                break;
        }

        return $baseResponse;
    }
    
    /**
     * 生成文件上传响应
     */
    private function generateFileUploadResponse()
    {
        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'file_id' => 'file_' . uniqid(),
                'filename' => 'uploaded_file_' . time(),
                'size' => rand(1024, 10485760), // 1KB-10MB
                'upload_time' => date('Y-m-d H:i:s'),
                'file_url' => HttpHelper::generateMockFileUrl('upload', 'bin'),
                'status' => 'uploaded'
            ]
        ];
    }
    
    /**
     * 根据上下文生成内容
     */
    private function generateContentByContext($messages)
    {
        $lastMessage = end($messages);
        $content = $lastMessage['content'] ?? '';
        
        // 根据关键词生成不同类型的内容
        $keywords = [
            '视频' => '我可以帮您生成高质量的视频内容，包括海螺视频生成等功能。',
            '语音' => '我支持多种语音合成功能，可以将文本转换为自然流畅的语音。',
            '对话' => '我是MiniMaxi AI助手，擅长多模态内容生成和智能对话。',
            '创作' => '我可以协助您进行各种创意内容的创作，包括文本、视频、音频等。'
        ];
        
        foreach ($keywords as $keyword => $response) {
            if (strpos($content, $keyword) !== false) {
                return $response;
            }
        }
        
        return '我是MiniMaxi AI助手，专注于多模态AI服务。我可以帮助您进行文本生成、视频创作、语音合成等多种任务。请告诉我您需要什么帮助？';
    }
    
    /**
     * 🔧 LongDev1修复：更新为最新官方模型列表 - 基于ai-api-minimaxi.com-guidelines.mdc
     */
    private function validateModel($model)
    {
        $validModels = [
            // 🆕 最新文本生成模型（基于官方文档）
            'MiniMax-M1',         // 全球最长上下文窗口，支持1M-token输入，80k-token输出
            'MiniMax-Text-01',    // 标准文本生成模型，支持1M-token上下文，最大生成token数2048

            // 🆕 最新视频生成模型
            'Hailuo 02',          // 全新视频模型，指令理解更精准，支持原生1080p视频，最长可生成10秒内容
            'MiniMax-Hailuo-02',  // 新一代视频生成模型，支持1080P超清视频，10s视频生成
            'T2V-01-Director',    // 文生视频模型（导演版），支持运镜指令控制
            'I2V-01-Director',    // 图生视频模型（导演版），支持运镜指令控制
            'I2V-01-live',        // 图生视频模型，增强卡通、漫画、手绘风格表现
            'S2V-01',             // 主体参考视频生成模型，支持人物主体稳定性

            // 🆕 图像生成模型
            'image-01',           // 全新图像生成模型，支持文生图、图生图
            'image-01-live',      // 图像生成模型，支持文生图并进行画风设置

            // 🆕 语音合成模型
            'speech-01',          // 同步语音合成
            'speech-01-async',    // 异步长文本语音合成
            'speech-clone',       // 快速复刻
            'speech-design',      // 音色设计

            // 🆕 音乐生成模型
            'music-1.5',          // 新版音乐生成模型，支持prompt+lyrics
            'music-01',           // 基于上传音频的音乐生成模型

            // 🔄 保留兼容的旧模型
            'abab6.5s-chat',      // 兼容旧版对话模型
            'abab6.5-chat',
            'hailuo-02',          // 兼容旧版视频模型
            'female-tianmei'      // 兼容旧版语音模型
        ];
        if (!in_array($model, $validModels)) {
            throw new Exception('不支持的模型: ' . $model . '。支持的模型: ' . implode(', ', $validModels));
        }
    }
    
    /**
     * 验证视频参数
     */
    private function validateVideoParameters($data)
    {
        // 验证时长
        if (isset($data['duration'])) {
            $duration = intval($data['duration']);
            if ($duration < 1 || $duration > 10) {
                throw new Exception('视频时长必须在1-10秒之间');
            }
        }
        
        // 验证宽高比
        $validRatios = ['16:9', '9:16', '1:1'];
        if (isset($data['aspect_ratio']) && !in_array($data['aspect_ratio'], $validRatios)) {
            throw new Exception('不支持的宽高比: ' . $data['aspect_ratio']);
        }
    }
    
    /**
     * 验证音频参数
     */
    private function validateAudioParameters($data)
    {
        // 验证语速
        if (isset($data['speed'])) {
            $speed = floatval($data['speed']);
            if ($speed < 0.5 || $speed > 2.0) {
                throw new Exception('语速必须在0.5-2.0之间');
            }
        }
        
        // 验证音调
        if (isset($data['pitch'])) {
            $pitch = intval($data['pitch']);
            if ($pitch < -12 || $pitch > 12) {
                throw new Exception('音调必须在-12到12之间');
            }
        }
    }
    
    /**
     * 估算token数量
     */
    private function estimateTokens($content)
    {
        if (is_array($content)) {
            $text = json_encode($content, JSON_UNESCAPED_UNICODE);
        } else {
            $text = $content;
        }
        
        // 简单估算：中文字符约1.5个token，英文单词约1个token
        $chineseChars = preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text);
        $englishWords = str_word_count(preg_replace('/[\x{4e00}-\x{9fff}]/u', '', $text));
        
        return max(1, intval($chineseChars * 1.5 + $englishWords));
    }
    
    /**
     * 估算音频时长
     */
    private function estimateAudioDuration($text)
    {
        // 简单估算：中文约2字符/秒，英文约3单词/秒
        $chineseChars = preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text);
        $englishWords = str_word_count(preg_replace('/[\x{4e00}-\x{9fff}]/u', '', $text));
        
        $duration = ($chineseChars / 2) + ($englishWords / 3);
        return max(1, round($duration, 1));
    }
    
    /**
     * 生成MiniMaxi格式的错误响应 - 严格按照官方文档格式
     */
    private function generateMiniMaxErrorResponse($code, $message, $details = null)
    {
        // 根据错误类型设置正确的HTTP状态码
        $httpCode = 400;
        $statusCode = 1001; // MiniMaxi默认错误码

        switch ($code) {
            case 'UNAUTHORIZED':
            case 'AUTH_FAILED':
                $httpCode = 401;
                $statusCode = 1002;
                break;
            case 'FORBIDDEN':
                $httpCode = 403;
                $statusCode = 1003;
                break;
            case 'NOT_FOUND':
            case 'MISSING_FILE_ID':
                $httpCode = 404;
                $statusCode = 1004;
                break;
            case 'RATE_LIMIT_EXCEEDED':
                $httpCode = 429;
                $statusCode = 1005;
                break;
            case 'INTERNAL_ERROR':
            case 'PROCESSING_ERROR':
                $httpCode = 500;
                $statusCode = 1006;
                break;
            default:
                $httpCode = 400;
                $statusCode = 1001;
        }

        http_response_code($httpCode);

        // 按照MiniMaxi官方文档格式返回错误
        return [
            'base_resp' => [
                'status_code' => $statusCode,
                'status_msg' => $message
            ],
            'error' => [
                'code' => $code,
                'message' => $message,
                'details' => $details
            ],
            'data' => null,
            'timestamp' => time(),
            'request_id' => generateRequestId()
        ];
    }

    /**
     * 角色扮演对话接口
     * POST /minimax/v1/text/chatcompletion_pro
     */
    public function chatCompletionPro($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'messages', 'role_meta']);

            // 验证模型
            $this->validateModel($data['model']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('SERVICE_UNAVAILABLE', '服务暂时不可用，请稍后重试');
            }

            // 生成响应
            $response = $this->generateChatProResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/text/chatcompletion_pro',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/text/chatcompletion_pro', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 音乐生成接口
     * POST /minimax/v1/music_generation
     */
    public function generateMusic($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证参数
            $this->validateMusicParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('MUSIC_GENERATION_FAILED', '音乐生成失败，请重试');
            }

            // 生成响应
            $response = $this->generateMusicResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/music_generation',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/music_generation', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 文件查询接口
     * GET /minimax/v1/files/{file_id}
     */
    public function getFile($params = [])
    {
        $startTime = microtime(true);

        try {
            $fileId = $params['file_id'] ?? null;

            if (!$fileId) {
                return $this->generateMiniMaxErrorResponse('MISSING_FILE_ID', '缺少文件ID');
            }

            // 生成文件查询响应
            $response = $this->generateFileQueryResponse($fileId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/files/' . $fileId,
                'GET',
                ['file_id' => $fileId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/files/get', $e->getMessage(), ['file_id' => $fileId ?? 'unknown']);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 文件删除接口
     * DELETE /minimax/v1/files/{file_id}
     */
    public function deleteFile($params = [])
    {
        $startTime = microtime(true);

        try {
            $fileId = $params['file_id'] ?? null;

            if (!$fileId) {
                return $this->generateMiniMaxErrorResponse('MISSING_FILE_ID', '缺少文件ID');
            }

            // 生成文件删除响应
            $response = $this->generateFileDeleteResponse($fileId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/files/' . $fileId,
                'DELETE',
                ['file_id' => $fileId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/files/delete', $e->getMessage(), ['file_id' => $fileId ?? 'unknown']);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 生成角色扮演对话响应
     */
    private function generateChatProResponse($data)
    {
        $model = $data['model'];
        $messages = $data['messages'];
        $roleMeta = $data['role_meta'];

        // 根据角色生成响应内容
        $responseContent = $this->generateRoleBasedContent($messages, $roleMeta);

        // 计算token使用量
        $promptTokens = $this->estimateTokens($messages);
        $completionTokens = $this->estimateTokens($responseContent);
        $totalTokens = $promptTokens + $completionTokens;

        return [
            'id' => 'chatcmpl_pro_' . uniqid(),
            'object' => 'ChatCompletionPro',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $responseContent
                    ],
                    'finish_reason' => 'stop'
                ]
            ],
            'usage' => [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $totalTokens
            ],
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'role_meta' => $roleMeta
        ];
    }

    /**
     * 生成音乐响应
     */
    private function generateMusicResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'prompt' => $data['prompt'],
                'created_at' => date('Y-m-d H:i:s'),
                'estimated_time' => rand(60, 180), // 1-3分钟
                'parameters' => [
                    'duration' => $data['duration'] ?? 30,
                    'style' => $data['style'] ?? 'pop',
                    'tempo' => $data['tempo'] ?? 'medium',
                    'mood' => $data['mood'] ?? 'happy'
                ]
            ]
        ];
    }

    /**
     * 生成文件查询响应
     */
    private function generateFileQueryResponse($fileId)
    {
        // 模拟文件状态 - 增强：添加加权随机和详细错误信息
        $statuses = ['uploaded', 'processing', 'ready', 'error'];
        $weights = [15, 20, 60, 5]; // 上传15%，处理20%，就绪60%，错误5%

        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'file_id' => $fileId,
                'filename' => 'file_' . time() . '.bin',
                'size' => rand(1024, 10485760),
                'status' => $status,
                'upload_time' => date('Y-m-d H:i:s', time() - rand(0, 86400)),
                'mime_type' => 'application/octet-stream'
            ]
        ];

        // 根据状态添加特定信息
        switch ($status) {
            case 'uploaded':
                $baseResponse['data']['progress'] = 0;
                $baseResponse['data']['message'] = '文件已上传，等待处理';
                break;

            case 'processing':
                $baseResponse['data']['progress'] = rand(10, 90);
                $baseResponse['data']['message'] = '文件处理中...';
                $baseResponse['data']['estimated_time'] = rand(10, 60) . '秒';
                break;

            case 'ready':
                $baseResponse['data']['file_url'] = HttpHelper::generateMockFileUrl('file', 'bin');
                $baseResponse['data']['progress'] = 100;
                $baseResponse['data']['message'] = '文件处理完成';
                break;

            case 'error':
                $errorTypes = [
                    'UPLOAD_FAILED' => '文件上传失败',
                    'VIRUS_DETECTED' => '检测到病毒，文件已被删除',
                    'FORMAT_UNSUPPORTED' => '不支持的文件格式',
                    'FILE_TOO_LARGE' => '文件大小超出限制',
                    'PROCESSING_FAILED' => '文件处理失败'
                ];

                $errorCode = array_rand($errorTypes);
                $baseResponse['data']['error'] = [
                    'code' => $errorCode,
                    'message' => $errorTypes[$errorCode]
                ];
                $baseResponse['data']['message'] = $errorTypes[$errorCode];
                break;
        }

        return $baseResponse;
    }

    /**
     * 加权随机选择
     */
    private function weightedRandom($values, $weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);

        $currentWeight = 0;
        for ($i = 0; $i < count($values); $i++) {
            $currentWeight += $weights[$i];
            if ($random <= $currentWeight) {
                return $values[$i];
            }
        }

        return $values[0];
    }

    /**
     * 生成文件删除响应
     */
    private function generateFileDeleteResponse($fileId)
    {
        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'file_id' => $fileId,
                'deleted' => true,
                'deleted_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 根据角色生成内容
     */
    private function generateRoleBasedContent($messages, $roleMeta)
    {
        $lastMessage = end($messages);
        $content = $lastMessage['content'] ?? '';
        $roleName = $roleMeta['user_name'] ?? '助手';
        $roleDescription = $roleMeta['bot_name'] ?? '智能助手';

        // 根据角色设定生成响应
        $roleResponses = [
            '老师' => "作为{$roleDescription}，我来为您详细解答这个问题...",
            '医生' => "根据您的描述，我建议您注意以下几点...",
            '律师' => "从法律角度来看，这个问题需要考虑以下因素...",
            '翻译' => "我来为您翻译这段内容...",
            '作家' => "让我为您创作一个精彩的故事..."
        ];

        foreach ($roleResponses as $role => $response) {
            if (strpos($roleDescription, $role) !== false) {
                return $response;
            }
        }

        return "我是{$roleDescription}，很高兴为您服务。针对您的问题，我的建议是...";
    }

    /**
     * 验证音乐参数
     */
    private function validateMusicParameters($data)
    {
        // 验证时长
        if (isset($data['duration'])) {
            $duration = intval($data['duration']);
            if ($duration < 5 || $duration > 300) {
                throw new Exception('音乐时长必须在5-300秒之间');
            }
        }

        // 验证风格
        $validStyles = ['pop', 'rock', 'classical', 'jazz', 'electronic', 'folk'];
        if (isset($data['style']) && !in_array($data['style'], $validStyles)) {
            throw new Exception('不支持的音乐风格: ' . $data['style']);
        }

        // 验证节拍
        $validTempos = ['slow', 'medium', 'fast'];
        if (isset($data['tempo']) && !in_array($data['tempo'], $validTempos)) {
            throw new Exception('不支持的节拍: ' . $data['tempo']);
        }
    }

    // ==================== 🆕 LongDev1新增：图像生成接口 ====================

    /**
     * 🆕 文本生成图像接口
     * POST /minimax/v1/image_generation
     * 基于 ai-api-minimaxi.com-guidelines.mdc 官方文档实现
     */
    public function generateImage($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证模型
            $model = $data['model'] ?? 'image-01';
            $this->validateModel($model);

            // 验证图像参数
            $this->validateImageParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('IMAGE_GENERATION_FAILED', '图像生成失败，请重试');
            }

            // 生成响应
            $response = $this->generateImageResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/image_generation',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/image_generation', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 图生图接口
     * POST /minimax/v1/image_generation_i2i
     */
    public function imageToImage($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt', 'subject_reference']);

            // 验证模型
            $model = $data['model'] ?? 'image-01';
            $this->validateModel($model);

            // 验证图像参数
            $this->validateImageParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('IMAGE_GENERATION_FAILED', '图生图失败，请重试');
            }

            // 生成响应
            $response = $this->generateImageToImageResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/image_generation_i2i',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/image_generation_i2i', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 画风控制生图接口
     * POST /minimax/v1/image_generation_style
     */
    public function generateImageWithStyle($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt', 'style']);

            // 验证模型
            $model = $data['model'] ?? 'image-01-live';
            $this->validateModel($model);

            // 验证图像参数
            $this->validateImageParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('IMAGE_GENERATION_FAILED', '画风生图失败，请重试');
            }

            // 生成响应
            $response = $this->generateStyleImageResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/image_generation_style',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/image_generation_style', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    // ==================== 🆕 LongDev1新增：图像生成辅助方法 ====================

    /**
     * 🆕 验证图像参数
     */
    private function validateImageParameters($data)
    {
        // 验证宽高比
        $validRatios = ['1:1', '16:9', '4:3', '3:2', '2:3', '3:4', '9:16', '21:9'];
        if (isset($data['aspect_ratio']) && !in_array($data['aspect_ratio'], $validRatios)) {
            throw new Exception('不支持的宽高比: ' . $data['aspect_ratio']);
        }

        // 验证宽度和高度
        if (isset($data['width'])) {
            $width = intval($data['width']);
            if ($width < 512 || $width > 2048 || $width % 8 !== 0) {
                throw new Exception('宽度必须在512-2048之间且为8的倍数');
            }
        }

        if (isset($data['height'])) {
            $height = intval($data['height']);
            if ($height < 512 || $height > 2048 || $height % 8 !== 0) {
                throw new Exception('高度必须在512-2048之间且为8的倍数');
            }
        }

        // 验证生成数量
        if (isset($data['n'])) {
            $n = intval($data['n']);
            if ($n < 1 || $n > 9) {
                throw new Exception('生成数量必须在1-9之间');
            }
        }

        // 验证提示词长度
        if (isset($data['prompt'])) {
            $promptLength = mb_strlen($data['prompt'], 'UTF-8');
            if ($promptLength > 1500) {
                throw new Exception('提示词长度不能超过1500字符');
            }
        }
    }

    /**
     * 🆕 生成图像响应
     */
    private function generateImageResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');
        $n = $data['n'] ?? 1;

        // 生成多张图像
        $images = [];
        for ($i = 0; $i < $n; $i++) {
            $images[] = [
                'url' => HttpHelper::generateMockFileUrl('image', 'jpg'),
                'revised_prompt' => $data['prompt'] . ' (优化后)',
                'size' => $this->getImageSize($data),
                'style' => $data['style'] ?? 'realistic'
            ];
        }

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'data' => $images,
            'created' => time(),
            'model' => $data['model'] ?? 'image-01',
            'usage' => [
                'prompt_tokens' => $this->estimateTokens($data['prompt']),
                'total_tokens' => $this->estimateTokens($data['prompt'])
            ]
        ];
    }

    /**
     * 🆕 生成图生图响应
     */
    private function generateImageToImageResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'data' => [
                [
                    'url' => HttpHelper::generateMockFileUrl('image', 'jpg'),
                    'revised_prompt' => $data['prompt'] . ' (基于参考图像优化)',
                    'size' => $this->getImageSize($data),
                    'reference_weight' => $data['subject_reference'][0]['weight'] ?? 1.0,
                    'style' => 'reference_based'
                ]
            ],
            'created' => time(),
            'model' => $data['model'] ?? 'image-01',
            'usage' => [
                'prompt_tokens' => $this->estimateTokens($data['prompt']),
                'total_tokens' => $this->estimateTokens($data['prompt'])
            ]
        ];
    }

    /**
     * 🆕 生成画风控制图像响应
     */
    private function generateStyleImageResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'data' => [
                [
                    'url' => HttpHelper::generateMockFileUrl('image', 'jpg'),
                    'revised_prompt' => $data['prompt'] . ' (' . $data['style']['style_type'] . '风格)',
                    'size' => $this->getImageSize($data),
                    'style' => [
                        'style_type' => $data['style']['style_type'],
                        'intensity' => $data['style']['intensity'] ?? 1.0
                    ]
                ]
            ],
            'created' => time(),
            'model' => $data['model'] ?? 'image-01-live',
            'usage' => [
                'prompt_tokens' => $this->estimateTokens($data['prompt']),
                'total_tokens' => $this->estimateTokens($data['prompt'])
            ]
        ];
    }

    /**
     * 🆕 获取图像尺寸
     */
    private function getImageSize($data)
    {
        if (isset($data['width']) && isset($data['height'])) {
            return $data['width'] . 'x' . $data['height'];
        }

        // 根据宽高比返回默认尺寸
        $aspectRatio = $data['aspect_ratio'] ?? '1:1';
        $sizeMap = [
            '1:1' => '1024x1024',
            '16:9' => '1920x1080',
            '4:3' => '1024x768',
            '3:2' => '1024x683',
            '2:3' => '683x1024',
            '3:4' => '768x1024',
            '9:16' => '1080x1920',
            '21:9' => '2048x878'
        ];

        return $sizeMap[$aspectRatio] ?? '1024x1024';
    }

    // ==================== 🆕 LongDev1新增：完善语音合成功能 ====================

    /**
     * 🆕 异步长文本语音合成接口
     * POST /minimax/v1/t2a_async
     * 基于 ai-api-minimaxi.com-guidelines.mdc 官方文档实现
     */
    public function asyncTextToAudio($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['text']);

            // 验证模型
            $model = $data['model'] ?? 'speech-01-async';
            $this->validateModel($model);

            // 验证参数
            $this->validateAudioParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('AUDIO_GENERATION_FAILED', '异步语音合成失败，请重试');
            }

            // 生成响应
            $response = $this->generateAsyncAudioResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/t2a_async',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/t2a_async', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 快速复刻接口
     * POST /minimax/v1/voice_clone
     */
    public function voiceClone($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['voice_name', 'audio_file']);

            // 验证参数
            $this->validateVoiceCloneParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('VOICE_CLONE_FAILED', '声音复刻失败，请重试');
            }

            // 生成响应
            $response = $this->generateVoiceCloneResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/voice_clone',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/voice_clone', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 音色设计接口
     * POST /minimax/v1/voice_design
     */
    public function voiceDesign($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证参数
            $this->validateVoiceDesignParameters($data);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('VOICE_DESIGN_FAILED', '音色设计失败，请重试');
            }

            // 生成响应
            $response = $this->generateVoiceDesignResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/voice_design',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/voice_design', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 查询可用声音ID接口
     * GET /minimax/v1/query/voice
     */
    public function getAvailableVoices($params = [])
    {
        $startTime = microtime(true);

        try {
            // 生成可用声音列表响应
            $response = $this->generateAvailableVoicesResponse();

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/query/voice',
                'GET',
                [],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/query/voice', $e->getMessage(), []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 删除声音接口
     * DELETE /minimax/v1/voice_delete
     */
    public function deleteVoice($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['voice_id']);

            // 生成删除响应
            $response = $this->generateVoiceDeleteResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/voice_delete',
                'DELETE',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/voice_delete', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    // ==================== 🆕 LongDev1新增：语音合成辅助方法 ====================

    /**
     * 🆕 验证声音复刻参数
     */
    private function validateVoiceCloneParameters($data)
    {
        // 验证声音名称
        if (isset($data['voice_name'])) {
            $nameLength = mb_strlen($data['voice_name'], 'UTF-8');
            if ($nameLength < 1 || $nameLength > 50) {
                throw new Exception('声音名称长度必须在1-50字符之间');
            }
        }

        // 验证语言
        $validLanguages = ['zh', 'en'];
        if (isset($data['language']) && !in_array($data['language'], $validLanguages)) {
            throw new Exception('不支持的语言: ' . $data['language']);
        }
    }

    /**
     * 🆕 验证音色设计参数
     */
    private function validateVoiceDesignParameters($data)
    {
        // 验证性别
        $validGenders = ['male', 'female'];
        if (isset($data['gender']) && !in_array($data['gender'], $validGenders)) {
            throw new Exception('不支持的性别: ' . $data['gender']);
        }

        // 验证年龄
        $validAges = ['child', 'teen', 'adult', 'elder'];
        if (isset($data['age']) && !in_array($data['age'], $validAges)) {
            throw new Exception('不支持的年龄: ' . $data['age']);
        }

        // 验证情感
        $validEmotions = ['neutral', 'happy', 'sad', 'angry', 'excited', 'calm'];
        if (isset($data['emotion']) && !in_array($data['emotion'], $validEmotions)) {
            throw new Exception('不支持的情感: ' . $data['emotion']);
        }

        // 验证口音
        $validAccents = ['standard', 'regional', 'foreign'];
        if (isset($data['accent']) && !in_array($data['accent'], $validAccents)) {
            throw new Exception('不支持的口音: ' . $data['accent']);
        }
    }

    /**
     * 🆕 生成异步语音响应
     */
    private function generateAsyncAudioResponse($data)
    {
        $taskId = HttpHelper::generateTaskId('minimax');

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'task_id' => $taskId,
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'text_length' => mb_strlen($data['text'], 'UTF-8'),
                'estimated_time' => $this->estimateAudioDuration($data['text']) + 10, // 异步处理需要额外时间
                'voice_settings' => [
                    'voice_id' => $data['voice_id'] ?? 'female-tianmei',
                    'speed' => $data['speed'] ?? 1.0,
                    'pitch' => $data['pitch'] ?? 0,
                    'volume' => $data['vol'] ?? 1.0
                ],
                'audio_setting' => [
                    'sample_rate' => $data['audio_setting']['sample_rate'] ?? 32000,
                    'bitrate' => $data['audio_setting']['bitrate'] ?? 128000,
                    'format' => $data['audio_setting']['format'] ?? 'mp3'
                ]
            ]
        ];
    }

    /**
     * 🆕 生成声音复刻响应
     */
    private function generateVoiceCloneResponse($data)
    {
        $voiceId = 'cloned_' . uniqid();

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'voice_id' => $voiceId,
                'voice_name' => $data['voice_name'],
                'status' => 'training',
                'progress' => rand(10, 30),
                'estimated_time' => rand(300, 600), // 5-10分钟
                'language' => $data['language'] ?? 'zh',
                'quality_score' => rand(85, 98) / 100,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 🆕 生成音色设计响应
     */
    private function generateVoiceDesignResponse($data)
    {
        $voiceId = 'designed_' . uniqid();

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'voice_id' => $voiceId,
                'voice_name' => $data['voice_name'] ?? 'custom_voice_' . time(),
                'status' => 'completed',
                'characteristics' => [
                    'gender' => $data['gender'] ?? 'female',
                    'age' => $data['age'] ?? 'adult',
                    'emotion' => $data['emotion'] ?? 'neutral',
                    'accent' => $data['accent'] ?? 'standard'
                ],
                'preview_url' => HttpHelper::generateMockFileUrl('audio', 'mp3'),
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 🆕 生成可用声音列表响应
     */
    private function generateAvailableVoicesResponse()
    {
        $voices = [
            [
                'voice_id' => 'female-tianmei',
                'voice_name' => '甜美女声',
                'gender' => 'female',
                'age' => 'adult',
                'language' => 'zh',
                'style' => 'sweet',
                'preview_url' => HttpHelper::generateMockFileUrl('audio', 'mp3')
            ],
            [
                'voice_id' => 'male-qingse',
                'voice_name' => '清澈男声',
                'gender' => 'male',
                'age' => 'adult',
                'language' => 'zh',
                'style' => 'clear',
                'preview_url' => HttpHelper::generateMockFileUrl('audio', 'mp3')
            ],
            [
                'voice_id' => 'female-wenrou',
                'voice_name' => '温柔女声',
                'gender' => 'female',
                'age' => 'adult',
                'language' => 'zh',
                'style' => 'gentle',
                'preview_url' => HttpHelper::generateMockFileUrl('audio', 'mp3')
            ],
            [
                'voice_id' => 'male-chenwen',
                'voice_name' => '沉稳男声',
                'gender' => 'male',
                'age' => 'adult',
                'language' => 'zh',
                'style' => 'steady',
                'preview_url' => HttpHelper::generateMockFileUrl('audio', 'mp3')
            ]
        ];

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'voices' => $voices,
                'total' => count($voices)
            ]
        ];
    }

    /**
     * 🆕 生成声音删除响应
     */
    private function generateVoiceDeleteResponse($data)
    {
        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'voice_id' => $data['voice_id'],
                'deleted' => true,
                'deleted_at' => date('Y-m-d H:i:s'),
                'message' => '声音已成功删除'
            ]
        ];
    }

    // ==================== 🆕 LongDev1新增：流式处理支持 ====================

    /**
     * 🆕 流式对话接口
     * POST /minimax/v1/text/chatcompletion_v2_stream
     * 基于 ai-api-minimaxi.com-guidelines.mdc 官方文档实现
     */
    public function streamChatCompletion($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'messages']);

            // 验证模型
            $this->validateModel($data['model']);

            // 强制设置为流式
            $data['stream'] = true;

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('SERVICE_UNAVAILABLE', '流式服务暂时不可用，请稍后重试');
            }

            // 生成流式响应
            $this->generateStreamResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/text/chatcompletion_v2_stream',
                'POST',
                $data,
                ['stream' => true],
                round($duration, 2)
            );

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/text/chatcompletion_v2_stream', $e->getMessage(), $data ?? []);

            // 流式错误响应
            $this->sendStreamError($e->getMessage());
        }
    }

    /**
     * 🆕 流式音乐生成接口
     * POST /minimax/v1/music_generation_stream
     */
    public function streamMusicGeneration($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['prompt']);

            // 验证参数
            $this->validateMusicParameters($data);

            // 强制设置为流式
            $data['stream'] = true;

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('SERVICE_UNAVAILABLE', '流式音乐生成服务暂时不可用，请稍后重试');
            }

            // 生成流式音乐响应
            $this->generateStreamMusicResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/music_generation_stream',
                'POST',
                $data,
                ['stream' => true],
                round($duration, 2)
            );

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/music_generation_stream', $e->getMessage(), $data ?? []);

            // 流式错误响应
            $this->sendStreamError($e->getMessage());
        }
    }

    /**
     * 🆕 生成流式响应
     */
    private function generateStreamResponse($data)
    {
        // 设置SSE头部
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Content-Type');

        // 生成响应内容
        $content = $this->generateContentByContext($data['messages']);
        $words = explode('', $content); // 按字符分割

        $id = 'chatcmpl_stream_' . uniqid();
        $model = $data['model'];

        // 逐字发送
        foreach ($words as $index => $word) {
            $chunk = [
                'id' => $id,
                'object' => 'chat.completion.chunk',
                'created' => time(),
                'model' => $model,
                'choices' => [
                    [
                        'index' => 0,
                        'delta' => [
                            'content' => $word
                        ],
                        'finish_reason' => null
                    ]
                ]
            ];

            echo "data: " . json_encode($chunk, JSON_UNESCAPED_UNICODE) . "\n\n";
            flush();

            // 模拟打字延迟
            usleep(50000); // 50ms
        }

        // 发送结束标记
        $endChunk = [
            'id' => $id,
            'object' => 'chat.completion.chunk',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'index' => 0,
                    'delta' => [],
                    'finish_reason' => 'stop'
                ]
            ]
        ];

        echo "data: " . json_encode($endChunk, JSON_UNESCAPED_UNICODE) . "\n\n";
        echo "data: [DONE]\n\n";
        flush();
    }

    /**
     * 🆕 生成流式音乐响应
     */
    private function generateStreamMusicResponse($data)
    {
        // 设置SSE头部
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: Content-Type');

        $taskId = HttpHelper::generateTaskId('minimax');

        // 模拟音乐生成进度
        $stages = [
            ['progress' => 10, 'stage' => '分析提示词'],
            ['progress' => 25, 'stage' => '生成旋律'],
            ['progress' => 50, 'stage' => '添加和声'],
            ['progress' => 75, 'stage' => '混音处理'],
            ['progress' => 90, 'stage' => '最终渲染'],
            ['progress' => 100, 'stage' => '完成']
        ];

        foreach ($stages as $stage) {
            $chunk = [
                'task_id' => $taskId,
                'progress' => $stage['progress'],
                'stage' => $stage['stage'],
                'timestamp' => time()
            ];

            if ($stage['progress'] === 100) {
                $chunk['audio_url'] = HttpHelper::generateMockFileUrl('audio', 'mp3');
                $chunk['duration'] = $data['duration'] ?? 30;
                $chunk['format'] = 'mp3';
            }

            echo "data: " . json_encode($chunk, JSON_UNESCAPED_UNICODE) . "\n\n";
            flush();

            // 模拟处理时间
            sleep(2);
        }

        echo "data: [DONE]\n\n";
        flush();
    }

    /**
     * 🆕 发送流式错误
     */
    private function sendStreamError($message)
    {
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');

        $error = [
            'error' => [
                'message' => $message,
                'type' => 'stream_error',
                'code' => 'STREAM_ERROR'
            ]
        ];

        echo "data: " . json_encode($error, JSON_UNESCAPED_UNICODE) . "\n\n";
        echo "data: [DONE]\n\n";
        flush();
    }

    // ==================== 🆕 LongDev1新增：OpenAI兼容性接口 ====================

    /**
     * 🆕 OpenAI兼容的对话接口
     * POST /minimax/v1/chat/completions
     * 基于 ai-api-minimaxi.com-guidelines.mdc 官方文档实现
     */
    public function openaiChatCompletions($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'messages']);

            // 验证模型
            $this->validateModel($data['model']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('SERVICE_UNAVAILABLE', '服务暂时不可用，请稍后重试');
            }

            // 检查是否为流式请求
            if (isset($data['stream']) && $data['stream'] === true) {
                $this->generateStreamResponse($data);
                return;
            }

            // 生成OpenAI格式响应
            $response = $this->generateOpenAIChatResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/chat/completions',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/chat/completions', $e->getMessage(), $data ?? []);

            return $this->generateOpenAIErrorResponse($e->getMessage());
        }
    }

    /**
     * 🆕 OpenAI兼容的模型列表接口
     * GET /minimax/v1/models
     */
    public function openaiModels($params = [])
    {
        $startTime = microtime(true);

        try {
            // 生成OpenAI格式的模型列表
            $response = $this->generateOpenAIModelsResponse();

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/models',
                'GET',
                [],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/models', $e->getMessage(), []);

            return $this->generateOpenAIErrorResponse($e->getMessage());
        }
    }

    /**
     * 🆕 生成OpenAI格式的对话响应
     */
    private function generateOpenAIChatResponse($data)
    {
        $model = $data['model'];
        $messages = $data['messages'];

        // 生成响应内容
        $responseContent = $this->generateContentByContext($messages);

        // 计算token使用量
        $promptTokens = $this->estimateTokens($messages);
        $completionTokens = $this->estimateTokens($responseContent);
        $totalTokens = $promptTokens + $completionTokens;

        return [
            'id' => 'chatcmpl-' . uniqid(),
            'object' => 'chat.completion',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $responseContent
                    ],
                    'finish_reason' => 'stop'
                ]
            ],
            'usage' => [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $totalTokens
            ],
            'system_fingerprint' => 'minimax-' . date('Ymd')
        ];
    }

    /**
     * 🆕 生成OpenAI格式的模型列表响应
     */
    private function generateOpenAIModelsResponse()
    {
        $models = [
            [
                'id' => 'MiniMax-M1',
                'object' => 'model',
                'created' => 1677610602,
                'owned_by' => 'minimax',
                'permission' => [
                    [
                        'id' => 'modelperm-' . uniqid(),
                        'object' => 'model_permission',
                        'created' => 1677610602,
                        'allow_create_engine' => false,
                        'allow_sampling' => true,
                        'allow_logprobs' => false,
                        'allow_search_indices' => false,
                        'allow_view' => true,
                        'allow_fine_tuning' => false,
                        'organization' => '*',
                        'group' => null,
                        'is_blocking' => false
                    ]
                ]
            ],
            [
                'id' => 'MiniMax-Text-01',
                'object' => 'model',
                'created' => 1677610602,
                'owned_by' => 'minimax',
                'permission' => [
                    [
                        'id' => 'modelperm-' . uniqid(),
                        'object' => 'model_permission',
                        'created' => 1677610602,
                        'allow_create_engine' => false,
                        'allow_sampling' => true,
                        'allow_logprobs' => false,
                        'allow_search_indices' => false,
                        'allow_view' => true,
                        'allow_fine_tuning' => false,
                        'organization' => '*',
                        'group' => null,
                        'is_blocking' => false
                    ]
                ]
            ]
        ];

        return [
            'object' => 'list',
            'data' => $models
        ];
    }

    /**
     * 🆕 生成OpenAI格式的错误响应
     */
    private function generateOpenAIErrorResponse($message)
    {
        return [
            'error' => [
                'message' => $message,
                'type' => 'invalid_request_error',
                'param' => null,
                'code' => null
            ]
        ];
    }

    // ==================== 🆕 LongDev1新增：完善文件管理功能 ====================

    /**
     * 🆕 音乐文件上传接口
     * POST /minimax/v1/music_upload
     * 基于 ai-api-minimaxi.com-guidelines.mdc 官方文档实现
     */
    public function uploadMusicFile($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['purpose']);

            // 验证上传目的
            $this->validateMusicUploadPurpose($data['purpose']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateMiniMaxErrorResponse('UPLOAD_FAILED', '音乐文件上传失败，请重试');
            }

            // 生成响应
            $response = $this->generateMusicUploadResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/music_upload',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/music_upload', $e->getMessage(), $data ?? []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 文件列表查询接口
     * GET /minimax/v1/files
     */
    public function listFiles($params = [])
    {
        $startTime = microtime(true);

        try {
            // 获取查询参数
            $purpose = $_GET['purpose'] ?? null;
            $limit = intval($_GET['limit'] ?? 20);
            $after = $_GET['after'] ?? null;

            // 验证参数
            if ($limit > 100) {
                throw new Exception('limit参数不能超过100');
            }

            // 生成文件列表响应
            $response = $this->generateFileListResponse($purpose, $limit, $after);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/files',
                'GET',
                ['purpose' => $purpose, 'limit' => $limit],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/files', $e->getMessage(), []);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 视频任务查询接口
     * GET /minimax/v1/query/video_generation/{task_id}
     */
    public function getVideoTaskStatus($params = [])
    {
        $startTime = microtime(true);

        try {
            $taskId = $params['task_id'] ?? null;

            if (!$taskId) {
                return $this->generateMiniMaxErrorResponse('MISSING_TASK_ID', '缺少任务ID');
            }

            // 生成视频任务状态响应
            $response = $this->generateVideoTaskStatusResponse($taskId);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'v1/query/video_generation/' . $taskId,
                'GET',
                ['task_id' => $taskId],
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'v1/query/video_generation', $e->getMessage(), ['task_id' => $taskId ?? 'unknown']);

            return $this->generateMiniMaxErrorResponse('PROCESSING_ERROR', $e->getMessage());
        }
    }

    /**
     * 🆕 验证音乐上传目的
     */
    private function validateMusicUploadPurpose($purpose)
    {
        $validPurposes = ['song', 'voice', 'instrumental'];
        if (!in_array($purpose, $validPurposes)) {
            throw new Exception('不支持的上传目的: ' . $purpose . '。支持的目的: ' . implode(', ', $validPurposes));
        }
    }

    /**
     * 🆕 生成音乐上传响应
     */
    private function generateMusicUploadResponse($data)
    {
        $fileId = 'music_' . uniqid();

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'file_id' => $fileId,
                'purpose' => $data['purpose'],
                'filename' => 'uploaded_music_' . time() . '.mp3',
                'size' => rand(1048576, 52428800), // 1MB-50MB
                'upload_time' => date('Y-m-d H:i:s'),
                'status' => 'uploaded',
                'duration' => rand(30, 300), // 30秒-5分钟
                'format' => 'mp3',
                'sample_rate' => 44100,
                'bit_rate' => 320
            ]
        ];
    }

    /**
     * 🆕 生成文件列表响应
     */
    private function generateFileListResponse($purpose, $limit, $after)
    {
        $files = [];

        // 生成模拟文件列表
        for ($i = 0; $i < min($limit, 10); $i++) {
            $fileId = 'file_' . uniqid();
            $files[] = [
                'id' => $fileId,
                'object' => 'file',
                'bytes' => rand(1024, 10485760),
                'created_at' => time() - rand(0, 86400),
                'filename' => 'file_' . ($i + 1) . '.bin',
                'purpose' => $purpose ?? 'general',
                'status' => ['uploaded', 'processing', 'ready', 'error'][rand(0, 3)],
                'status_details' => null
            ];
        }

        return [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'object' => 'list',
            'data' => $files,
            'has_more' => count($files) === $limit,
            'first_id' => $files[0]['id'] ?? null,
            'last_id' => end($files)['id'] ?? null
        ];
    }

    /**
     * 🆕 生成视频任务状态响应
     */
    private function generateVideoTaskStatusResponse($taskId)
    {
        // 模拟任务状态
        $statuses = ['submitted', 'processing', 'completed', 'failed'];
        $weights = [10, 30, 55, 5];
        $status = $this->weightedRandom($statuses, $weights);

        $baseResponse = [
            'base_resp' => [
                'status_code' => 0,
                'status_msg' => 'success'
            ],
            'data' => [
                'task_id' => $taskId,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s', time() - rand(0, 3600)),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // 根据状态添加特定信息
        switch ($status) {
            case 'submitted':
                $baseResponse['data']['queue_position'] = rand(1, 5);
                $baseResponse['data']['estimated_time'] = rand(120, 300);
                break;

            case 'processing':
                $baseResponse['data']['progress'] = rand(10, 90);
                $baseResponse['data']['current_step'] = ['初始化', '场景分析', '视频生成', '后处理'][rand(0, 3)];
                $baseResponse['data']['estimated_time'] = rand(60, 180);
                break;

            case 'completed':
                $baseResponse['data']['progress'] = 100;
                $baseResponse['data']['video_url'] = HttpHelper::generateMockFileUrl('video', 'mp4');
                $baseResponse['data']['thumbnail_url'] = HttpHelper::generateMockFileUrl('image', 'jpg');
                $baseResponse['data']['duration'] = rand(5, 10);
                $baseResponse['data']['resolution'] = '1080p';
                $baseResponse['data']['file_size'] = rand(10, 100) . 'MB';
                break;

            case 'failed':
                $errorTypes = [
                    'CONTENT_POLICY_VIOLATION' => '内容违反使用政策',
                    'GENERATION_TIMEOUT' => '生成超时',
                    'QUOTA_EXCEEDED' => '配额已用完',
                    'INVALID_PROMPT' => '提示词无效',
                    'MODEL_OVERLOADED' => '模型负载过高'
                ];

                $errorCode = array_rand($errorTypes);
                $baseResponse['data']['error'] = [
                    'code' => $errorCode,
                    'message' => $errorTypes[$errorCode]
                ];
                break;
        }

        return $baseResponse;
    }
}
