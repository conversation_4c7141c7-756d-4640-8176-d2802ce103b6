<?php

/**
 * 测试项目状态更新功能
 * 验证 ProjectService::updateProject 方法是否正确处理 status 字段
 *
 * 使用方法：在 php/api 目录下运行
 * php artisan tinker --execute="require_once '../../test_project_status_update.php';"
 */

// 这个文件应该通过 artisan tinker 运行，所以不需要手动初始化

echo "=== 项目状态更新测试 ===\n\n";

try {
    // 1. 查找一个测试用户
    $user = User::first();
    if (!$user) {
        echo "❌ 错误：没有找到测试用户\n";
        exit(1);
    }
    
    echo "✅ 找到测试用户: ID={$user->id}, 用户名={$user->username}\n";

    // 2. 查找或创建一个测试项目
    $project = Project::where('user_id', $user->id)->first();
    
    if (!$project) {
        // 创建测试项目
        $project = Project::create([
            'user_id' => $user->id,
            'title' => '状态更新测试项目',
            'description' => '用于测试状态更新功能的项目',
            'status' => Project::STATUS_DRAFT,
            'project_config' => ['test' => true],
            'last_accessed_at' => now()
        ]);
        echo "✅ 创建测试项目: ID={$project->id}\n";
    } else {
        echo "✅ 找到测试项目: ID={$project->id}, 当前状态={$project->status}\n";
    }

    // 3. 测试状态更新
    $projectService = new ProjectService();
    
    echo "\n--- 测试状态更新 ---\n";
    echo "原始状态: {$project->status}\n";
    
    // 准备更新数据
    $updateData = [
        'title' => '状态更新测试项目 - 已更新',
        'status' => Project::STATUS_IN_PROGRESS,
        'description' => '测试状态更新功能 - 更新后'
    ];
    
    echo "尝试更新状态为: " . Project::STATUS_IN_PROGRESS . "\n";
    
    // 调用更新方法
    $result = $projectService->updateProject($project->id, $user->id, $updateData);
    
    // 4. 检查结果
    if ($result['code'] === 200) {
        echo "✅ 更新方法调用成功\n";
        
        // 重新加载项目数据
        $project->refresh();
        echo "数据库中的状态: {$project->status}\n";
        echo "数据库中的标题: {$project->title}\n";
        
        if ($project->status === Project::STATUS_IN_PROGRESS) {
            echo "🎉 状态更新成功！项目状态已更改为 'in_progress'\n";
        } else {
            echo "❌ 状态更新失败！期望: " . Project::STATUS_IN_PROGRESS . ", 实际: {$project->status}\n";
        }
        
        // 5. 测试其他状态
        echo "\n--- 测试其他状态更新 ---\n";
        $testStatuses = [
            Project::STATUS_COMPLETED,
            Project::STATUS_ARCHIVED,
            Project::STATUS_DRAFT
        ];
        
        foreach ($testStatuses as $testStatus) {
            echo "测试状态: {$testStatus}\n";
            $result = $projectService->updateProject($project->id, $user->id, ['status' => $testStatus]);
            
            if ($result['code'] === 200) {
                $project->refresh();
                if ($project->status === $testStatus) {
                    echo "  ✅ 成功更新为: {$testStatus}\n";
                } else {
                    echo "  ❌ 更新失败，期望: {$testStatus}, 实际: {$project->status}\n";
                }
            } else {
                echo "  ❌ 更新方法调用失败: {$result['message']}\n";
            }
        }
        
    } else {
        echo "❌ 更新方法调用失败\n";
        echo "错误代码: {$result['code']}\n";
        echo "错误信息: {$result['message']}\n";
        print_r($result);
    }

} catch (\Exception $e) {
    echo "❌ 测试过程中发生异常:\n";
    echo "错误信息: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
