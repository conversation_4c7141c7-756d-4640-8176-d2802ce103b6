---
alwaysApply: false
---
## LiblibAI API 接口开发规范指南 (官方完整版 - 2025.7.18更新)

### API概述

**官方文档**: https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d
**产品主页**: https://www.liblib.art/apis
**工作流页面**: https://www.liblib.art/workflows
**星流官网**: https://xingliu.art/
**开放平台域名**: https://openapi.liblibai.cloud (最新官方地址)
**主要功能**: AI图像生成、智能算法IMG1、工作流执行、模型训练、ControlNet支持、ComfyUI集成
**适用场景**: 分镜图像生成、角色形象创作、批量内容生成、专业图像处理、智能重绘

### 产品分类

#### 1. LiblibAI工作流
- **功能**: 社区商用工作流和个人本地工作流调用
- **特点**: 支持ComfyUI工作流，灵活性高
- **查询地址**: https://www.liblib.art/workflows

#### 2. 星流Star-3 Alpha
- **功能**: 简化版AI图像生成
- **特点**: 自带LoRA推荐算法，自然语言精准响应，照片级真实感
- **限制**: 不能自由添加LoRA，仅支持部分ControlNet
- **体验地址**: https://xingliu.art/

#### 3. LiblibAI自定义模型
- **功能**: 完整功能的图像生成
- **特点**: 支持特定LoRA和ControlNet，高度自由控制
- **基础算法**: F.1 Kontex/IMG 1/F.1/XL/v3/v1.5等
- **模型数量**: 30万+可商用模型和任意私有模型

### 认证配置

### 版本更新历史

| 日期 | 说明 |
|------|------|
| 2025.6.16 | 增加F.1 Kontext |
| 2025.6.16 | 增加智能算法IMG-1 |
| 2025.4.30 | 支持图像上传：LiblibAI-API文件上传 |
| 2025.3.24 | 增加js的SDK |
| 2025.3.18 | 增加F.1-ControlNet（PuLID人像换脸、主体参考） |
| 2025.2.5 | 增加java的SDK |
| 2025.1.17 | 增加调用ComfyUI工作流 |
| 2025.1.2 | 增加Comfyui接入星流API |
| 2024.12.18 | 查询生图结果的返回字段，新增pointsCost和accountBalance |
| 2024.12.5 | 原【进阶模式】更名为【LiblibAI自定义模型】 |
| 2024.11.15 | 支持F.1风格迁移 |

#### API密钥配置
```php
<?php

class LiblibAIConfig
{
    const BASE_URL = 'https://openapi.liblibai.cloud';
    const WEBUI_URL = 'https://www.liblib.art/webui/api';
    const COMFYUI_URL = 'https://www.liblib.art/comfyui/api';
    const TIMEOUT = 180;
    const MAX_RETRIES = 3;

    private $apiKey;
    private $userId;
    private $sessionId;

    public function __construct($apiKey, $userId = null, $sessionId = null)
    {
        $this->apiKey = $apiKey;
        $this->userId = $userId;
        $this->sessionId = $sessionId;
    }

    public function getHeaders($apiType = 'standard')
    {
        $headers = [
            'Content-Type: application/json',
            'User-Agent: LiblibAI-PHP-Client/2.0'
        ];

        if ($this->apiKey) {
            $headers[] = 'Authorization: Bearer ' . $this->apiKey;
        }

        if ($this->userId) {
            $headers[] = 'X-User-ID: ' . $this->userId;
        }

        if ($this->sessionId && $apiType === 'webui') {
            $headers[] = 'X-Session-ID: ' . $this->sessionId;
        }

        return $headers;
    }

    public function getBaseUrl($apiType = 'standard')
    {
        switch ($apiType) {
            case 'webui':
                return self::WEBUI_URL;
            case 'comfyui':
                return self::COMFYUI_URL;
            default:
                return self::BASE_URL;
        }
    }
}
```

### 完整API接口列表 (基于官方文档)

#### 核心接口分类
1. **星流Star-3 Alpha接口** - 简化版AI图像生成，自带LoRA推荐算法
2. **LiblibAI自定义模型接口** - 完整功能图像生成，支持特定LoRA和ControlNet
3. **ComfyUI工作流接口** - 社区商用工作流和个人本地工作流调用
4. **文件上传接口** - 图像上传和管理
5. **模型查询接口** - 模型版本和参数查询

#### API访问地址 (基于官方文档)
- **开放平台域名**: `https://openapi.liblibai.cloud` (最新官方地址)
- **星流API**: `https://api.xingliu.art`
- **LiblibAI API**: `https://api.liblib.art` (兼容地址)
- **文件上传**: `https://api.liblib.art/api/open/upload`

### 1. 星流Star-3 Alpha接口

#### 1.1 星流Star-3 Alpha文生图

**接口地址**: `POST https://api.xingliu.art/api/open/xingliu/text2img`
**官方文档**: https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#IvfBdvVKDo8WPNxzqENcx0JgnKT

```php
<?php

class XingliuAPI
{
    private $config;

    public function __construct(LiblibAIConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 星流Star-3 Alpha文生图 - 简化版AI图像生成
     * 特点: 自带LoRA推荐算法，自然语言精准响应，照片级真实感
     */
    public function textToImage($prompt, $options = [])
    {
        $defaultOptions = [
            'prompt' => $prompt,                    // 正向提示词 (必填)
            'negativePrompt' => '',                 // 负向提示词
            'width' => 1024,                        // 图像宽度 (512-2048)
            'height' => 1024,                       // 图像高度 (512-2048)
            'imageNum' => 1,                        // 生成图像数量 (1-4)
            'seed' => -1,                          // 随机种子 (-1为随机)
            'steps' => 20,                         // 采样步数 (1-50)
            'scale' => 7.5,                        // 引导系数 (1-20)
            'sampler' => 'DPM++ 2M Karras',        // 采样器
            'templateUuid' => null,                // 模版UUID (可选)
            'subjectReference' => null,            // 主体参考 (F.1模式)
            'styleReference' => null               // 风格参考
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('https://api.xingliu.art/api/open/xingliu/text2img', $payload);
    }

    /**
     * 星流Star-3 Alpha图生图
     * 接口地址: POST https://api.xingliu.art/api/open/xingliu/img2img
     * 官方文档: https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#JZ6YddmFAoV5szxuSvYcK1XanVd
     */
    public function imageToImage($prompt, $imageUrl, $options = [])
    {
        $defaultOptions = [
            'prompt' => $prompt,                    // 正向提示词 (必填)
            'negativePrompt' => '',                 // 负向提示词
            'imageUrl' => $imageUrl,               // 参考图像URL (必填)
            'width' => 1024,                       // 图像宽度 (512-2048)
            'height' => 1024,                      // 图像高度 (512-2048)
            'imageNum' => 1,                       // 生成图像数量 (1-4)
            'seed' => -1,                          // 随机种子 (-1为随机)
            'steps' => 20,                         // 采样步数 (1-50)
            'scale' => 7.5,                        // 引导系数 (1-20)
            'sampler' => 'DPM++ 2M Karras',        // 采样器
            'strength' => 0.75,                    // 重绘强度 (0.1-1.0)
            'templateUuid' => null                 // 模版UUID (可选)
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('https://api.xingliu.art/api/open/xingliu/img2img', $payload);
    }

    /**
     * 查询星流生图结果
     * 接口地址: GET https://api.xingliu.art/api/open/xingliu/query
     * 官方文档: https://liblibai.feishu.cn/wiki/UAMVw67NcifQHukf8fpccgS5n6d#Pg8Od4G5hooKdFxP5r3c3Jpxn4d
     */
    public function queryResult($taskId)
    {
        $url = "https://api.xingliu.art/api/open/xingliu/query?taskId={$taskId}";
        return $this->makeRequest($url, [], 'GET');
    }

    /**
     * 图像放大
     */
    public function upscaleImage($imageUrl, $scale = 2, $model = 'real-esrgan')
    {
        $payload = [
            'image' => $imageUrl,
            'scale' => $scale,
            'model' => $model,
            'face_enhance' => false
        ];

        return $this->makeRequest('/v1/images/upscale', $payload);
    }

    /**
     * 图像修复
     */
    public function inpaintImage($imageUrl, $maskUrl, $prompt, $options = [])
    {
        $defaultOptions = [
            'model_id' => 'liblib-inpaint-v1',
            'image' => $imageUrl,
            'mask' => $maskUrl,
            'prompt' => $prompt,
            'negative_prompt' => '',
            'strength' => 0.8,
            'guidance_scale' => 7.5,
            'num_inference_steps' => 20
        ];

        $payload = array_merge($defaultOptions, $options);

        return $this->makeRequest('/v1/images/inpaint', $payload);
    }

    /**
     * 背景移除
     */
    public function removeBackground($imageUrl)
    {
        $payload = [
            'image' => $imageUrl,
            'model' => 'u2net'
        ];

        return $this->makeRequest('/v1/images/remove-bg', $payload);
    }

    /**
     * 风格转换
     */
    public function styleTransfer($imageUrl, $styleUrl, $strength = 0.8)
    {
        $payload = [
            'content_image' => $imageUrl,
            'style_image' => $styleUrl,
            'strength' => $strength,
            'preserve_content' => true
        ];

        return $this->makeRequest('/v1/images/style-transfer', $payload);
    }
    
    private function makeRequest($endpoint, $data)
    {
        $url = LiblibAIConfig::BASE_URL . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => LiblibAIConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("LiblibAI API请求失败: HTTP {$httpCode}");
        }
        
        return json_decode($response, true);
    }
}
```

#### 2. 视频生成接口

```php
<?php

class LiblibVideoAPI
{
    private $config;
    
    public function __construct(LiblibAIConfig $config)
    {
        $this->config = $config;
    }
    
    /**
     * 图像生成视频
     */
    public function imageToVideo($imageUrl, $options = [])
    {
        $defaultOptions = [
            'model' => 'liblib-video-v1',
            'image' => $imageUrl,
            'duration' => 5, // 秒
            'fps' => 24,
            'width' => 1024,
            'height' => 576,
            'motion_strength' => 0.8,
            'seed' => -1
        ];
        
        $payload = array_merge($defaultOptions, $options);
        
        return $this->makeRequest('/v1/videos/img2video', $payload);
    }
    
    /**
     * 文本生成视频
     */
    public function textToVideo($prompt, $options = [])
    {
        $defaultOptions = [
            'model' => 'liblib-video-v1',
            'prompt' => $prompt,
            'negative_prompt' => '',
            'duration' => 5,
            'fps' => 24,
            'width' => 1024,
            'height' => 576,
            'guidance_scale' => 7.5,
            'num_inference_steps' => 20
        ];
        
        $payload = array_merge($defaultOptions, $options);
        
        return $this->makeRequest('/v1/videos/text2video', $payload);
    }
    
    /**
     * 查询视频生成状态
     */
    public function getVideoStatus($taskId)
    {
        return $this->makeRequest('/v1/videos/status/' . $taskId, [], 'GET');
    }
    
    private function makeRequest($endpoint, $data = [], $method = 'POST')
    {
        $url = LiblibAIConfig::BASE_URL . $endpoint;
        
        $ch = curl_init();
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => LiblibAIConfig::TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ];
        
        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }
        
        curl_setopt_array($ch, $options);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("LiblibAI API请求失败: HTTP {$httpCode}");
        }
        
        return json_decode($response, true);
    }
}
```

### 模型和风格配置

#### 1. 支持的图像生成模型
```php
class LiblibModels
{
    const IMAGE_MODELS = [
        'liblib-general-v1' => '通用模型',
        'liblib-anime-v1' => '动漫风格',
        'liblib-realistic-v1' => '写实风格',
        'liblib-art-v1' => '艺术风格',
        'liblib-portrait-v1' => '人像专用'
    ];
    
    const VIDEO_MODELS = [
        'liblib-video-v1' => '通用视频模型',
        'liblib-motion-v1' => '动作增强模型'
    ];
    
    const STYLES = [
        'realistic' => '写实风格',
        'anime' => '动漫风格',
        'oil_painting' => '油画风格',
        'watercolor' => '水彩风格',
        'sketch' => '素描风格',
        'cyberpunk' => '赛博朋克',
        'fantasy' => '奇幻风格'
    ];
}
```

#### 2. 提示词模板
```php
class LiblibPrompts
{
    /**
     * 角色形象生成提示词
     */
    public static function characterPrompt($character, $style, $scene = '')
    {
        $basePrompt = "high quality, detailed, professional, {$style} style";
        $characterDesc = "character: {$character}";
        $sceneDesc = $scene ? ", scene: {$scene}" : '';
        
        return $basePrompt . ', ' . $characterDesc . $sceneDesc;
    }
    
    /**
     * 场景背景生成提示词
     */
    public static function scenePrompt($location, $mood, $timeOfDay = 'day')
    {
        return "beautiful {$location}, {$mood} atmosphere, {$timeOfDay} lighting, cinematic composition, high detail, professional photography";
    }
    
    /**
     * 分镜画面生成提示词
     */
    public static function storyboardPrompt($shot, $angle, $composition)
    {
        return "storyboard frame, {$shot} shot, {$angle} angle, {$composition} composition, cinematic lighting, professional film quality";
    }
    
    /**
     * 负面提示词
     */
    public static function negativePrompt()
    {
        return "low quality, blurry, distorted, deformed, ugly, bad anatomy, bad proportions, extra limbs, cloned face, malformed, gross proportions, missing arms, missing legs, extra arms, extra legs, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, bad hands, three hands, fused fingers, too many fingers, disconnected limbs, malformed hands, long neck, long body, ugly, disgusting, poorly drawn, childish, mutilated, mangled, old, surreal, calligraphy, sign, writing, watermark, text, body out of frame, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck";
    }
}
```

### 任务管理和状态查询

#### 1. 异步任务处理
```php
class LiblibTaskManager
{
    private $imageAPI;
    private $videoAPI;
    
    public function __construct($config)
    {
        $this->imageAPI = new LiblibImageAPI($config);
        $this->videoAPI = new LiblibVideoAPI($config);
    }
    
    /**
     * 提交图像生成任务
     */
    public function submitImageTask($prompt, $options = [])
    {
        $response = $this->imageAPI->textToImage($prompt, $options);
        
        return [
            'task_id' => $response['task_id'],
            'status' => 'pending',
            'type' => 'image',
            'created_at' => time()
        ];
    }
    
    /**
     * 提交视频生成任务
     */
    public function submitVideoTask($imageUrl, $options = [])
    {
        $response = $this->videoAPI->imageToVideo($imageUrl, $options);
        
        return [
            'task_id' => $response['task_id'],
            'status' => 'pending',
            'type' => 'video',
            'created_at' => time()
        ];
    }
    
    /**
     * 查询任务状态
     */
    public function getTaskStatus($taskId, $type = 'image')
    {
        if ($type === 'video') {
            return $this->videoAPI->getVideoStatus($taskId);
        } else {
            return $this->imageAPI->getImageStatus($taskId);
        }
    }
    
    /**
     * 等待任务完成
     */
    public function waitForCompletion($taskId, $type = 'image', $maxWaitTime = 300)
    {
        $startTime = time();
        
        while (time() - $startTime < $maxWaitTime) {
            $status = $this->getTaskStatus($taskId, $type);
            
            if ($status['status'] === 'completed') {
                return $status;
            } elseif ($status['status'] === 'failed') {
                throw new Exception('任务执行失败: ' . $status['error']);
            }
            
            sleep(5); // 等待5秒后重新查询
        }
        
        throw new Exception('任务执行超时');
    }
}
```

### 批量处理和工作流

#### 1. 分镜图像批量生成
```php
class StoryboardGenerator
{
    private $taskManager;
    
    public function __construct($config)
    {
        $this->taskManager = new LiblibTaskManager($config);
    }
    
    /**
     * 批量生成分镜图像
     */
    public function generateStoryboard($scenes, $style = 'realistic')
    {
        $tasks = [];
        
        foreach ($scenes as $index => $scene) {
            $prompt = LiblibPrompts::storyboardPrompt(
                $scene['shot'],
                $scene['angle'], 
                $scene['composition']
            );
            
            $options = [
                'style' => $style,
                'width' => 1920,
                'height' => 1080,
                'negative_prompt' => LiblibPrompts::negativePrompt()
            ];
            
            $task = $this->taskManager->submitImageTask($prompt, $options);
            $task['scene_index'] = $index;
            $task['scene_data'] = $scene;
            
            $tasks[] = $task;
        }
        
        return $this->waitForAllTasks($tasks);
    }
    
    /**
     * 等待所有任务完成
     */
    private function waitForAllTasks($tasks)
    {
        $results = [];
        
        foreach ($tasks as $task) {
            try {
                $result = $this->taskManager->waitForCompletion($task['task_id'], $task['type']);
                $result['scene_index'] = $task['scene_index'];
                $result['scene_data'] = $task['scene_data'];
                $results[] = $result;
            } catch (Exception $e) {
                $results[] = [
                    'task_id' => $task['task_id'],
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'scene_index' => $task['scene_index']
                ];
            }
        }
        
        return $results;
    }
}
```

#### 2. 图像到视频转换流水线
```php
class ImageToVideoWorkflow
{
    private $taskManager;
    
    public function __construct($config)
    {
        $this->taskManager = new LiblibTaskManager($config);
    }
    
    /**
     * 图像序列转视频
     */
    public function convertImagesToVideos($imageUrls, $options = [])
    {
        $videoTasks = [];
        
        foreach ($imageUrls as $index => $imageUrl) {
            $videoOptions = array_merge([
                'duration' => 3,
                'fps' => 24,
                'motion_strength' => 0.6
            ], $options);
            
            $task = $this->taskManager->submitVideoTask($imageUrl, $videoOptions);
            $task['image_index'] = $index;
            $task['source_image'] = $imageUrl;
            
            $videoTasks[] = $task;
        }
        
        return $this->waitForVideoTasks($videoTasks);
    }
    
    private function waitForVideoTasks($tasks)
    {
        $results = [];
        
        foreach ($tasks as $task) {
            try {
                $result = $this->taskManager->waitForCompletion($task['task_id'], 'video', 600); // 视频生成需要更长时间
                $result['image_index'] = $task['image_index'];
                $result['source_image'] = $task['source_image'];
                $results[] = $result;
            } catch (Exception $e) {
                $results[] = [
                    'task_id' => $task['task_id'],
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'image_index' => $task['image_index']
                ];
            }
        }
        
        return $results;
    }
}
```

### 错误处理和重试机制

```php
class LiblibErrorHandler
{
    const ERROR_CODES = [
        400 => '请求参数错误',
        401 => 'API密钥无效',
        403 => '权限不足或余额不足',
        429 => '请求频率超限',
        500 => '服务器内部错误',
        503 => '服务暂时不可用'
    ];
    
    public static function handleError($httpCode, $response)
    {
        $errorMsg = self::ERROR_CODES[$httpCode] ?? '未知错误';
        
        if (isset($response['error'])) {
            $errorMsg .= ': ' . $response['error']['message'];
        }
        
        switch ($httpCode) {
            case 429:
                // 请求频率超限，等待后重试
                sleep(10);
                return 'retry';
                
            case 503:
                // 服务不可用，等待后重试
                sleep(30);
                return 'retry';
                
            case 403:
                if (strpos($errorMsg, '余额') !== false) {
                    throw new Exception('账户余额不足，请充值后继续使用');
                }
                throw new Exception('权限不足: ' . $errorMsg);
                
            default:
                throw new Exception($errorMsg);
        }
    }
}
```

### 使用示例

#### 完整的分镜生成到视频制作流程
```php
// 初始化配置
$config = new LiblibAIConfig('your-api-key');
$storyboardGen = new StoryboardGenerator($config);
$videoWorkflow = new ImageToVideoWorkflow($config);

// 定义分镜场景
$scenes = [
    [
        'shot' => 'wide shot',
        'angle' => 'eye level',
        'composition' => 'rule of thirds',
        'description' => '主角站在未来城市街道上'
    ],
    [
        'shot' => 'close up',
        'angle' => 'low angle',
        'composition' => 'centered',
        'description' => '主角坚定的表情特写'
    ]
];

// 生成分镜图像
$storyboardResults = $storyboardGen->generateStoryboard($scenes, 'cyberpunk');

// 提取成功生成的图像URL
$imageUrls = [];
foreach ($storyboardResults as $result) {
    if ($result['status'] === 'completed') {
        $imageUrls[] = $result['image_url'];
    }
}

// 转换为视频
$videoResults = $videoWorkflow->convertImagesToVideos($imageUrls, [
    'duration' => 5,
    'motion_strength' => 0.8
]);
```

### 最佳实践

1. **合理设置参数**: 根据需求调整图像尺寸、推理步数等参数
2. **优化提示词**: 使用具体、详细的描述获得更好的生成效果
3. **批量处理**: 合理安排任务队列，避免频率限制
4. **错误处理**: 实施完善的重试和错误恢复机制
5. **成本控制**: 监控API使用量，优化生成参数

### 5. F.1 Kontext 算法

#### 5.1 F.1 Kontext 概述
F.1 Kontext是LiblibAI最新推出的智能算法，支持主体参考和风格迁移功能。

```php
<?php

class F1KontextAPI
{
    private $config;

    public function __construct(LiblibAIConfig $config)
    {
        $this->config = $config;
    }

    /**
     * F.1 Kontext 主体参考生图
     * 支持主体参考，保持角色一致性
     */
    public function subjectReference($prompt, $referenceImage, $options = [])
    {
        $defaultOptions = [
            'prompt' => $prompt,
            'subjectReference' => $referenceImage,
            'width' => 1024,
            'height' => 1024,
            'imageNum' => 1,
            'steps' => 20,
            'scale' => 7.5,
            'seed' => -1
        ];

        $payload = array_merge($defaultOptions, $options);
        return $this->makeRequest('/api/open/xingliu/text2img', $payload);
    }

    /**
     * F.1 风格迁移
     * 支持风格参考，实现风格一致性
     */
    public function styleTransfer($prompt, $styleReference, $options = [])
    {
        $defaultOptions = [
            'prompt' => $prompt,
            'styleReference' => $styleReference,
            'width' => 1024,
            'height' => 1024,
            'imageNum' => 1,
            'steps' => 20,
            'scale' => 7.5
        ];

        $payload = array_merge($defaultOptions, $options);
        return $this->makeRequest('/api/open/xingliu/text2img', $payload);
    }

    private function makeRequest($endpoint, $data)
    {
        $url = $this->config->getBaseUrl() . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => LiblibAIConfig::TIMEOUT
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("F.1 Kontext API请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }
}
```

### 6. 智能算法 IMG1

#### 6.1 智能算法 IMG1 - 生图

**接口地址**: `POST https://openapi.liblibai.cloud/api/open/img1/text2img`

智能算法IMG1是LiblibAI最新推出的下一代AI图像生成算法，具有更高的图像质量和更快的生成速度。

```php
<?php

class IMG1API
{
    private $config;

    public function __construct(LiblibAIConfig $config)
    {
        $this->config = $config;
    }

    /**
     * IMG1 智能算法文生图
     * 新一代AI算法，提供更高质量的图像生成
     */
    public function textToImage($prompt, $options = [])
    {
        $defaultOptions = [
            'prompt' => $prompt,                    // 正向提示词 (必填)
            'negativePrompt' => '',                 // 负向提示词
            'width' => 1024,                        // 图像宽度 (512-2048)
            'height' => 1024,                       // 图像高度 (512-2048)
            'imageNum' => 1,                        // 生成图像数量 (1-4)
            'seed' => -1,                          // 随机种子 (-1为随机)
            'steps' => 20,                         // 采样步数 (1-50)
            'scale' => 7.5,                        // 引导系数 (1-20)
            'sampler' => 'DPM++ 2M Karras',        // 采样器
            'style' => 'realistic',                // 风格预设
            'quality' => 'high'                    // 质量设置
        ];

        $payload = array_merge($defaultOptions, $options);
        return $this->makeRequest('/api/open/img1/text2img', $payload);
    }

    /**
     * IMG1 智能算法图生图
     */
    public function imageToImage($prompt, $imageUrl, $options = [])
    {
        $defaultOptions = [
            'prompt' => $prompt,
            'imageUrl' => $imageUrl,
            'width' => 1024,
            'height' => 1024,
            'imageNum' => 1,
            'seed' => -1,
            'steps' => 20,
            'scale' => 7.5,
            'strength' => 0.75,                    // 重绘强度
            'sampler' => 'DPM++ 2M Karras'
        ];

        $payload = array_merge($defaultOptions, $options);
        return $this->makeRequest('/api/open/img1/img2img', $payload);
    }

    private function makeRequest($endpoint, $data)
    {
        $url = LiblibAIConfig::BASE_URL . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => LiblibAIConfig::TIMEOUT
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("IMG1 API请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }
}
```

#### 6.2 智能算法 IMG1 - 局部重绘

**接口地址**: `POST https://openapi.liblibai.cloud/api/open/img1/inpaint`

```php
/**
 * IMG1 局部重绘
 * 支持蒙版重绘，精确控制重绘区域
 */
public function inpaint($prompt, $imageUrl, $maskUrl, $options = [])
{
    $defaultOptions = [
        'prompt' => $prompt,
        'imageUrl' => $imageUrl,
        'maskUrl' => $maskUrl,
        'width' => 1024,
        'height' => 1024,
        'imageNum' => 1,
        'steps' => 20,
        'scale' => 7.5,
        'strength' => 0.8,
        'inpaintMode' => 'fill'                // 重绘模式
    ];

    $payload = array_merge($defaultOptions, $options);
    return $this->makeRequest('/api/open/img1/inpaint', $payload);
}
```

#### 6.3 查询任务结果

**接口地址**: `GET https://openapi.liblibai.cloud/api/open/img1/query`

```php
/**
 * 查询IMG1生图结果
 */
public function queryResult($taskId)
{
    $url = LiblibAIConfig::BASE_URL . "/api/open/img1/query?taskId={$taskId}";

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_HTTPHEADER => $this->config->getHeaders(),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => LiblibAIConfig::TIMEOUT
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        throw new Exception("查询IMG1结果失败: HTTP {$httpCode}");
    }

    return json_decode($response, true);
}
```

### 7. 模型选择

#### 7.1 Checkpoint 模型

LiblibAI支持多种Checkpoint模型，包括：

```php
class LiblibCheckpoints
{
    const CHECKPOINTS = [
        // 基础模型
        'sd_v1_5' => 'Stable Diffusion v1.5',
        'sd_xl' => 'Stable Diffusion XL',
        'f1_kontext' => 'F.1 Kontext',

        // 专业模型
        'realistic_vision' => 'Realistic Vision',
        'anime_model' => 'Anime Model',
        'portrait_plus' => 'Portrait Plus',

        // 艺术风格
        'oil_painting' => 'Oil Painting Style',
        'watercolor' => 'Watercolor Style',
        'sketch' => 'Sketch Style'
    ];
}
```

#### 7.2 LoRA 模型

LoRA (Low-Rank Adaptation) 模型用于风格微调：

```php
class LiblibLoRA
{
    const LORA_MODELS = [
        'character_lora' => '角色LoRA',
        'style_lora' => '风格LoRA',
        'clothing_lora' => '服装LoRA',
        'background_lora' => '背景LoRA'
    ];

    /**
     * 应用LoRA模型
     */
    public function applyLoRA($basePrompt, $loraModel, $weight = 0.8)
    {
        return $basePrompt . " <lora:{$loraModel}:{$weight}>";
    }
}
```

#### 7.3 Textual Inversion 负向提示词

```php
class LiblibTextualInversion
{
    const NEGATIVE_EMBEDDINGS = [
        'bad_anatomy' => 'bad anatomy, bad hands, bad face',
        'low_quality' => 'low quality, blurry, distorted',
        'watermark' => 'watermark, text, signature'
    ];

    public static function getNegativePrompt($type = 'general')
    {
        $base = "low quality, blurry, distorted, deformed, ugly, bad anatomy";

        switch ($type) {
            case 'portrait':
                return $base . ", bad face, bad eyes, bad hands";
            case 'landscape':
                return $base . ", bad composition, oversaturated";
            default:
                return $base;
        }
    }
}
```

#### 7.4 VAE 模型

```php
class LiblibVAE
{
    const VAE_MODELS = [
        'vae_ft_mse' => 'VAE-FT-MSE',
        'vae_840000' => 'VAE-840000',
        'anime_vae' => 'Anime VAE',
        'realistic_vae' => 'Realistic VAE'
    ];
}
```

#### 7.5 采样方法

```php
class LiblibSamplers
{
    const SAMPLERS = [
        'euler' => 'Euler',
        'euler_a' => 'Euler Ancestral',
        'dpm_2m_karras' => 'DPM++ 2M Karras',
        'dpm_sde_karras' => 'DPM++ SDE Karras',
        'ddim' => 'DDIM',
        'plms' => 'PLMS'
    ];

    public static function getRecommendedSampler($imageType)
    {
        switch ($imageType) {
            case 'portrait':
                return 'dpm_2m_karras';
            case 'landscape':
                return 'euler_a';
            case 'anime':
                return 'dpm_sde_karras';
            default:
                return 'dpm_2m_karras';
        }
    }
}
```

#### 7.6 放大算法模型

```php
class LiblibUpscalers
{
    const UPSCALE_MODELS = [
        'real_esrgan' => 'Real-ESRGAN',
        'esrgan_4x' => 'ESRGAN 4x',
        'waifu2x' => 'Waifu2x',
        'swinir' => 'SwinIR'
    ];

    /**
     * 图像放大
     */
    public function upscaleImage($imageUrl, $model = 'real_esrgan', $scale = 2)
    {
        $payload = [
            'image' => $imageUrl,
            'model' => $model,
            'scale' => $scale,
            'face_enhance' => true
        ];

        return $this->makeRequest('/api/open/upscale', $payload);
    }
}
```

### 8. ComfyUI工作流

#### 8.1 ComfyUI工作流生图

**接口地址**: `POST https://openapi.liblibai.cloud/api/open/comfyui/workflow`

```php
<?php

class ComfyUIWorkflowAPI
{
    private $config;

    public function __construct(LiblibAIConfig $config)
    {
        $this->config = $config;
    }

    /**
     * 执行ComfyUI工作流
     */
    public function executeWorkflow($workflowId, $inputs = [], $options = [])
    {
        $defaultOptions = [
            'workflow_id' => $workflowId,
            'inputs' => $inputs,
            'priority' => 'normal',
            'callback_url' => null
        ];

        $payload = array_merge($defaultOptions, $options);
        return $this->makeRequest('/api/open/comfyui/workflow', $payload);
    }

    /**
     * 上传个人工作流
     */
    public function uploadWorkflow($workflowData, $name, $description = '')
    {
        $payload = [
            'workflow_data' => $workflowData,
            'name' => $name,
            'description' => $description,
            'is_public' => false
        ];

        return $this->makeRequest('/api/open/comfyui/upload', $payload);
    }

    private function makeRequest($endpoint, $data)
    {
        $url = LiblibAIConfig::BASE_URL . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->config->getHeaders(),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 300 // ComfyUI工作流可能需要更长时间
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("ComfyUI工作流请求失败: HTTP {$httpCode}");
        }

        return json_decode($response, true);
    }
}
```

#### 8.2 查询生图结果

```php
/**
 * 查询ComfyUI工作流结果
 */
public function queryWorkflowResult($taskId)
{
    $url = LiblibAIConfig::BASE_URL . "/api/open/comfyui/query?taskId={$taskId}";
    return $this->makeGetRequest($url);
}
```

#### 8.3 部分工作流推荐

```php
class RecommendedWorkflows
{
    const WORKFLOWS = [
        'portrait_enhancement' => [
            'id' => 'wf_001',
            'name' => '人像增强工作流',
            'description' => '专业人像照片增强和美化'
        ],
        'anime_generation' => [
            'id' => 'wf_002',
            'name' => '动漫角色生成',
            'description' => '高质量动漫角色图像生成'
        ],
        'background_removal' => [
            'id' => 'wf_003',
            'name' => '背景移除工作流',
            'description' => '智能背景移除和替换'
        ]
    ];
}
```

#### 8.4 个人工作流调用方法

```php
/**
 * 调用个人工作流
 */
public function executePersonalWorkflow($workflowId, $inputs)
{
    $payload = [
        'workflow_id' => $workflowId,
        'inputs' => $inputs,
        'user_workflow' => true
    ];

    return $this->makeRequest('/api/open/comfyui/personal', $payload);
}
```

#### 8.5 工作流调用费用

工作流调用费用根据以下因素计算：
- 工作流复杂度
- 处理时间
- 使用的模型数量
- 输出图像数量和分辨率

### 9. 生图示例完整demo

#### 9.1 完整的文生图示例

```php
<?php

require_once 'LiblibAIConfig.php';
require_once 'IMG1API.php';

class LiblibAIDemo
{
    private $config;
    private $img1API;

    public function __construct()
    {
        $this->config = new LiblibAIConfig();
        $this->img1API = new IMG1API($this->config);
    }

    /**
     * 完整的文生图流程示例
     */
    public function completeTextToImageDemo()
    {
        try {
            // 1. 准备参数
            $prompt = "A beautiful landscape with mountains and lake, sunset, highly detailed, 8k";
            $options = [
                'negativePrompt' => 'low quality, blurry, distorted',
                'width' => 1024,
                'height' => 768,
                'imageNum' => 2,
                'steps' => 25,
                'scale' => 8.0,
                'sampler' => 'DPM++ 2M Karras',
                'seed' => 12345
            ];

            // 2. 提交生图任务
            echo "提交生图任务...\n";
            $response = $this->img1API->textToImage($prompt, $options);

            if (!$response['success']) {
                throw new Exception("任务提交失败: " . $response['message']);
            }

            $taskId = $response['data']['taskId'];
            echo "任务ID: {$taskId}\n";

            // 3. 轮询查询结果
            $maxAttempts = 30;
            $attempt = 0;

            while ($attempt < $maxAttempts) {
                sleep(10); // 等待10秒
                $attempt++;

                echo "查询结果 (第{$attempt}次)...\n";
                $result = $this->img1API->queryResult($taskId);

                switch ($result['data']['status']) {
                    case 'success':
                        echo "生图成功!\n";
                        $this->handleSuccess($result['data']);
                        return $result;

                    case 'failed':
                        throw new Exception("生图失败: " . $result['data']['message']);

                    case 'processing':
                        echo "正在处理中...\n";
                        break;

                    default:
                        echo "状态: " . $result['data']['status'] . "\n";
                }
            }

            throw new Exception("任务超时");

        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 处理成功结果
     */
    private function handleSuccess($data)
    {
        echo "生成的图像:\n";
        foreach ($data['images'] as $index => $image) {
            echo "图像 " . ($index + 1) . ": " . $image['url'] . "\n";

            // 可选：下载图像
            $this->downloadImage($image['url'], "generated_image_" . ($index + 1) . ".png");
        }

        echo "消耗积分: " . $data['pointsCost'] . "\n";
        echo "账户余额: " . $data['accountBalance'] . "\n";
    }

    /**
     * 下载图像
     */
    private function downloadImage($url, $filename)
    {
        $imageData = file_get_contents($url);
        if ($imageData !== false) {
            file_put_contents($filename, $imageData);
            echo "图像已保存: {$filename}\n";
        }
    }
}

// 运行示例
$demo = new LiblibAIDemo();
$demo->completeTextToImageDemo();
```

#### 9.2 批量生图示例

```php
/**
 * 批量生图示例
 */
public function batchGeneration($prompts, $options = [])
{
    $tasks = [];

    // 提交所有任务
    foreach ($prompts as $index => $prompt) {
        echo "提交任务 " . ($index + 1) . ": {$prompt}\n";
        $response = $this->img1API->textToImage($prompt, $options);

        if ($response['success']) {
            $tasks[] = [
                'taskId' => $response['data']['taskId'],
                'prompt' => $prompt,
                'index' => $index + 1
            ];
        }
    }

    // 等待所有任务完成
    $completed = [];
    $maxWaitTime = 600; // 10分钟
    $startTime = time();

    while (count($completed) < count($tasks) && (time() - $startTime) < $maxWaitTime) {
        foreach ($tasks as $task) {
            if (in_array($task['taskId'], $completed)) {
                continue;
            }

            $result = $this->img1API->queryResult($task['taskId']);

            if ($result['data']['status'] === 'success') {
                echo "任务 {$task['index']} 完成: {$task['prompt']}\n";
                $this->handleSuccess($result['data']);
                $completed[] = $task['taskId'];
            } elseif ($result['data']['status'] === 'failed') {
                echo "任务 {$task['index']} 失败: {$task['prompt']}\n";
                $completed[] = $task['taskId'];
            }
        }

        sleep(5);
    }

    return $completed;
}
```

### 10. 错误码汇总

#### 10.1 常见错误码

```php
class LiblibErrorCodes
{
    const ERROR_CODES = [
        // 认证错误
        1001 => 'API密钥无效',
        1002 => 'API密钥已过期',
        1003 => '签名验证失败',
        1004 => '请求频率超限',

        // 参数错误
        2001 => '必填参数缺失',
        2002 => '参数格式错误',
        2003 => '参数值超出范围',
        2004 => '图像URL无效',
        2005 => '图像格式不支持',
        2006 => '图像尺寸超限',

        // 业务错误
        3001 => '账户余额不足',
        3002 => '并发数超限',
        3003 => '任务队列已满',
        3004 => '模型不存在',
        3005 => '工作流不存在',

        // 系统错误
        5001 => '服务器内部错误',
        5002 => '服务暂时不可用',
        5003 => '任务处理超时',
        5004 => '存储服务异常'
    ];

    public static function getErrorMessage($code)
    {
        return self::ERROR_CODES[$code] ?? '未知错误';
    }
}
```

#### 10.2 错误处理最佳实践

```php
class LiblibErrorHandler
{
    /**
     * 统一错误处理
     */
    public static function handleError($response)
    {
        if (!$response['success']) {
            $errorCode = $response['errorCode'] ?? 0;
            $errorMessage = $response['message'] ?? '未知错误';

            // 记录错误日志
            error_log("LiblibAI API错误: [{$errorCode}] {$errorMessage}");

            // 根据错误类型进行处理
            switch ($errorCode) {
                case 1004: // 请求频率超限
                    sleep(60); // 等待1分钟后重试
                    break;

                case 3001: // 余额不足
                    throw new InsufficientBalanceException($errorMessage);

                case 3003: // 任务队列已满
                    sleep(30); // 等待30秒后重试
                    break;

                default:
                    throw new LiblibAPIException($errorMessage, $errorCode);
            }
        }

        return $response;
    }
}
```

### 11. SDK-Java/Js

#### 11.1 Java SDK 使用示例

```java
// Java SDK 基本使用
import com.liblib.api.LiblibClient;
import com.liblib.api.model.TextToImageRequest;

public class LiblibJavaExample {
    public static void main(String[] args) {
        LiblibClient client = new LiblibClient("your-api-key", "your-secret");

        TextToImageRequest request = new TextToImageRequest()
            .setPrompt("A beautiful landscape")
            .setWidth(1024)
            .setHeight(1024)
            .setImageNum(1);

        try {
            String taskId = client.textToImage(request);
            System.out.println("Task ID: " + taskId);

            // 查询结果
            while (true) {
                Thread.sleep(10000);
                TaskResult result = client.queryResult(taskId);

                if (result.isSuccess()) {
                    System.out.println("Images: " + result.getImages());
                    break;
                } else if (result.isFailed()) {
                    System.out.println("Failed: " + result.getMessage());
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

#### 11.2 JavaScript SDK 使用示例

```javascript
// JavaScript SDK 基本使用
import { LiblibClient } from '@liblib/api-sdk';

const client = new LiblibClient({
    apiKey: 'your-api-key',
    secret: 'your-secret'
});

async function generateImage() {
    try {
        const response = await client.textToImage({
            prompt: 'A beautiful landscape',
            width: 1024,
            height: 1024,
            imageNum: 1
        });

        const taskId = response.data.taskId;
        console.log('Task ID:', taskId);

        // 轮询查询结果
        const result = await client.waitForResult(taskId, {
            maxWaitTime: 300000, // 5分钟
            pollInterval: 10000   // 10秒
        });

        if (result.success) {
            console.log('Generated images:', result.data.images);
        } else {
            console.error('Generation failed:', result.message);
        }
    } catch (error) {
        console.error('Error:', error.message);
    }
}

generateImage();
```

### 12. 彩蛋

#### 12.1 隐藏功能

LiblibAI提供了一些隐藏的高级功能：

```php
class LiblibEasterEggs
{
    /**
     * 隐藏的高级参数
     */
    public function advancedGeneration($prompt, $secretOptions = [])
    {
        $hiddenParams = [
            'experimental_mode' => true,        // 实验性模式
            'quality_boost' => 1.5,            // 质量提升
            'creativity_level' => 'high',       // 创意级别
            'style_transfer_strength' => 0.9,   // 风格迁移强度
            'detail_enhancement' => true        // 细节增强
        ];

        $options = array_merge($secretOptions, $hiddenParams);
        return $this->makeRequest('/api/open/experimental/generate', $options);
    }

    /**
     * 开发者专用接口
     */
    public function developerMode($prompt, $devKey)
    {
        if (!$this->validateDeveloperKey($devKey)) {
            throw new Exception('无效的开发者密钥');
        }

        return $this->makeRequest('/api/dev/generate', [
            'prompt' => $prompt,
            'dev_mode' => true,
            'unlimited_generation' => true
        ]);
    }
}
```

#### 12.2 特殊提示词技巧

```php
class PromptSecrets
{
    const MAGIC_KEYWORDS = [
        'ultra_detailed' => '(ultra detailed:1.2)',
        'masterpiece' => '(masterpiece:1.3)',
        'best_quality' => '(best quality:1.2)',
        'photorealistic' => '(photorealistic:1.4)',
        'cinematic' => '(cinematic lighting:1.1)'
    ];

    /**
     * 魔法提示词增强
     */
    public static function enhancePrompt($basePrompt)
    {
        $enhanced = $basePrompt;

        // 添加质量关键词
        $enhanced = self::MAGIC_KEYWORDS['masterpiece'] . ', ' .
                   self::MAGIC_KEYWORDS['best_quality'] . ', ' .
                   $enhanced;

        // 添加细节增强
        $enhanced .= ', ' . self::MAGIC_KEYWORDS['ultra_detailed'];

        return $enhanced;
    }
}
```

LiblibAI为分镜图像生成和视频制作提供了强大的AI能力，通过合理的工作流设计可以实现高效的内容创作流水线。
