#!/usr/bin/env python3
"""
多AI程序员协同开发 - 自动化测试和质量保证系统
Automated Testing and Quality Assurance System for Multi-AI Development
"""

import os
import json
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import re

@dataclass
class QualityReport:
    """质量报告数据类"""
    timestamp: datetime
    ai_id: str
    branch: str
    files_changed: List[str]
    test_results: Dict
    code_quality: Dict
    security_scan: Dict
    performance_metrics: Dict
    overall_score: float
    passed: bool

class AIQualityAssurance:
    """AI质量保证系统"""
    
    def __init__(self, repo_path: str):
        self.repo_path = Path(repo_path)
        self.config_file = self.repo_path / "qa_config.json"
        self.reports_dir = self.repo_path / "qa_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        default_config = {
            "quality_gates": {
                "min_test_coverage": 80,
                "max_complexity": 10,
                "max_duplicated_lines": 5,
                "min_maintainability": 70,
                "max_security_issues": 0
            },
            "test_commands": {
                "python": [
                    "python -m pytest --cov=. --cov-report=json",
                    "python -m flake8 --format=json",
                    "python -m bandit -r . -f json"
                ],
                "php": [
                    "vendor/bin/phpunit --coverage-clover coverage.xml",
                    "vendor/bin/phpcs --report=json",
                    "vendor/bin/psalm --output-format=json"
                ],
                "javascript": [
                    "npm test -- --coverage --json",
                    "npx eslint . --format json",
                    "npm audit --json"
                ]
            },
            "performance_thresholds": {
                "max_response_time": 200,  # ms
                "max_memory_usage": 100,   # MB
                "min_throughput": 1000     # requests/sec
            },
            "notification_webhook": "",
            "auto_fix_enabled": True
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """保存配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def run_quality_check(self, ai_id: str, branch: str) -> QualityReport:
        """运行完整的质量检查"""
        print(f"开始质量检查: AI={ai_id}, Branch={branch}")
        
        # 获取变更文件
        changed_files = self.get_changed_files(branch)
        
        # 运行各项检查
        test_results = self.run_tests(changed_files)
        code_quality = self.analyze_code_quality(changed_files)
        security_scan = self.run_security_scan(changed_files)
        performance_metrics = self.measure_performance(changed_files)
        
        # 计算总分
        overall_score = self.calculate_overall_score(
            test_results, code_quality, security_scan, performance_metrics
        )
        
        # 判断是否通过
        passed = self.evaluate_quality_gates(
            test_results, code_quality, security_scan, performance_metrics
        )
        
        report = QualityReport(
            timestamp=datetime.now(),
            ai_id=ai_id,
            branch=branch,
            files_changed=changed_files,
            test_results=test_results,
            code_quality=code_quality,
            security_scan=security_scan,
            performance_metrics=performance_metrics,
            overall_score=overall_score,
            passed=passed
        )
        
        self.save_report(report)
        return report
    
    def get_changed_files(self, branch: str) -> List[str]:
        """获取分支中的变更文件"""
        try:
            result = subprocess.run(
                ['git', 'diff', '--name-only', 'origin/develop', f'origin/{branch}'],
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                check=True
            )
            return [f.strip() for f in result.stdout.split('\n') if f.strip()]
        except subprocess.CalledProcessError:
            return []
    
    def run_tests(self, files: List[str]) -> Dict:
        """运行测试"""
        results = {
            "unit_tests": {"passed": 0, "failed": 0, "coverage": 0},
            "integration_tests": {"passed": 0, "failed": 0},
            "syntax_check": {"errors": [], "warnings": []},
            "execution_time": 0
        }
        
        start_time = time.time()
        
        # 检测项目类型
        project_type = self.detect_project_type(files)
        test_commands = self.config["test_commands"].get(project_type, [])
        
        for command in test_commands:
            try:
                result = subprocess.run(
                    command.split(),
                    cwd=self.repo_path,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )
                
                # 解析测试结果
                if "pytest" in command:
                    self.parse_pytest_results(result.stdout, results)
                elif "phpunit" in command:
                    self.parse_phpunit_results(result.stdout, results)
                elif "npm test" in command:
                    self.parse_jest_results(result.stdout, results)
                
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                results["syntax_check"]["errors"].append(f"Command failed: {command}")
        
        results["execution_time"] = time.time() - start_time
        return results
    
    def analyze_code_quality(self, files: List[str]) -> Dict:
        """分析代码质量"""
        quality_metrics = {
            "complexity": 0,
            "maintainability": 100,
            "duplicated_lines": 0,
            "code_smells": 0,
            "technical_debt": 0,
            "lines_of_code": 0
        }
        
        for file_path in files:
            full_path = self.repo_path / file_path
            if full_path.exists() and full_path.is_file():
                file_metrics = self.analyze_file_quality(full_path)
                
                # 累加指标
                quality_metrics["complexity"] = max(quality_metrics["complexity"], file_metrics.get("complexity", 0))
                quality_metrics["duplicated_lines"] += file_metrics.get("duplicated_lines", 0)
                quality_metrics["code_smells"] += file_metrics.get("code_smells", 0)
                quality_metrics["lines_of_code"] += file_metrics.get("lines_of_code", 0)
        
        # 计算可维护性指数
        if quality_metrics["lines_of_code"] > 0:
            quality_metrics["maintainability"] = max(0, 100 - 
                (quality_metrics["complexity"] * 2) - 
                (quality_metrics["code_smells"] * 5) - 
                (quality_metrics["duplicated_lines"] / quality_metrics["lines_of_code"] * 100)
            )
        
        return quality_metrics
    
    def analyze_file_quality(self, file_path: Path) -> Dict:
        """分析单个文件的质量"""
        metrics = {
            "complexity": 0,
            "duplicated_lines": 0,
            "code_smells": 0,
            "lines_of_code": 0
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                metrics["lines_of_code"] = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
                
                # 计算圈复杂度
                metrics["complexity"] = self.calculate_complexity(content)
                
                # 检测重复代码
                metrics["duplicated_lines"] = self.detect_duplicated_lines(lines)
                
                # 检测代码异味
                metrics["code_smells"] = self.detect_code_smells(content)
        
        except Exception as e:
            print(f"分析文件失败 {file_path}: {e}")
        
        return metrics
    
    def calculate_complexity(self, content: str) -> int:
        """计算圈复杂度"""
        complexity = 1  # 基础复杂度
        
        # 控制流关键字
        control_keywords = ['if', 'elif', 'else', 'for', 'while', 'try', 'except', 'finally', 'with']
        
        for keyword in control_keywords:
            complexity += len(re.findall(rf'\b{keyword}\b', content))
        
        # 逻辑操作符
        complexity += len(re.findall(r'\band\b|\bor\b|&&|\|\|', content))
        
        return complexity
    
    def detect_duplicated_lines(self, lines: List[str]) -> int:
        """检测重复行"""
        line_counts = {}
        duplicated = 0
        
        for line in lines:
            stripped = line.strip()
            if len(stripped) > 10:  # 忽略短行
                line_counts[stripped] = line_counts.get(stripped, 0) + 1
        
        for count in line_counts.values():
            if count > 1:
                duplicated += count - 1
        
        return duplicated
    
    def detect_code_smells(self, content: str) -> int:
        """检测代码异味"""
        smells = 0
        
        # 长方法（超过50行）
        methods = re.findall(r'def\s+\w+.*?(?=def|\Z)', content, re.DOTALL)
        for method in methods:
            if len(method.split('\n')) > 50:
                smells += 1
        
        # 长参数列表（超过5个参数）
        smells += len(re.findall(r'def\s+\w+\([^)]*,.*?,.*?,.*?,.*?,', content))
        
        # 深度嵌套（超过4层）
        lines = content.split('\n')
        for line in lines:
            indent_level = (len(line) - len(line.lstrip())) // 4
            if indent_level > 4:
                smells += 1
        
        return smells
    
    def run_security_scan(self, files: List[str]) -> Dict:
        """运行安全扫描"""
        security_results = {
            "vulnerabilities": [],
            "severity_counts": {"high": 0, "medium": 0, "low": 0},
            "total_issues": 0
        }
        
        # 这里可以集成各种安全扫描工具
        # 如 bandit (Python), ESLint security plugin (JS), PHPCS security (PHP)
        
        for file_path in files:
            full_path = self.repo_path / file_path
            if full_path.exists():
                file_vulnerabilities = self.scan_file_security(full_path)
                security_results["vulnerabilities"].extend(file_vulnerabilities)
        
        # 统计严重程度
        for vuln in security_results["vulnerabilities"]:
            severity = vuln.get("severity", "low")
            security_results["severity_counts"][severity] += 1
        
        security_results["total_issues"] = len(security_results["vulnerabilities"])
        
        return security_results
    
    def scan_file_security(self, file_path: Path) -> List[Dict]:
        """扫描单个文件的安全问题"""
        vulnerabilities = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检测常见安全问题
                if 'eval(' in content:
                    vulnerabilities.append({
                        "type": "dangerous_function",
                        "description": "使用了危险的eval()函数",
                        "severity": "high",
                        "file": str(file_path)
                    })
                
                if re.search(r'password\s*=\s*["\'][^"\']+["\']', content, re.IGNORECASE):
                    vulnerabilities.append({
                        "type": "hardcoded_password",
                        "description": "硬编码密码",
                        "severity": "high",
                        "file": str(file_path)
                    })
                
                if 'SELECT * FROM' in content.upper():
                    vulnerabilities.append({
                        "type": "sql_injection_risk",
                        "description": "可能存在SQL注入风险",
                        "severity": "medium",
                        "file": str(file_path)
                    })
        
        except Exception as e:
            print(f"安全扫描失败 {file_path}: {e}")
        
        return vulnerabilities
    
    def measure_performance(self, files: List[str]) -> Dict:
        """测量性能指标"""
        performance = {
            "response_time": 0,
            "memory_usage": 0,
            "cpu_usage": 0,
            "throughput": 0,
            "bottlenecks": []
        }
        
        # 这里可以集成性能测试工具
        # 如 pytest-benchmark, Apache Bench, JMeter等
        
        return performance
    
    def calculate_overall_score(self, test_results: Dict, code_quality: Dict, 
                              security_scan: Dict, performance_metrics: Dict) -> float:
        """计算总体质量分数"""
        score = 100.0
        
        # 测试覆盖率权重 (30%)
        coverage = test_results.get("unit_tests", {}).get("coverage", 0)
        score *= (coverage / 100) * 0.3 + 0.7
        
        # 代码质量权重 (40%)
        maintainability = code_quality.get("maintainability", 100)
        score *= (maintainability / 100) * 0.4 + 0.6
        
        # 安全性权重 (20%)
        security_issues = security_scan.get("total_issues", 0)
        security_score = max(0, 100 - security_issues * 10) / 100
        score *= security_score * 0.2 + 0.8
        
        # 性能权重 (10%)
        # 这里可以根据实际性能指标计算
        score *= 0.9 + 0.1
        
        return min(100.0, max(0.0, score))
    
    def evaluate_quality_gates(self, test_results: Dict, code_quality: Dict, 
                             security_scan: Dict, performance_metrics: Dict) -> bool:
        """评估质量门禁"""
        gates = self.config["quality_gates"]
        
        # 检查测试覆盖率
        coverage = test_results.get("unit_tests", {}).get("coverage", 0)
        if coverage < gates["min_test_coverage"]:
            return False
        
        # 检查复杂度
        if code_quality.get("complexity", 0) > gates["max_complexity"]:
            return False
        
        # 检查重复代码
        if code_quality.get("duplicated_lines", 0) > gates["max_duplicated_lines"]:
            return False
        
        # 检查可维护性
        if code_quality.get("maintainability", 100) < gates["min_maintainability"]:
            return False
        
        # 检查安全问题
        if security_scan.get("total_issues", 0) > gates["max_security_issues"]:
            return False
        
        return True
    
    def detect_project_type(self, files: List[str]) -> str:
        """检测项目类型"""
        extensions = [Path(f).suffix for f in files]
        
        if '.py' in extensions:
            return 'python'
        elif '.php' in extensions:
            return 'php'
        elif '.js' in extensions or '.ts' in extensions:
            return 'javascript'
        else:
            return 'unknown'
    
    def parse_pytest_results(self, output: str, results: Dict):
        """解析pytest结果"""
        # 这里需要实际解析pytest的JSON输出
        pass
    
    def parse_phpunit_results(self, output: str, results: Dict):
        """解析PHPUnit结果"""
        # 这里需要实际解析PHPUnit的XML输出
        pass
    
    def parse_jest_results(self, output: str, results: Dict):
        """解析Jest结果"""
        # 这里需要实际解析Jest的JSON输出
        pass
    
    def save_report(self, report: QualityReport):
        """保存质量报告"""
        report_file = self.reports_dir / f"qa_report_{report.ai_id}_{report.timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            "timestamp": report.timestamp.isoformat(),
            "ai_id": report.ai_id,
            "branch": report.branch,
            "files_changed": report.files_changed,
            "test_results": report.test_results,
            "code_quality": report.code_quality,
            "security_scan": report.security_scan,
            "performance_metrics": report.performance_metrics,
            "overall_score": report.overall_score,
            "passed": report.passed
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI质量保证系统")
    parser.add_argument("--repo", default=".", help="仓库路径")
    parser.add_argument("--ai-id", required=True, help="AI标识")
    parser.add_argument("--branch", required=True, help="分支名称")
    
    args = parser.parse_args()
    
    qa_system = AIQualityAssurance(args.repo)
    report = qa_system.run_quality_check(args.ai_id, args.branch)
    
    print(f"质量检查完成:")
    print(f"  总分: {report.overall_score:.1f}")
    print(f"  通过: {'是' if report.passed else '否'}")
    print(f"  测试覆盖率: {report.test_results.get('unit_tests', {}).get('coverage', 0)}%")
    print(f"  安全问题: {report.security_scan.get('total_issues', 0)}个")
    print(f"  代码质量: {report.code_quality.get('maintainability', 0):.1f}")

if __name__ == "__main__":
    main()
