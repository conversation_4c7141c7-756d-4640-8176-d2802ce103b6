---
description: PHP 8.1.29版本开发项目规范指南
globs: ["**/*.php", "**/composer.json", "**/composer.lock", "**/*.ini", "**/.env*"]
alwaysApply: true
---

## PHP 8.1.29 开发项目规范指南

### 版本信息

- **PHP版本**: 8.1.29
- **发布日期**: 2024年6月6日
- **支持状态**: 安全修复支持至2025年11月25日
- **官方文档**: https://www.php.net/manual/zh/
- **变更日志**: https://www.php.net/ChangeLog-8.php#8.1.29

### 核心语言特性

#### 1. 枚举 (Enumerations)
```php
// 纯枚举
enum Status
{
    case PENDING;
    case RUNNING;
    case COMPLETED;
}

// 支持值的枚举
enum StatusCode: int
{
    case PENDING = 1;
    case RUNNING = 2;
    case COMPLETED = 3;
    
    public function label(): string
    {
        return match($this) {
            self::PENDING => '待处理',
            self::RUNNING => '运行中',
            self::COMPLETED => '已完成',
        };
    }
}
```

#### 2. 只读属性 (Readonly Properties)
```php
class User
{
    public function __construct(
        public readonly string $name,
        public readonly int $id,
    ) {}
}

// 使用
$user = new User('张三', 123);
// $user->name = '李四'; // 错误：不能修改只读属性
```

#### 3. 纤程 (Fibers)
```php
$fiber = new Fiber(function (): void {
    $value = Fiber::suspend('fiber');
    echo "Value used to resume fiber: ", $value, PHP_EOL;
});

$value = $fiber->start();
echo "Value from fiber suspending: ", $value, PHP_EOL;
$fiber->resume('test');
```

#### 4. 新的初始化器
```php
class Service
{
    // 新的初始化器语法
    private Logger $logger;
    
    public function __construct()
    {
        $this->logger = new Logger();
    }
}
```

### 类型系统增强

#### 1. 联合类型改进
```php
function process(string|int|null $value): string|null
{
    if ($value === null) {
        return null;
    }
    
    return (string) $value;
}
```

#### 2. 交集类型
```php
interface Loggable
{
    public function log(): void;
}

interface Cacheable
{
    public function cache(): void;
}

// 交集类型
function handle(Loggable&Cacheable $object): void
{
    $object->log();
    $object->cache();
}
```

#### 3. never 返回类型
```php
function redirect(string $url): never
{
    header('Location: ' . $url);
    exit;
}

function throwException(): never
{
    throw new Exception('Always throws');
}
```

### 新增函数和特性

#### 1. 数组函数
```php
// array_is_list() - 检查数组是否为列表
$list = [1, 2, 3];
$map = ['a' => 1, 'b' => 2];

var_dump(array_is_list($list)); // true
var_dump(array_is_list($map));  // false
```

#### 2. 字符串函数
```php
// str_contains() - 检查字符串是否包含子字符串
if (str_contains($haystack, $needle)) {
    // 包含
}

// str_starts_with() - 检查字符串是否以指定字符串开头
if (str_starts_with($string, 'prefix')) {
    // 以prefix开头
}

// str_ends_with() - 检查字符串是否以指定字符串结尾
if (str_ends_with($string, 'suffix')) {
    // 以suffix结尾
}
```

#### 3. 新的属性
```php
// #[ReturnTypeWillChange] 属性
class MyClass
{
    #[ReturnTypeWillChange]
    public function jsonSerialize()
    {
        return [];
    }
}
```

### 性能优化

#### 1. JIT 编译器
```ini
; php.ini 配置
opcache.enable=1
opcache.jit_buffer_size=256M
opcache.jit=1255
```

#### 2. 预加载优化
```php
// preload.php
opcache_compile_file('/path/to/class1.php');
opcache_compile_file('/path/to/class2.php');
```

### 安全增强

#### 1. 密码哈希
```php
// 使用推荐的密码哈希
$hash = password_hash($password, PASSWORD_ARGON2ID, [
    'memory_cost' => 65536,
    'time_cost' => 4,
    'threads' => 3
]);
```

#### 2. 随机数生成
```php
// 安全的随机数生成
$randomBytes = random_bytes(32);
$randomInt = random_int(1, 100);
```

### 错误处理

#### 1. 异常处理最佳实践
```php
try {
    // 可能抛出异常的代码
    $result = riskyOperation();
} catch (SpecificException $e) {
    // 处理特定异常
    logger()->error('Specific error: ' . $e->getMessage());
    throw $e; // 重新抛出如果需要
} catch (Throwable $e) {
    // 处理所有其他异常
    logger()->error('Unexpected error: ' . $e->getMessage());
    return null;
} finally {
    // 清理代码
    cleanup();
}
```

#### 2. 错误报告配置
```ini
; 开发环境
error_reporting=E_ALL
display_errors=On
log_errors=On

; 生产环境
error_reporting=E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors=Off
log_errors=On
```

### 数据库操作

#### 1. PDO 最佳实践
```php
class DatabaseConnection
{
    private PDO $pdo;
    
    public function __construct(string $dsn, string $username, string $password)
    {
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $this->pdo = new PDO($dsn, $username, $password, $options);
    }
    
    public function query(string $sql, array $params = []): array
    {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}
```

#### 2. 事务处理
```php
public function transferMoney(int $fromId, int $toId, float $amount): void
{
    $this->pdo->beginTransaction();
    
    try {
        $this->debitAccount($fromId, $amount);
        $this->creditAccount($toId, $amount);
        $this->pdo->commit();
    } catch (Exception $e) {
        $this->pdo->rollBack();
        throw $e;
    }
}
```

### 代码质量标准

#### 1. 类型声明
```php
// 严格类型模式
declare(strict_types=1);

class UserService
{
    public function createUser(
        string $name,
        string $email,
        int $age,
        ?string $phone = null
    ): User {
        // 实现
    }
}
```

#### 2. 文档注释
```php
/**
 * 用户服务类
 * 
 * @package App\Services
 * <AUTHOR> Name <<EMAIL>>
 * @since 1.0.0
 */
class UserService
{
    /**
     * 创建新用户
     * 
     * @param string $name 用户姓名
     * @param string $email 用户邮箱
     * @param int $age 用户年龄
     * @param string|null $phone 用户电话（可选）
     * 
     * @return User 创建的用户对象
     * 
     * @throws InvalidArgumentException 当参数无效时
     * @throws DatabaseException 当数据库操作失败时
     */
    public function createUser(
        string $name,
        string $email,
        int $age,
        ?string $phone = null
    ): User {
        // 实现
    }
}
```

### 配置管理

#### 1. 环境配置
```ini
; .env 文件
APP_ENV=production
APP_DEBUG=false
APP_KEY=your-secret-key

DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=null
```

#### 2. PHP 配置优化
```ini
; php.ini 生产环境配置
memory_limit=256M
max_execution_time=30
max_input_time=60
post_max_size=32M
upload_max_filesize=32M
max_file_uploads=20

; OPcache 配置
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.validate_timestamps=0
opcache.save_comments=0
opcache.fast_shutdown=1
```

### 安全最佳实践

#### 1. 输入验证
```php
function validateEmail(string $email): bool
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function sanitizeString(string $input): string
{
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}
```

#### 2. SQL 注入防护
```php
// 正确：使用预处理语句
$stmt = $pdo->prepare('SELECT * FROM users WHERE email = ?');
$stmt->execute([$email]);

// 错误：直接拼接SQL
// $sql = "SELECT * FROM users WHERE email = '$email'"; // 危险！
```

#### 3. XSS 防护
```php
// 输出时转义
echo htmlspecialchars($userInput, ENT_QUOTES, 'UTF-8');

// 或使用模板引擎的自动转义功能
```

### 测试规范

#### 1. 单元测试
```php
use PHPUnit\Framework\TestCase;

class UserServiceTest extends TestCase
{
    private UserService $userService;
    
    protected function setUp(): void
    {
        $this->userService = new UserService();
    }
    
    public function testCreateUser(): void
    {
        $user = $this->userService->createUser('张三', '<EMAIL>', 25);
        
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('张三', $user->getName());
        $this->assertEquals('<EMAIL>', $user->getEmail());
    }
    
    public function testCreateUserWithInvalidEmail(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->userService->createUser('张三', 'invalid-email', 25);
    }
}
```

### 部署和运维

#### 1. Composer 依赖管理
```json
{
    "require": {
        "php": "^8.1.29",
        "ext-pdo": "*",
        "ext-json": "*",
        "monolog/monolog": "^3.0"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.0",
        "phpstan/phpstan": "^1.0"
    },
    "autoload": {
        "psr-4": {
            "App\\": "src/"
        }
    },
    "config": {
        "optimize-autoloader": true,
        "classmap-authoritative": true
    }
}
```

#### 2. 日志记录
```php
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;

$logger = new Logger('app');
$logger->pushHandler(new RotatingFileHandler('/var/log/app.log', 0, Logger::INFO));

// 使用
$logger->info('User created', ['user_id' => $userId]);
$logger->error('Database connection failed', ['error' => $e->getMessage()]);
```

### 废弃功能警告

#### 1. PHP 8.1 中废弃的功能
- `FILTER_SANITIZE_STRING` 过滤器已废弃
- 动态属性创建已废弃（除非类实现 `__get()` 和 `__set()`）
- `auto_detect_line_endings` ini 设置已废弃
- 某些内部函数的可选参数传递 null 已废弃

#### 2. 迁移建议
```php
// 废弃：动态属性
class User
{
    // 应该明确声明属性
    public string $name;
    public string $email;
}

// 或者实现魔术方法
class DynamicUser
{
    private array $data = [];
    
    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
    
    public function __set(string $name, $value): void
    {
        $this->data[$name] = $value;
    }
}
```

### 性能监控

#### 1. 性能分析
```php
// 使用 Xdebug 或 Blackfire 进行性能分析
// 监控关键指标：内存使用、执行时间、数据库查询次数

class PerformanceMonitor
{
    private float $startTime;
    private int $startMemory;
    
    public function start(): void
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
    }
    
    public function end(): array
    {
        return [
            'execution_time' => microtime(true) - $this->startTime,
            'memory_usage' => memory_get_usage(true) - $this->startMemory,
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }
}
```

### 总结

PHP 8.1.29 提供了强大的新特性和性能改进，包括枚举、只读属性、纤程等。在开发时应：

1. 充分利用新的类型系统特性
2. 遵循安全编码实践
3. 使用现代化的错误处理
4. 实施全面的测试策略
5. 优化性能和配置
6. 及时处理废弃功能的迁移

遵循这些规范将确保代码的质量、安全性和可维护性。
