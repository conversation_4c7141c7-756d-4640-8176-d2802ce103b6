# Py视频创作工具 - 角色创建罩层调用规范

## 📋 概述

本文档定义了Py视频创作工具中角色创建罩层的统一调用标准和参数规范。所有需要角色创建功能的业务流程都应该遵循此规范，确保用户体验的一致性和代码的可维护性。

## 🎯 设计原则

### 核心原则
- **统一性**: 所有调用方使用相同的接口和参数格式
- **灵活性**: 支持多种业务场景的参数化配置
- **一致性**: 与Py视频创作工具现有架构保持一致
- **可扩展性**: 便于后续功能扩展和参数添加

### UI/UX原则
- **统一UI处理**: 所有UI交互由"Py视频创作工具前端"统一处理
- **罩层模式**: 使用弹出罩层，不影响原页面状态
- **进度反馈**: 提供实时的创建进度和状态反馈
- **错误友好**: 清晰的错误提示和处理机制

## 🔧 调用接口规范

### 主调用接口

```javascript
/**
 * 角色创建罩层主调用接口
 * @param {Object} options - 配置选项
 * @returns {Promise<CharacterResult>} - 返回Promise，包含创建结果
 */
PyVideoTool.CharacterCreationModal.open(options)
```

### 快捷调用接口

```javascript
// 全角色列表模式
PyVideoTool.CharacterCreationModal.openFull(callback)

// 条件筛选模式
PyVideoTool.CharacterCreationModal.openFiltered(filters, callback)

// 快速创建模式
PyVideoTool.CharacterCreationModal.openQuick(presets, callback)

// 高级创建模式
PyVideoTool.CharacterCreationModal.openAdvanced(config, callback)
```

## 📋 参数配置规范

### 完整参数结构

```javascript
const options = {
    // 基础配置
    mode: 'full|filtered|quick|advanced',
    title: '创建角色',  // 罩层标题
    
    // 筛选条件
    filters: {
        projectType: 'video|animation|story|...',
        styles: ['写实风3.0', '动漫可爱3.0', ...],
        permissions: ['create', 'publish', 'share'],
        tags: ['男性', '女性', '动物', ...],
        maxCount: 10,
        minCount: 1
    },
    
    // 显示配置
    display: {
        showStyleSelection: true,
        showModeSelection: true,
        showAdvancedOptions: false,
        showPreview: true,
        enableBatchCreate: false
    },
    
    // 功能配置
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enableStyleCustomization: true,
        enablePublish: false,
        enableSave: true
    },
    
    // 预设配置（快速创建模式）
    presets: {
        style: '写实风3.0',
        mode: 'ai_created',
        platform: 'LiblibAI',
        quickSettings: { ... }
    },
    
    // 回调函数
    callbacks: {
        onSuccess: (character) => { /* 成功回调 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onProgress: (progress) => { /* 进度回调 */ },
        onValidate: (data) => { /* 验证回调 */ }
    },
    
    // 高级配置
    advanced: {
        timeout: 300000,  // 超时时间(毫秒)
        retryCount: 3,    // 重试次数
        cacheEnabled: true,
        debugMode: false
    }
};
```

### 参数详细说明

#### mode 参数
- **full**: 显示所有可用角色，无筛选条件
- **filtered**: 根据filters参数筛选显示
- **quick**: 使用预设配置，简化创建流程
- **advanced**: 提供完整的角色创建功能

#### filters 参数
- **projectType**: 项目类型筛选
- **styles**: 角色风格筛选（数组）
- **permissions**: 用户权限筛选
- **tags**: 角色标签筛选
- **maxCount/minCount**: 选择数量限制

#### display 参数
- **showStyleSelection**: 是否显示风格选择
- **showModeSelection**: 是否显示创建模式选择
- **showAdvancedOptions**: 是否显示高级选项
- **showPreview**: 是否显示预览功能
- **enableBatchCreate**: 是否启用批量创建

## 📤 返回结果规范

### 成功结果格式

```javascript
{
    success: true,
    data: {
        character: {
            id: 'char_123456789',
            name: '角色名称',
            description: '角色描述',
            style: '写实风3.0',
            mode: 'ai_created|user_owned',
            imageUrl: 'https://example.com/character.jpg',
            thumbnailUrl: 'https://example.com/thumb.jpg',
            metadata: {
                platform: 'LiblibAI',
                createdAt: '2024-01-01T00:00:00Z',
                fileSize: 1024000,
                dimensions: { width: 1024, height: 1024 },
                tags: ['男性', '现代'],
                permissions: ['use', 'modify']
            }
        },
        usage: {
            pointsUsed: 10,
            timeSpent: 45000,
            platform: 'LiblibAI'
        }
    },
    message: '角色创建成功',
    timestamp: '2024-01-01T00:00:00Z'
}
```

### 失败结果格式

```javascript
{
    success: false,
    error: {
        code: 'AUTH_FAILED|INSUFFICIENT_POINTS|CREATION_FAILED|USER_CANCELLED|TIMEOUT|VALIDATION_ERROR',
        message: '详细错误信息',
        details: {
            reason: '具体失败原因',
            suggestion: '建议的解决方案',
            retryable: true|false
        }
    },
    timestamp: '2024-01-01T00:00:00Z'
}
```

### 进度回调格式

```javascript
{
    progress: 30,  // 进度百分比 0-100
    stage: 'processing',  // 当前阶段
    message: '正在处理角色图片',  // 进度描述
    details: {
        currentStep: 3,
        totalSteps: 10,
        estimatedTime: 30000  // 预估剩余时间(毫秒)
    }
}
```

## 🎭 事件监听机制

### 事件类型

```javascript
// 监听罩层事件
PyVideoTool.CharacterCreationModal.on('event_name', callback);

// 事件类型列表
const events = {
    'modal.open': '罩层打开',
    'modal.close': '罩层关闭',
    'creation.start': '开始创建',
    'creation.progress': '创建进度更新',
    'creation.success': '创建成功',
    'creation.error': '创建失败',
    'validation.error': '验证失败',
    'user.cancel': '用户取消'
};
```

### 事件使用示例

```javascript
// 监听创建进度
PyVideoTool.CharacterCreationModal.on('creation.progress', (progress) => {
    console.log(`创建进度: ${progress.progress}% - ${progress.message}`);
});

// 监听创建成功
PyVideoTool.CharacterCreationModal.on('creation.success', (result) => {
    console.log('角色创建成功:', result.data.character);
});
```

## ⚠️ 错误处理规范

### 错误代码定义

| 错误代码 | 描述 | 处理建议 |
|---------|------|---------|
| AUTH_FAILED | Token验证失败 | 重新登录 |
| INSUFFICIENT_POINTS | 积分不足 | 充值积分 |
| CREATION_FAILED | 创建失败 | 检查参数，重试 |
| USER_CANCELLED | 用户取消 | 正常流程，无需处理 |
| TIMEOUT | 请求超时 | 检查网络，重试 |
| VALIDATION_ERROR | 参数验证失败 | 检查参数格式 |
| NETWORK_ERROR | 网络错误 | 检查网络连接 |
| SERVER_ERROR | 服务器错误 | 稍后重试 |

### 错误处理最佳实践

```javascript
PyVideoTool.CharacterCreationModal.open({
    mode: 'filtered',
    filters: { projectType: 'video' },
    callbacks: {
        onError: (error) => {
            switch(error.code) {
                case 'AUTH_FAILED':
                    // 跳转到登录页面
                    PyVideoTool.Auth.showLogin();
                    break;
                case 'INSUFFICIENT_POINTS':
                    // 显示充值提示
                    PyVideoTool.Points.showRecharge();
                    break;
                case 'CREATION_FAILED':
                    // 显示重试选项
                    if (error.details.retryable) {
                        showRetryDialog();
                    }
                    break;
                default:
                    // 显示通用错误提示
                    showErrorMessage(error.message);
            }
        }
    }
});
```

## 🚀 性能优化策略

### 缓存策略
- **角色风格列表**: 缓存24小时
- **用户偏好设置**: 本地存储
- **平台状态信息**: 缓存1小时
- **创建历史记录**: 本地存储最近10条

### 加载优化
- **懒加载**: 罩层内容按需加载
- **预加载**: 常用资源提前加载
- **压缩**: 图片和资源压缩
- **CDN**: 使用CDN加速资源加载

### 内存管理
- **及时清理**: 罩层关闭后清理资源
- **事件解绑**: 自动解绑事件监听器
- **WebSocket管理**: 合理管理连接生命周期

## 📚 兼容性要求

### Py视频创作工具版本兼容性
- **最低版本**: v2.0.0
- **推荐版本**: v2.5.0+
- **向后兼容**: 支持旧版本参数格式

### 浏览器兼容性
- **Chrome**: 80+
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

### 设备兼容性
- **桌面端**: 完整功能支持
- **平板端**: 响应式适配
- **移动端**: 基础功能支持

## 📖 使用示例

### 基础使用

```javascript
// 最简单的调用
PyVideoTool.CharacterCreationModal.open({
    mode: 'full',
    callbacks: {
        onSuccess: (result) => {
            console.log('创建成功:', result.data.character);
        }
    }
});
```

### 高级使用

```javascript
// 完整配置的调用
PyVideoTool.CharacterCreationModal.open({
    mode: 'filtered',
    title: '为项目创建角色',
    filters: {
        projectType: 'video',
        styles: ['写实风3.0', '动漫可爱3.0'],
        tags: ['现代', '都市']
    },
    display: {
        showStyleSelection: true,
        showPreview: true
    },
    features: {
        enableUpload: true,
        enableAIGeneration: true,
        enablePublish: false
    },
    callbacks: {
        onSuccess: (result) => {
            // 处理成功结果
            addCharacterToProject(result.data.character);
        },
        onProgress: (progress) => {
            // 更新进度显示
            updateProgressBar(progress.progress);
        },
        onError: (error) => {
            // 处理错误
            handleCreationError(error);
        }
    }
});
```

## 🔄 版本管理

### 当前版本
- **版本号**: v1.0.0
- **发布日期**: 2024-01-01
- **兼容性**: Py视频创作工具 v2.0.0+

### 版本更新策略
- **主版本**: 不兼容的API变更
- **次版本**: 向后兼容的功能添加
- **修订版本**: 向后兼容的问题修复

---

*本规范文档将随着Py视频创作工具的发展持续更新和完善。*
