#!/usr/bin/env python3
"""
多AI程序员协同开发环境初始化脚本
Multi-AI Development Environment Setup Script
"""

import os
import json
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

class MultiAISetup:
    """多AI环境设置器"""
    
    def __init__(self, project_path: str = ".."):
        self.project_path = Path(project_path).resolve()
        self.ai_workspace = self.project_path / "ai_workspace"
        self.hooks_dir = self.project_path / ".git" / "hooks"
        
    def setup_environment(self):
        """设置完整环境"""
        print("🚀 开始设置多AI协同开发环境...")
        
        # 1. 创建目录结构
        self.create_directories()
        
        # 2. 配置Git
        self.configure_git()
        
        # 3. 安装Git钩子
        self.install_git_hooks()
        
        # 4. 创建配置文件
        self.create_config_files()
        
        # 5. 设置权限
        self.set_permissions()
        
        # 6. 验证安装
        self.verify_installation()
        
        print("✅ 多AI协同开发环境设置完成！")
        self.print_next_steps()
    
    def create_directories(self):
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        directories = [
            self.ai_workspace,
            self.ai_workspace / "logs",
            self.ai_workspace / "reports",
            self.ai_workspace / "temp",
            self.project_path / "qa_reports",
            self.project_path / "ai_configs"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"  ✓ {directory}")
    
    def configure_git(self):
        """配置Git设置"""
        print("⚙️ 配置Git设置...")
        
        git_configs = [
            ("merge.tool", "vimdiff"),
            ("merge.conflictstyle", "diff3"),
            ("core.autocrlf", "true"),
            ("http.postBuffer", "524288000"),
            ("submodule.fetchJobs", "4"),
            ("gc.auto", "1"),
            ("rerere.enabled", "true"),
            ("http.timeout", "300")
        ]
        
        for key, value in git_configs:
            try:
                subprocess.run(
                    ["git", "config", key, value],
                    cwd=self.project_path,
                    check=True,
                    capture_output=True
                )
                print(f"  ✓ {key} = {value}")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ 配置失败: {key} - {e}")
    
    def install_git_hooks(self):
        """安装Git钩子"""
        print("🪝 安装Git钩子...")
        
        if not self.hooks_dir.exists():
            self.hooks_dir.mkdir(parents=True)
        
        # Pre-commit钩子
        pre_commit_hook = self.hooks_dir / "pre-commit"
        pre_commit_content = '''#!/bin/bash
# 多AI协同开发 Pre-commit 钩子

echo "🔍 运行预提交检查..."

# 获取AI ID
AI_ID=$(git config user.name | grep -o "ai-[^-]*" || echo "unknown")

# 运行质量检查
if [ -f "ai_quality_assurance.py" ]; then
    python ai_quality_assurance.py --ai-id "$AI_ID" --branch "$(git branch --show-current)"
    if [ $? -ne 0 ]; then
        echo "❌ 质量检查失败，提交被阻止"
        exit 1
    fi
fi

# 检查冲突标记
if git diff --cached | grep -E "^[<>=]{7}"; then
    echo "❌ 发现冲突标记，请解决后再提交"
    exit 1
fi

echo "✅ 预提交检查通过"
exit 0
'''
        
        with open(pre_commit_hook, 'w', encoding='utf-8') as f:
            f.write(pre_commit_content)
        
        # 设置执行权限
        os.chmod(pre_commit_hook, 0o755)
        print(f"  ✓ pre-commit")
        
        # Post-commit钩子
        post_commit_hook = self.hooks_dir / "post-commit"
        post_commit_content = '''#!/bin/bash
# 多AI协同开发 Post-commit 钩子

echo "📊 记录提交信息..."

# 获取AI ID和分支
AI_ID=$(git config user.name | grep -o "ai-[^-]*" || echo "unknown")
BRANCH=$(git branch --show-current)
COMMIT_HASH=$(git rev-parse HEAD)

# 记录到日志
LOG_FILE="ai_workspace/logs/commits.log"
echo "$(date -Iseconds),$AI_ID,$BRANCH,$COMMIT_HASH,$(git log -1 --pretty=format:'%s')" >> "$LOG_FILE"

# 触发冲突检测
if [ -f "ai_conflict_resolver.py" ]; then
    python ai_conflict_resolver.py --check-branch "$BRANCH" &
fi

echo "✅ 提交后处理完成"
'''
        
        with open(post_commit_hook, 'w', encoding='utf-8') as f:
            f.write(post_commit_content)
        
        os.chmod(post_commit_hook, 0o755)
        print(f"  ✓ post-commit")
    
    def create_config_files(self):
        """创建配置文件"""
        print("📝 创建配置文件...")
        
        # AI冲突解决器配置
        conflict_config = {
            "ai_priorities": {
                "claude": 1,
                "gpt4": 2,
                "gemini": 3,
                "copilot": 4
            },
            "auto_resolve_types": [
                "whitespace",
                "imports",
                "comments",
                "formatting"
            ],
            "merge_strategies": {
                "function_conflict": "keep_both",
                "variable_conflict": "latest_timestamp",
                "config_conflict": "merge_all",
                "documentation_conflict": "longest_version"
            },
            "notification_webhook": "",
            "max_auto_resolve_attempts": 3,
            "check_interval": 300
        }
        
        with open(self.project_path / "ai_conflict_config.json", 'w', encoding='utf-8') as f:
            json.dump(conflict_config, f, indent=2, ensure_ascii=False)
        print("  ✓ ai_conflict_config.json")
        
        # 质量保证配置
        qa_config = {
            "quality_gates": {
                "min_test_coverage": 80,
                "max_complexity": 10,
                "max_duplicated_lines": 5,
                "min_maintainability": 70,
                "max_security_issues": 0
            },
            "test_commands": {
                "python": [
                    "python -m pytest --cov=. --cov-report=json",
                    "python -m flake8 --format=json",
                    "python -m bandit -r . -f json"
                ],
                "php": [
                    "vendor/bin/phpunit --coverage-clover coverage.xml",
                    "vendor/bin/phpcs --report=json",
                    "vendor/bin/psalm --output-format=json"
                ],
                "javascript": [
                    "npm test -- --coverage --json",
                    "npx eslint . --format json",
                    "npm audit --json"
                ]
            },
            "performance_thresholds": {
                "max_response_time": 200,
                "max_memory_usage": 100,
                "min_throughput": 1000
            },
            "auto_fix_enabled": True
        }
        
        with open(self.project_path / "qa_config.json", 'w', encoding='utf-8') as f:
            json.dump(qa_config, f, indent=2, ensure_ascii=False)
        print("  ✓ qa_config.json")
        
        # 工作调度器初始配置
        scheduler_config = {
            "max_concurrent_ais": 5,
            "task_timeout": 3600,
            "heartbeat_interval": 60,
            "auto_assign": True,
            "load_balancing": "round_robin"
        }
        
        with open(self.project_path / "scheduler_config.json", 'w', encoding='utf-8') as f:
            json.dump(scheduler_config, f, indent=2, ensure_ascii=False)
        print("  ✓ scheduler_config.json")
    
    def set_permissions(self):
        """设置文件权限"""
        print("🔐 设置文件权限...")
        
        # 设置脚本执行权限
        scripts = [
            "ai_conflict_resolver.py",
            "ai_work_scheduler.py",
            "ai_quality_assurance.py"
        ]
        
        for script in scripts:
            script_path = self.project_path / script
            if script_path.exists():
                os.chmod(script_path, 0o755)
                print(f"  ✓ {script}")
    
    def verify_installation(self):
        """验证安装"""
        print("🔍 验证安装...")
        
        # 检查必需文件
        required_files = [
            "git/ai_conflict_resolver.py",
            "git/ai_work_scheduler.py",
            "git/ai_quality_assurance.py",
            "git/multi-ai-git-config.md",
            "git/multi-ai-collaboration-guide.md"
        ]

        missing_files = []
        for file_name in required_files:
            if not (self.project_path / file_name).exists():
                missing_files.append(file_name)
        
        if missing_files:
            print(f"  ❌ 缺少文件: {', '.join(missing_files)}")
            return False
        
        # 检查Git配置
        try:
            result = subprocess.run(
                ["git", "config", "rerere.enabled"],
                cwd=self.project_path,
                capture_output=True,
                text=True,
                check=True
            )
            if result.stdout.strip() != "true":
                print("  ❌ Git rerere未启用")
                return False
        except subprocess.CalledProcessError:
            print("  ❌ Git配置检查失败")
            return False
        
        print("  ✅ 所有检查通过")
        return True
    
    def print_next_steps(self):
        """打印后续步骤"""
        print("\n🎯 后续步骤:")
        print("1. 进入git目录:")
        print("   cd git")
        print("\n2. 注册AI代理:")
        print("   python -c \"from ai_work_scheduler import AIWorkScheduler; s=AIWorkScheduler('..'); s.register_ai('your-ai-id', 'Your AI Name', ['python', 'javascript'])\"")
        print("\n3. 启动服务:")
        print("   python ai_work_scheduler.py --daemon --interval 30 &")
        print("   python ai_conflict_resolver.py --daemon --interval 300 &")
        print("\n4. 创建第一个任务:")
        print("   python -c \"from ai_work_scheduler import AIWorkScheduler; s=AIWorkScheduler('..'); s.create_task('测试任务', '这是一个测试任务', ['test.py'])\"")
        print("\n5. 查看状态:")
        print("   python -c \"from ai_work_scheduler import AIWorkScheduler; s=AIWorkScheduler('..'); print(s.get_status_report())\"")
        print("\n📖 详细文档: git/multi-ai-collaboration-guide.md")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="多AI协同开发环境设置")
    parser.add_argument("--project-path", default=".", help="项目路径")
    parser.add_argument("--force", action="store_true", help="强制重新设置")
    
    args = parser.parse_args()
    
    setup = MultiAISetup(args.project_path)
    
    # 检查是否已经设置过
    if not args.force and (setup.project_path / "ai_conflict_config.json").exists():
        response = input("环境似乎已经设置过，是否重新设置？(y/N): ")
        if response.lower() != 'y':
            print("取消设置")
            return
    
    try:
        setup.setup_environment()
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
