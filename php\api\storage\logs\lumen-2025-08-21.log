[2025-08-21 16:03:28] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","business_type":"text_generation_aistory"} 
[2025-08-21 16:03:28] production.INFO: 项目验证成功 {"project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 16:03:28] production.INFO: 项目验证成功 {"business_type":"text_generation_aistory","project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 16:03:28] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4077,"current_balance":"9734.92","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-21 16:03:28] production.DEBUG: TransactionManager::begin 调用 {"context":"PointsService.freezePoints","current_level_before":0,"stack_size_before":0,"process_id":7784,"memory_usage":18874368,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:03:28] production.DEBUG: Lumen事务开始 {"context":"PointsService.freezePoints","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"PointsService.freezePoints","current_level_before":1,"stack_size_before":1,"process_id":7784,"memory_usage":18874368,"transaction_stack_before":[{"context":"PointsService.freezePoints","level":1,"timestamp":1755763408.999008,"memory_usage":18874368}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":159,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:03:29] production.DEBUG: 事务提交 {"context":"PointsService.freezePoints","level":1,"action":"real_commit","duration_ms":42.93,"total_stack_cleared":true} 
[2025-08-21 16:03:29] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4077,"business_type":"text_generation","business_id":null,"transaction_id":301,"freeze_id":301} 
[2025-08-21 16:03:29] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"estimated_cost":1.4077,"generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-21 16:03:29] production.INFO: AI生成任务记录创建成功 {"task_record_id":38,"external_task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"status":"pending","created_outside_transaction":true} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"ProcessTextGeneration.handle","current_level_before":0,"stack_size_before":0,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":37,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen事务开始 {"context":"ProcessTextGeneration.handle","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:03:29] production.DEBUG: ProcessTextGeneration.handle - 事务开始 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"transaction_level":1,"in_transaction":true,"nesting_info":{"current_level":1,"max_level":30,"stack_size":1,"in_transaction":true,"is_nested":false,"remaining_capacity":29},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824}]} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","timestamp":1755763409,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:03:29] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","progress":10,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d2d1c5d5b","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","data_size":239,"max_attempts":3,"timestamp":"2025-08-21T16:03:29+08:00"} 
[2025-08-21 16:03:29] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":513} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763409.825602,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":2.99} 
[2025-08-21 16:03:29] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":239} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d2d1c5d5b","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","attempt":1,"success_count":1,"attempt_duration_ms":18.42,"total_duration_ms":30.98,"timestamp":"2025-08-21T16:03:29+08:00"} 
[2025-08-21 16:03:29] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":10,"message":"开始文本生成任务"} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":10,"message":"开始文本生成任务","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:03:29] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":513} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","timestamp":1755763409,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:03:29] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:03:29] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","progress":30,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d2d1ce30f","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","data_size":217,"max_attempts":3,"timestamp":"2025-08-21T16:03:29+08:00"} 
[2025-08-21 16:03:29] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:03:29] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":491} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763409.854886,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":2.07} 
[2025-08-21 16:03:29] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":217} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d2d1ce30f","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","attempt":1,"success_count":1,"attempt_duration_ms":12.48,"total_duration_ms":23.54,"timestamp":"2025-08-21T16:03:29+08:00"} 
[2025-08-21 16:03:29] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":30,"message":"连接AI平台"} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":30,"message":"连接AI平台","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationService.generateText","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":387,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationService.generateText","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.DEBUG: AiGenerationService.generateText - 事务开始 {"user_id":14,"external_task_id":"text_gen_1755763408_oWaQRN9k","transaction_level":2,"in_transaction":true,"nesting_info":{"current_level":2,"max_level":30,"stack_size":2,"in_transaction":true,"is_nested":true,"remaining_capacity":28},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824}]} 
[2025-08-21 16:03:29] production.INFO: generateText方法 - 参数检查 {"user_id":14,"external_task_id":"text_gen_1755763408_oWaQRN9k","generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4,"has_estimated_cost":true,"estimated_cost_value":1.4077} 
[2025-08-21 16:03:29] production.INFO: 任务状态更新为处理中 {"task_record_id":38,"external_task_id":"text_gen_1755763408_oWaQRN9k","status":"processing"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationService.executeTextGeneration","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1060,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationService.executeTextGeneration","level":3,"action":"nested_detected","framework":"Lumen 10","stack_size":3,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.INFO: 🔍 开始执行文本生成任务 {"task_id":38,"user_id":14,"platform":"deepseek","model_name":"deepseek-chat","task_type":"text_generation_aistory","external_task_id":"text_gen_1755763408_oWaQRN9k"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationTask.start","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":163,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1075,"function":"start","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationTask.start","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationTask.start","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763409.874363,"memory_usage":25165824},{"context":"AiGenerationTask.start","level":4,"timestamp":1755763409.874732,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":169,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1075,"function":"start","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationTask.start","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":0.54} 
[2025-08-21 16:03:29] production.INFO: AI生成任务开始 {"task_id":38,"user_id":14,"task_type":"text_generation_aistory","platform":"deepseek"} 
[2025-08-21 16:03:29] production.INFO: 🔍 准备调用AI服务 {"task_id":38,"platform":"deepseek","task_type":"text_generation_aistory","request_data":{"model":"deepseek-chat","max_tokens":"1000","temperature":"0.7","top_p":0.9,"prompt_length":1566},"ai_service_url":"https://aiapi.tiptop.cn/"} 
[2025-08-21 16:03:29] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":2969} 
[2025-08-21 16:03:29] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-21 16:03:29] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":491} 
[2025-08-21 16:03:29] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:03:29] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:03:29] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":7610} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"AiServiceClient.recordUserPreference","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":790,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":745,"function":"recordUserPreference","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1109,"function":"callWithUserChoice","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"AiServiceClient.recordUserPreference","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"AiServiceClient.recordUserPreference","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763409.874363,"memory_usage":25165824},{"context":"AiServiceClient.recordUserPreference","level":4,"timestamp":1755763409.97603,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":850,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":745,"function":"recordUserPreference","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1109,"function":"callWithUserChoice","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiServiceClient.recordUserPreference","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":9.01} 
[2025-08-21 16:03:29] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_aistory","usage_count":9,"success_rate":1.0} 
[2025-08-21 16:03:29] production.INFO: 🔍 AI服务调用完成 {"task_id":38,"platform":"deepseek","response_success":true,"response_status":"unknown","response_mode":"mock","has_data":true,"has_error":false} 
[2025-08-21 16:03:29] production.INFO: 🔍 AI服务调用成功，开始解析响应 {"task_id":38,"response_data_keys":["id","object","created","model","choices","usage"],"response_data_type":"array"} 
[2025-08-21 16:03:29] production.INFO: 🔍 AI响应解析完成 {"task_id":38,"generated_text_length":6592,"tokens_used":1849,"finish_reason":"stop","model":"deepseek-chat"} 
[2025-08-21 16:03:29] production.INFO: 🔍 开始完成任务并消费积分 {"task_id":38,"output_data_keys":["text","finish_reason","model","usage"],"tokens_used":1849} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationTask.complete","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":197,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1157,"function":"complete","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationTask.complete","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationTask.complete","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763409.874363,"memory_usage":25165824},{"context":"AiGenerationTask.complete","level":4,"timestamp":1755763409.985862,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":210,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1157,"function":"complete","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationTask.complete","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":4.87} 
[2025-08-21 16:03:29] production.INFO: AI生成任务完成 {"task_id":38,"user_id":14,"task_type":"text_generation_aistory","platform":"deepseek","tokens_used":1849,"processing_time_ms":986} 
[2025-08-21 16:03:29] production.INFO: 🔍 任务完成 {"task_id":38,"task_status":"completed","processing_time_ms":986} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationService.executeTextGeneration","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763409.874363,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1190,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:03:29] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationService.executeTextGeneration","level":3,"action":"nested_commit","remaining_stack_size":2,"duration_ms":117.05} 
[2025-08-21 16:03:29] production.DEBUG: TransactionManager::begin 调用 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":723,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:03:29] production.DEBUG: Lumen嵌套事务检测 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"action":"nested_detected","framework":"Lumen 10","stack_size":3,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:29] production.INFO: 开始事务回滚 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","reason":"缺少故事标题","original_level":3,"stack_size":3,"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824},{"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"timestamp":1755763409.991651,"memory_usage":25165824}],"action":"start_rollback"} 
[2025-08-21 16:03:29] production.WARNING: 嵌套事务回滚标记 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","reason":"缺少故事标题","original_level":3,"new_level":2,"remaining_stack_size":2,"action":"nested_rollback_marked","rollback_type":"progressive_rollback","duration_ms":0.27} 
[2025-08-21 16:03:29] production.INFO: 开始事务回滚 {"context":"AiGenerationService.generateText","reason":"分镜生成失败：缺少故事标题","original_level":2,"stack_size":2,"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763409.868594,"memory_usage":25165824}],"action":"start_rollback"} 
[2025-08-21 16:03:29] production.WARNING: 嵌套事务回滚标记 {"context":"AiGenerationService.generateText","reason":"分镜生成失败：缺少故事标题","original_level":2,"new_level":1,"remaining_stack_size":1,"action":"nested_rollback_marked","rollback_type":"progressive_rollback","duration_ms":123.52} 
[2025-08-21 16:03:29] production.INFO: 业务处理失败，事务回滚后任务状态已更新 {"task_record_id":38,"external_task_id":"text_gen_1755763408_oWaQRN9k","status":"failed","error_message":"分镜生成失败：缺少故事标题"} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":60,"message":"AI文本生成完成","push_type":"ai_generation_progress","timestamp":1755763409,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:03:29] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","progress":60,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:03:29] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d2d1f3971","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","data_size":229,"max_attempts":3,"timestamp":"2025-08-21T16:03:29+08:00"} 
[2025-08-21 16:03:30] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":503} 
[2025-08-21 16:03:30] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:30] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:30] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763410.00841,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:30] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.88} 
[2025-08-21 16:03:30] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":229} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d2d1f3971","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","attempt":1,"success_count":1,"attempt_duration_ms":12.63,"total_duration_ms":22.87,"timestamp":"2025-08-21T16:03:30+08:00"} 
[2025-08-21 16:03:30] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":60,"message":"AI文本生成完成"} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":60,"message":"AI文本生成完成","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:03:30] production.DEBUG: ProcessTextGeneration Job - 任务参数 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"context":"111","estimated_cost":1.4077,"points_status":"already_frozen_in_generateTextWithWebSocket"} 
[2025-08-21 16:03:30] production.WARNING: ProcessTextGeneration - 开始事务回滚 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"context":"111","error_message":"分镜生成失败：缺少故事标题","error_type":"business_error","is_business_exception":true,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle","rollback_reason":"文本生成任务执行失败","timestamp":1755763410} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":90,"message":"处理生成结果中...","push_type":"ai_generation_progress","timestamp":1755763410,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:03:30] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","progress":90,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d2d205b91","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","data_size":236,"max_attempts":3,"timestamp":"2025-08-21T16:03:30+08:00"} 
[2025-08-21 16:03:30] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":2,"data_size":510} 
[2025-08-21 16:03:30] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:30] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:30] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763410.03434,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:30] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.64} 
[2025-08-21 16:03:30] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":236} 
[2025-08-21 16:03:30] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":503} 
[2025-08-21 16:03:30] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d2d205b91","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763408_oWaQRN9k","attempt":1,"success_count":1,"attempt_duration_ms":12.64,"total_duration_ms":21.81,"timestamp":"2025-08-21T16:03:30+08:00"} 
[2025-08-21 16:03:30] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":90,"message":"处理生成结果中..."} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"progress":90,"message":"处理生成结果中...","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:03:30] production.INFO: ProcessTextGeneration - 开始失败处理 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"error_message":"分镜生成失败：缺少故事标题","current_transaction_level":1,"business_type":"text_generation_aistory","processing_order":"1. 推送进度 -> 2. 推送失败事件 -> 3. 事务回滚 -> 4. 发布事件","timestamp":1755763410} 
[2025-08-21 16:03:30] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:03:30] production.INFO: 使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationCompleted"} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","push_type":"aistory_generation_failed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755763410,"job_class":"App\\Services\\PyApi\\WebSocketEventService","job_method":"handle"} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d2d20bd64","user_id":14,"event_type":"aistory_generation_failed","context":"aistory_generation_failed:text_gen_1755763408_oWaQRN9k","data_size":353,"max_attempts":3,"timestamp":"2025-08-21T16:03:30+08:00"} 
[2025-08-21 16:03:30] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"aistory_generation_failed","list_length":2,"data_size":630} 
[2025-08-21 16:03:30] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":387,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:30] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:03:30] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763410.057767,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":387,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:03:30] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.81} 
[2025-08-21 16:03:30] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"aistory_generation_failed","data_size":353} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d2d20bd64","user_id":14,"event_type":"aistory_generation_failed","context":"aistory_generation_failed:text_gen_1755763408_oWaQRN9k","attempt":1,"success_count":1,"attempt_duration_ms":11.19,"total_duration_ms":20.18,"timestamp":"2025-08-21T16:03:30+08:00"} 
[2025-08-21 16:03:30] production.INFO: AI生成失败推送成功 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"task_type":"text_generation_aistory","is_failure":true,"event_type":"aistory_generation_failed"} 
[2025-08-21 16:03:30] production.WARNING: 无任务记录，跳过积分处理 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"is_failure":true} 
[2025-08-21 16:03:30] production.INFO: WebSocket推送结果 - 业务错误失败 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"push_success":"yes","push_method":"Redis通道桥接","error_message":"分镜生成失败：缺少故事标题","error_type":"business_error","push_message":"AI生成失败推送成功","points_refunded":true,"will_retry":false} 
[2025-08-21 16:03:30] production.INFO: 开始事务回滚 {"context":"ProcessTextGeneration.handle","reason":"分镜生成失败：缺少故事标题","original_level":1,"stack_size":1,"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763409.805366,"memory_usage":25165824}],"action":"start_rollback"} 
[2025-08-21 16:03:30] production.WARNING: 事务回滚完成 {"context":"ProcessTextGeneration.handle","reason":"分镜生成失败：缺少故事标题","original_level":1,"final_level":0,"cleared_stack_size":1,"action":"rollback_completed","rollback_type":"real_rollback"} 
[2025-08-21 16:03:30] production.WARNING: ProcessTextGeneration - 事务回滚完成 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"context":"111","rollback_success":"yes","final_transaction_level":0,"rollback_reason":"分镜生成失败：缺少故事标题","transaction_context":"ProcessTextGeneration.handle","job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle","timestamp":1755763410,"next_action":"publish_failure_event"} 
[2025-08-21 16:03:30] production.INFO: ProcessTextGeneration - 开始发布失败事件 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"transaction_level":0,"business_type":"text_generation_aistory","context":"post_rollback_event_publish"} 
[2025-08-21 16:03:30] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":510} 
[2025-08-21 16:03:30] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:03:30] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:03:30] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":630} 
[2025-08-21 16:03:30] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"aistory_generation_failed"} 
[2025-08-21 16:03:30] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"aistory_generation_failed"} 
[2025-08-21 16:03:30] production.INFO: 内部服务认证成功 {"token_hint":"internal...","service_type":"internal_api"} 
[2025-08-21 16:03:30] production.DEBUG: EventController - 允许的事件类型 {"total_count":42,"event_types":["text_generation_failed","text_generation_completed","project_creation_failed","project_creation_completed","character_creation_failed","character_creation_completed","aistory_generation_failed","aistory_generation_completed","mystory_generation_failed","mystory_generation_completed","prompt_generation_failed","prompt_generation_completed","prompt_character_failed","prompt_character_completed","prompt_style_failed","prompt_style_completed","story_generation_failed","story_generation_completed","image_character_generation_failed","image_character_generation_completed","style_generation_failed","style_generation_completed","image_generation_failed","image_generation_completed","video_generation_failed","video_generation_completed","voice_synthesis_failed","voice_synthesis_completed","voice_audition_failed","voice_audition_completed","sound_generation_failed","sound_generation_completed","music_generation_failed","music_generation_completed","audio_mix_failed","audio_mix_completed","audio_enhance_failed","audio_enhance_completed","ai_generation_progress","ai_service_error","points_transaction_failed","websocket_connection_error"],"requested_event_type":"aistory_generation_failed"} 
[2025-08-21 16:03:30] production.INFO: 事件总线发布 {"event_id":"evt_1755763410_420493ff","event_type":"aistory_generation_failed","business_id":"text_gen_1755763408_oWaQRN9k"} 
[2025-08-21 16:03:30] production.INFO: 事件发布成功 {"event_id":"evt_1755763410_420493ff","event_type":"aistory_generation_failed","business_id":"text_gen_1755763408_oWaQRN9k","user_id":14} 
[2025-08-21 16:03:30] production.INFO: 文本生成失败事件发布成功 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"event_type":"aistory_generation_failed","event_response":{"code":200,"message":"事件发布成功","data":{"event_id":"evt_1755763410_420493ff","event_type":"aistory_generation_failed","business_id":"text_gen_1755763408_oWaQRN9k","published_at":"2025-08-21T16:03:30+08:00"},"timestamp":1755763410,"request_id":"req_68a6d2d258b88_d6d603e0"},"listeners_notified":"系统内部组件已收到失败通知"} 
[2025-08-21 16:03:30] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1755763408_oWaQRN9k","user_id":14,"context":"111","prompt":"AAAAAAAAAAAAAAAAAAAAAA","error":"分镜生成失败：缺少故事标题","error_type":"business_error"} 
[2025-08-21 16:03:30] production.INFO: 业务错误处理完成，任务正常结束 {"task_id":"text_gen_1755763408_oWaQRN9k","error_type":"business_error","error_message":"分镜生成失败：缺少故事标题","actions_completed":{"websocket_notification":true,"points_refunded":true,"transaction_rollback":true,"event_published":true}} 
[2025-08-21 16:04:00] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":225,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 16:04:00] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:04:00] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":225,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755763440.6788,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 16:04:00] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":57.57,"total_stack_cleared":true} 
[2025-08-21 16:04:00] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":1,"timestamp":"2025-08-21T16:04:00+08:00"} 
[2025-08-21 16:05:09] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","business_type":"text_generation_aistory"} 
[2025-08-21 16:05:09] production.INFO: 项目验证成功 {"project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 16:05:09] production.INFO: 项目验证成功 {"business_type":"text_generation_aistory","project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 16:05:09] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4077,"current_balance":"9733.51","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"PointsService.freezePoints","current_level_before":0,"stack_size_before":0,"process_id":16696,"memory_usage":18874368,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen事务开始 {"context":"PointsService.freezePoints","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"PointsService.freezePoints","current_level_before":1,"stack_size_before":1,"process_id":16696,"memory_usage":18874368,"transaction_stack_before":[{"context":"PointsService.freezePoints","level":1,"timestamp":1755763509.132726,"memory_usage":18874368}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":159,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:05:09] production.DEBUG: 事务提交 {"context":"PointsService.freezePoints","level":1,"action":"real_commit","duration_ms":127.42,"total_stack_cleared":true} 
[2025-08-21 16:05:09] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4077,"business_type":"text_generation","business_id":null,"transaction_id":302,"freeze_id":302} 
[2025-08-21 16:05:09] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"estimated_cost":1.4077,"generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-21 16:05:09] production.INFO: AI生成任务记录创建成功 {"task_record_id":39,"external_task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"status":"pending","created_outside_transaction":true} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"ProcessTextGeneration.handle","current_level_before":0,"stack_size_before":0,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":37,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen事务开始 {"context":"ProcessTextGeneration.handle","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:05:09] production.DEBUG: ProcessTextGeneration.handle - 事务开始 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"transaction_level":1,"in_transaction":true,"nesting_info":{"current_level":1,"max_level":30,"stack_size":1,"in_transaction":true,"is_nested":false,"remaining_capacity":29},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824}]} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","timestamp":1755763509,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:05:09] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory","progress":10,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d335d17f9","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","data_size":239,"max_attempts":3,"timestamp":"2025-08-21T16:05:09+08:00"} 
[2025-08-21 16:05:09] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":513} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763509.866809,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.7} 
[2025-08-21 16:05:09] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":239} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d335d17f9","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","attempt":1,"success_count":1,"attempt_duration_ms":10.41,"total_duration_ms":19.85,"timestamp":"2025-08-21T16:05:09+08:00"} 
[2025-08-21 16:05:09] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":10,"message":"开始文本生成任务"} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":10,"message":"开始文本生成任务","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","timestamp":1755763509,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:05:09] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory","progress":30,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d335d6f2e","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","data_size":217,"max_attempts":3,"timestamp":"2025-08-21T16:05:09+08:00"} 
[2025-08-21 16:05:09] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":2,"data_size":491} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763509.889452,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.28} 
[2025-08-21 16:05:09] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":217} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d335d6f2e","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","attempt":1,"success_count":1,"attempt_duration_ms":10.38,"total_duration_ms":19.48,"timestamp":"2025-08-21T16:05:09+08:00"} 
[2025-08-21 16:05:09] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":30,"message":"连接AI平台"} 
[2025-08-21 16:05:09] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":30,"message":"连接AI平台","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationService.generateText","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":387,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationService.generateText","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: AiGenerationService.generateText - 事务开始 {"user_id":14,"external_task_id":"text_gen_1755763509_fRjKSt2c","transaction_level":2,"in_transaction":true,"nesting_info":{"current_level":2,"max_level":30,"stack_size":2,"in_transaction":true,"is_nested":true,"remaining_capacity":28},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824}]} 
[2025-08-21 16:05:09] production.INFO: generateText方法 - 参数检查 {"user_id":14,"external_task_id":"text_gen_1755763509_fRjKSt2c","generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4,"has_estimated_cost":true,"estimated_cost_value":1.4077} 
[2025-08-21 16:05:09] production.INFO: 任务状态更新为处理中 {"task_record_id":39,"external_task_id":"text_gen_1755763509_fRjKSt2c","status":"processing"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationService.executeTextGeneration","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1060,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationService.executeTextGeneration","level":3,"action":"nested_detected","framework":"Lumen 10","stack_size":3,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.INFO: 🔍 开始执行文本生成任务 {"task_id":39,"user_id":14,"platform":"deepseek","model_name":"deepseek-chat","task_type":"text_generation_aistory","external_task_id":"text_gen_1755763509_fRjKSt2c"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationTask.start","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":163,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1075,"function":"start","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationTask.start","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationTask.start","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763509.905136,"memory_usage":25165824},{"context":"AiGenerationTask.start","level":4,"timestamp":1755763509.905469,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":169,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1075,"function":"start","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationTask.start","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":0.54} 
[2025-08-21 16:05:09] production.INFO: AI生成任务开始 {"task_id":39,"user_id":14,"task_type":"text_generation_aistory","platform":"deepseek"} 
[2025-08-21 16:05:09] production.INFO: 🔍 准备调用AI服务 {"task_id":39,"platform":"deepseek","task_type":"text_generation_aistory","request_data":{"model":"deepseek-chat","max_tokens":"1000","temperature":"0.7","top_p":0.9,"prompt_length":1566},"ai_service_url":"https://aiapi.tiptop.cn/"} 
[2025-08-21 16:05:09] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":2969} 
[2025-08-21 16:05:09] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-21 16:05:09] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":513} 
[2025-08-21 16:05:09] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:05:09] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:05:09] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":7608} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"AiServiceClient.recordUserPreference","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":790,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":745,"function":"recordUserPreference","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1109,"function":"callWithUserChoice","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"AiServiceClient.recordUserPreference","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"AiServiceClient.recordUserPreference","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763509.905136,"memory_usage":25165824},{"context":"AiServiceClient.recordUserPreference","level":4,"timestamp":1755763509.979381,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":850,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":745,"function":"recordUserPreference","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1109,"function":"callWithUserChoice","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiServiceClient.recordUserPreference","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":8.0} 
[2025-08-21 16:05:09] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_aistory","usage_count":9,"success_rate":1.0} 
[2025-08-21 16:05:09] production.INFO: 🔍 AI服务调用完成 {"task_id":39,"platform":"deepseek","response_success":true,"response_status":"unknown","response_mode":"mock","has_data":true,"has_error":false} 
[2025-08-21 16:05:09] production.INFO: 🔍 AI服务调用成功，开始解析响应 {"task_id":39,"response_data_keys":["id","object","created","model","choices","usage"],"response_data_type":"array"} 
[2025-08-21 16:05:09] production.INFO: 🔍 AI响应解析完成 {"task_id":39,"generated_text_length":6590,"tokens_used":1849,"finish_reason":"stop","model":"deepseek-chat"} 
[2025-08-21 16:05:09] production.INFO: 🔍 开始完成任务并消费积分 {"task_id":39,"output_data_keys":["text","finish_reason","model","usage"],"tokens_used":1849} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationTask.complete","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":197,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1157,"function":"complete","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationTask.complete","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationTask.complete","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763509.905136,"memory_usage":25165824},{"context":"AiGenerationTask.complete","level":4,"timestamp":1755763509.988222,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":210,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1157,"function":"complete","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationTask.complete","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":4.54} 
[2025-08-21 16:05:09] production.INFO: AI生成任务完成 {"task_id":39,"user_id":14,"task_type":"text_generation_aistory","platform":"deepseek","tokens_used":1849,"processing_time_ms":988} 
[2025-08-21 16:05:09] production.INFO: 🔍 任务完成 {"task_id":39,"task_status":"completed","processing_time_ms":988} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationService.executeTextGeneration","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755763509.905136,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1190,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:05:09] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationService.executeTextGeneration","level":3,"action":"nested_commit","remaining_stack_size":2,"duration_ms":88.1} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":723,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"action":"nested_detected","framework":"Lumen 10","stack_size":3,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:09] production.DEBUG: TransactionManager::begin 调用 {"context":"ProjectService.updateProject","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectService.php","line":516,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":745,"function":"updateProject","class":"App\\Services\\PyApi\\ProjectService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 16:05:09] production.DEBUG: Lumen嵌套事务检测 {"context":"ProjectService.updateProject","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:10] production.INFO: 项目更新成功 {"project_id":3,"user_id":14,"updated_fields":["title","description","project_config","story_content","updated_at"]} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"ProjectService.updateProject","current_level_before":4,"stack_size_before":4,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824},{"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"timestamp":1755763509.993406,"memory_usage":25165824},{"context":"ProjectService.updateProject","level":4,"timestamp":1755763509.994117,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectService.php","line":553,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":745,"function":"updateProject","class":"App\\Services\\PyApi\\ProjectService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"ProjectService.updateProject","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":6.29} 
[2025-08-21 16:05:10] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":491} 
[2025-08-21 16:05:10] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","current_level_before":3,"stack_size_before":3,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824},{"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"timestamp":1755763509.993406,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":807,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"action":"nested_commit","remaining_stack_size":2,"duration_ms":97.16} 
[2025-08-21 16:05:10] production.INFO: 故事分镜拆解成功 {"project_id":3,"scenario_count":3,"storyboard_count":16,"character_count":4} 
[2025-08-21 16:05:10] production.INFO: 文本生成任务创建成功 {"task_id":39,"user_id":14,"model_id":1,"estimated_cost":1.4077} 
[2025-08-21 16:05:10] production.DEBUG: AiGenerationService.generateText - 准备提交事务 {"user_id":14,"external_task_id":"text_gen_1755763509_fRjKSt2c","transaction_level":2,"in_transaction":true,"nesting_info":{"current_level":2,"max_level":30,"stack_size":2,"in_transaction":true,"is_nested":true,"remaining_capacity":28},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824}]} 
[2025-08-21 16:05:10] production.INFO: 任务状态更新为完成 {"task_record_id":39,"external_task_id":"text_gen_1755763509_fRjKSt2c","status":"completed"} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationService.generateText","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755763509.900385,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":684,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"}]} 
[2025-08-21 16:05:10] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationService.generateText","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":194.2} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":60,"message":"AI文本生成完成","push_type":"ai_generation_progress","timestamp":1755763510,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:05:10] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory","progress":60,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d33617c15","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","data_size":229,"max_attempts":3,"timestamp":"2025-08-21T16:05:10+08:00"} 
[2025-08-21 16:05:10] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":503} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763510.106064,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.32} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":229} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d33617c15","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","attempt":1,"success_count":1,"attempt_duration_ms":10.18,"total_duration_ms":19.94,"timestamp":"2025-08-21T16:05:10+08:00"} 
[2025-08-21 16:05:10] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":60,"message":"AI文本生成完成"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":60,"message":"AI文本生成完成","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:05:10] production.DEBUG: ProcessTextGeneration Job - 任务参数 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"context":"111","estimated_cost":1.4077,"points_status":"already_frozen_in_generateTextWithWebSocket"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","timestamp":1755763510,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:05:10] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory","progress":80,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d3361d68d","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","data_size":227,"max_attempts":3,"timestamp":"2025-08-21T16:05:10+08:00"} 
[2025-08-21 16:05:10] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":2,"data_size":501} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763510.129342,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.26} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":227} 
[2025-08-21 16:05:10] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":503} 
[2025-08-21 16:05:10] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d3361d68d","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","attempt":1,"success_count":1,"attempt_duration_ms":10.19,"total_duration_ms":17.47,"timestamp":"2025-08-21T16:05:10+08:00"} 
[2025-08-21 16:05:10] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":80,"message":"保存文本数据"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":80,"message":"保存文本数据","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress","timestamp":1755763510,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 16:05:10] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory","progress":100,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送开始 {"push_id":"push_68a6d336224e2","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","data_size":228,"max_attempts":3,"timestamp":"2025-08-21T16:05:10+08:00"} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":2,"data_size":502} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755763510.148177,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 16:05:10] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.45} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","event_type":"ai_generation_progress","data_size":228} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送成功 {"push_id":"push_68a6d336224e2","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755763509_fRjKSt2c","attempt":1,"success_count":1,"attempt_duration_ms":9.13,"total_duration_ms":16.9,"timestamp":"2025-08-21T16:05:10+08:00"} 
[2025-08-21 16:05:10] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":100,"message":"文本生成完成"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"progress":100,"message":"文本生成完成","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 16:05:10] production.INFO: 使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory","method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationCompleted"} 
[2025-08-21 16:05:10] production.ERROR: 成功情况下必须有任务记录 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"task_type":"text_generation_aistory"} 
[2025-08-21 16:05:10] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"push_success":"no","push_method":"Redis通道桥接","push_message":"成功情况下必须有任务记录","points_consumed":false} 
[2025-08-21 16:05:10] production.DEBUG: ProcessTextGeneration.handle - 准备提交事务 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"transaction_level":1,"in_transaction":true,"nesting_info":{"current_level":1,"max_level":30,"stack_size":1,"in_transaction":true,"is_nested":false,"remaining_capacity":29},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824}]} 
[2025-08-21 16:05:10] production.DEBUG: TransactionManager::commit 调用 {"context":"ProcessTextGeneration.handle","current_level_before":1,"stack_size_before":1,"process_id":2336,"memory_usage":25165824,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755763509.854184,"memory_usage":25165824}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":204,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":37,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:05:10] production.DEBUG: 事务提交 {"context":"ProcessTextGeneration.handle","level":1,"action":"real_commit","duration_ms":312.24,"total_stack_cleared":true} 
[2025-08-21 16:05:10] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755763509_fRjKSt2c","user_id":14,"context":"111","ai_task_id":39,"cost":0,"processing_steps_completed":{"1_websocket_session_validation":true,"2_progress_notifications":true,"3_ai_text_generation":true,"4_parameter_validation":true,"5_result_processing":true,"6_websocket_completion_event":false,"7_transaction_commit":true}} 
[2025-08-21 16:05:10] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":501} 
[2025-08-21 16:05:10] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","list_key":"websocket:queue:ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_length":502} 
[2025-08-21 16:05:10] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","message_type":"ai_generation_progress"} 
[2025-08-21 16:05:10] production.INFO: WebSocket消息推送成功 {"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","connection_id":"3","event":"ai_generation_progress"} 
[2025-08-21 16:06:00] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":225,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 16:06:00] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:06:00] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":225,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755763560.673419,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 16:06:00] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":11.37,"total_stack_cleared":true} 
[2025-08-21 16:06:00] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":1,"timestamp":"2025-08-21T16:06:00+08:00"} 
[2025-08-21 16:06:18] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755763578_ySLi5ezv","user_id":14,"session_id":"ws_jJpElznDOageW9FXw5L1mrp0PB56V7vA","business_type":"text_generation_aistory"} 
[2025-08-21 16:06:18] production.INFO: 项目验证成功 {"project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 16:06:18] production.INFO: 项目验证成功 {"business_type":"text_generation_aistory","project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 16:06:18] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4077,"current_balance":"9732.10","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-21 16:06:18] production.DEBUG: TransactionManager::begin 调用 {"context":"PointsService.freezePoints","current_level_before":0,"stack_size_before":0,"process_id":16696,"memory_usage":18874368,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:06:18] production.DEBUG: Lumen事务开始 {"context":"PointsService.freezePoints","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 16:06:18] production.DEBUG: TransactionManager::commit 调用 {"context":"PointsService.freezePoints","current_level_before":1,"stack_size_before":1,"process_id":16696,"memory_usage":18874368,"transaction_stack_before":[{"context":"PointsService.freezePoints","level":1,"timestamp":1755763578.674629,"memory_usage":18874368}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":159,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 16:06:18] production.DEBUG: 事务提交 {"context":"PointsService.freezePoints","level":1,"action":"real_commit","duration_ms":63.33,"total_stack_cleared":true} 
[2025-08-21 16:06:18] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4077,"business_type":"text_generation","business_id":null,"transaction_id":303,"freeze_id":303} 
[2025-08-21 16:06:18] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755763578_ySLi5ezv","user_id":14,"estimated_cost":1.4077,"generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-21 16:06:18] production.INFO: AI生成任务记录创建成功 {"task_record_id":40,"external_task_id":"text_gen_1755763578_ySLi5ezv","user_id":14,"status":"pending","created_outside_transaction":true} 
