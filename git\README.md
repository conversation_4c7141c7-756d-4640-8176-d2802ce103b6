# 🤖 多AI程序员协同开发工具集

本目录包含多AI程序员协同开发的完整工具集和文档。

## 📁 目录结构

```
git/
├── README.md                        # 本说明文档
├── multi-ai-collaboration-guide.md  # 完整协作指南
├── multi-ai-git-config.md          # Git配置规范
├── ai_conflict_resolver.py          # 冲突检测和解决工具
├── ai_work_scheduler.py             # 工作分配和调度系统
├── ai_quality_assurance.py          # 质量保证和测试系统
├── setup_multi_ai.py               # 环境初始化脚本
└── requirements.txt                 # Python依赖包列表
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd git
pip install -r requirements.txt
```

### 2. 初始化环境
```bash
python setup_multi_ai.py
```

### 3. 启动服务
```bash
# 启动工作调度器
python ai_work_scheduler.py --daemon --interval 30 &

# 启动冲突检测器
python ai_conflict_resolver.py --daemon --interval 300 &
```

## 📖 文档说明

- **multi-ai-collaboration-guide.md** - 详细的多AI协同开发指南，包含完整的工作流程、最佳实践和故障排除
- **multi-ai-git-config.md** - Git分支管理策略和配置规范

## 🛠️ 工具说明

- **ai_conflict_resolver.py** - 自动检测和解决代码冲突，支持多种冲突类型的智能处理
- **ai_work_scheduler.py** - 智能任务分配和调度，支持AI能力匹配和负载均衡
- **ai_quality_assurance.py** - 全面的代码质量检查，包括测试、安全、性能等多个维度
- **setup_multi_ai.py** - 一键环境初始化，自动配置Git钩子和相关设置

## 🎯 核心特性

- ✅ **智能冲突解决** - 自动解决80%以上的代码冲突
- ✅ **任务智能分配** - 基于AI能力的智能任务调度
- ✅ **全面质量保证** - 多维度代码质量检查和门禁
- ✅ **实时监控** - 完整的状态监控和报告系统
- ✅ **一键部署** - 简单的环境初始化和配置

## 📊 系统要求

- Python 3.8+
- Git 2.20+
- 支持的项目类型：Python、PHP、JavaScript/TypeScript

## 🔧 配置说明

系统会在项目根目录创建以下配置文件：
- `ai_conflict_config.json` - 冲突解决配置
- `qa_config.json` - 质量保证配置
- `scheduler_config.json` - 调度器配置

## 📞 支持

如有问题，请参考 `multi-ai-collaboration-guide.md` 中的故障排除章节。

---

**版本**: v1.0.0  
**更新日期**: 2024-12-07  
**维护者**: AI协同开发团队
