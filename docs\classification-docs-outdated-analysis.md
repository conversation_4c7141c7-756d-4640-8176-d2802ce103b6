# "分类文档应用场景明确定义"节点失效信息分析报告

## 🎯 分析目标

检查 `index.mdc` 中"分类文档应用场景明确定义"节点是否有因为环境切换调整而失效的信息。

## 🔍 环境切换机制变化对比

### **原有机制 (index.mdc 中的描述)**
- **直接调用模拟服务**: 工具API直接调用 `https://aiapi.tiptop.cn`
- **虚拟服务概念**: 使用"虚拟服务"、"模拟服务"等术语
- **单一调用路径**: 工具API → AI模拟服务

### **现有机制 (实际实现)**
- **环境切换客户端**: 通过 `AiServiceClient` 和 `ThirdPartyServiceClient` 实现
- **配置驱动切换**: 通过 `AI_SERVICE_MODE` 环境变量控制 (mock/real)
- **双路径架构**: 工具API → 服务客户端 → 模拟服务/真实服务

## 🚨 **发现的失效信息**

### **1. 架构描述失效**

#### **❌ 失效内容**
```
🤖 【工具api接口服务】对接【AI服务集成摸拟返回数据服务】场景
- AI API调用必须遵循虚拟服务规范（https://aiapi.tiptop.cn）
- AI服务集成摸拟返回数据服务开发
```

#### **✅ 应更新为**
```
🤖 【工具api接口服务】通过【环境切换服务客户端】对接【AI服务】场景
- AI API调用必须通过 AiServiceClient 实现环境切换
- 支持 mock 模式（模拟服务）和 real 模式（真实服务）
```

### **2. 调用方式描述失效**

#### **❌ 失效内容**
```
- AI API接口调用：调用DeepSeek、LiblibAI、KlingAI、MiniMax、火山引擎豆包等AI服务
- AI服务集成摸拟返回数据服务开发：集成文本生成、图像生成、语音合成、视频生成功能
```

#### **✅ 应更新为**
```
- AI API接口调用：通过 AiServiceClient::call() 调用AI服务，支持环境自动切换
- AI服务环境切换开发：实现 mock/real 模式的无缝切换，确保开发和生产环境的一致性
```

### **3. 技术规范失效**

#### **❌ 失效内容**
```
- AI API调用必须遵循虚拟服务规范（https://aiapi.tiptop.cn）
- AI服务调用超时设置建议30秒
- 必须实现AI服务降级和错误处理机制
```

#### **✅ 应更新为**
```
- AI API调用必须使用 AiServiceClient::call() 方法
- 模拟服务超时30秒，真实服务超时60秒（可配置）
- 环境切换机制自动处理服务降级和错误处理
```

### **4. 业务流程图失效**

#### **❌ 失效内容**
```
participant AI as AI平台/虚拟服务
A->>AI: 调用AI服务
AI->>A: 返回结果
```

#### **✅ 应更新为**
```
participant SC as AiServiceClient
participant AI as AI平台/模拟服务
A->>SC: 调用AiServiceClient
SC->>AI: 环境切换调用
AI->>SC: 返回结果+mode标识
SC->>A: 返回结果{success, mode, data}
```

### **5. 文档引用失效**

#### **❌ 失效内容**
```
- 所有AI功能开发必须使用dev-aiapi-guidelines.mdc作为权威依据
- 可完全替代原dev-api-guidelines-new.mdc使用
```

#### **✅ 应更新为**
```
- 所有AI功能开发必须遵循环境切换机制，使用 AiServiceClient
- 必须配合 index-new.mdc 中的环境切换规范使用
```

## 📊 **失效程度评估**

### **高度失效 (需要重写)**
- **架构描述**: 从直接调用改为通过服务客户端调用
- **调用方式**: 从直接HTTP调用改为 AiServiceClient::call()
- **环境概念**: 从"虚拟服务"改为"环境切换机制"

### **中度失效 (需要更新)**
- **技术规范**: 超时设置、错误处理机制有变化
- **业务流程**: 增加了服务客户端层
- **文档引用**: 需要与 index-new.mdc 协调

### **轻度失效 (需要补充)**
- **AI平台列表**: 基本正确，但缺少环境切换说明
- **接口统计**: 数量基本正确，但调用方式有变化

## 🔄 **更新建议**

### **1. 核心概念更新**
- **"AI服务集成摸拟返回数据服务"** → **"AI服务环境切换机制"**
- **"虚拟服务规范"** → **"环境切换服务客户端规范"**
- **"直接调用"** → **"通过服务客户端调用"**

### **2. 技术实现更新**
- **调用方式**: 所有AI服务调用必须使用 `AiServiceClient::call()`
- **配置管理**: 通过 `php/api/config/ai.php` 进行环境切换配置
- **响应格式**: 包含 `mode` 字段标识当前调用模式

### **3. 文档协调更新**
- **主要文档**: 以 `index-new.mdc` 为权威架构规范
- **辅助文档**: `dev-aiapi-guidelines.mdc` 需要更新环境切换相关内容
- **交叉引用**: 建立与环境切换机制的明确关联

### **4. 业务流程更新**
- **增加服务客户端层**: 所有业务流程图需要包含 AiServiceClient
- **环境标识**: 响应中包含 mode 字段 (mock/real)
- **错误处理**: 环境切换失败的处理机制

## 🎯 **迁移策略建议**

### **1. 分阶段更新**
1. **第一阶段**: 更新核心概念和架构描述
2. **第二阶段**: 更新技术实现和调用方式
3. **第三阶段**: 更新业务流程图和文档引用

### **2. 保持兼容性**
- **渐进式更新**: 保留原有概念的同时引入新机制
- **向后兼容**: 确保现有开发流程的连续性
- **文档同步**: 与相关文档保持一致性

### **3. 重点更新内容**
- **环境切换机制**: 详细说明 mock/real 模式切换
- **服务客户端**: AiServiceClient 和 ThirdPartyServiceClient 的使用
- **配置管理**: 环境变量和配置文件的使用方法
- **错误处理**: 环境切换相关的错误处理机制

## 🎉 **结论**

### **失效程度**: 🚨 **中高度失效** (60-70%)

### **主要原因**:
1. **架构变化**: 从直接调用改为通过服务客户端调用
2. **概念更新**: 从"虚拟服务"改为"环境切换机制"
3. **技术实现**: 调用方式和配置管理有重大变化

### **迁移建议**:
**需要对节点内容进行重大更新，特别是架构描述、调用方式和技术规范部分。建议在迁移到 index-new.mdc 时同步进行更新，确保内容的准确性和时效性。**

**关键更新重点**:
- 🔄 **环境切换机制**: 替代原有的虚拟服务概念
- 🔧 **服务客户端**: 强调 AiServiceClient 的核心作用
- 📊 **配置驱动**: 突出配置文件和环境变量的重要性
- 🚨 **架构边界**: 明确工具API、服务客户端、AI服务的职责分离
