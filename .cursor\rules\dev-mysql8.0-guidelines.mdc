---
description: MySQL 8.0 Development Guidelines - The world's most popular open source database
globs: ["**/*.sql", "database/**/*", "migrations/**/*"]
alwaysApply: true
---

# MYSQL 8.0 开发指南

## 数据库概述

MySQL 8.0是世界上最流行的开源关系型数据库管理系统。它提供了高性能、可靠性和易用性，支持ACID事务、外键约束、视图、存储过程等企业级功能。

## 核心思维原则应用

在MySQL开发中应用以下思维原则：
- **系统思维**: 从数据库架构到查询优化进行全面设计
- **辩证思维**: 平衡数据一致性与查询性能
- **创新思维**: 利用MySQL 8.0的新特性优化应用
- **批判性思维**: 评估索引策略和查询效率

## MySQL 8.0新特性

### 主要改进
- 默认字符集改为utf8mb4
- 改进的JSON支持和函数
- 窗口函数支持
- 公用表表达式(CTE)
- 角色管理
- 不可见索引
- 降序索引
- 改进的性能模式

## 数据类型

### 数值类型
```sql
-- 整数类型
TINYINT     -- 1字节，-128到127
SMALLINT    -- 2字节，-32768到32767
MEDIUMINT   -- 3字节，-8388608到8388607
INT         -- 4字节，-2147483648到2147483647
BIGINT      -- 8字节，-9223372036854775808到9223372036854775807

-- 浮点类型
FLOAT(M,D)  -- 单精度浮点数
DOUBLE(M,D) -- 双精度浮点数
DECIMAL(M,D) -- 精确小数

-- 示例
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    price DECIMAL(10,2) NOT NULL,
    weight FLOAT(5,2),
    quantity INT UNSIGNED DEFAULT 0
);
```

### 字符串类型
```sql
-- 字符串类型
CHAR(n)     -- 固定长度字符串
VARCHAR(n)  -- 可变长度字符串
TEXT        -- 长文本
LONGTEXT    -- 超长文本

-- 二进制类型
BINARY(n)   -- 固定长度二进制
VARBINARY(n) -- 可变长度二进制
BLOB        -- 二进制大对象

-- 示例
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL,
    password_hash CHAR(64) NOT NULL,
    bio TEXT,
    avatar BLOB
);
```

### 日期时间类型
```sql
-- 日期时间类型
DATE        -- 日期 'YYYY-MM-DD'
TIME        -- 时间 'HH:MM:SS'
DATETIME    -- 日期时间 'YYYY-MM-DD HH:MM:SS'
TIMESTAMP   -- 时间戳
YEAR        -- 年份

-- 示例
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    event_date DATE NOT NULL,
    start_time TIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### JSON类型
```sql
-- JSON数据类型
CREATE TABLE user_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    profile_data JSON,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- JSON操作示例
INSERT INTO user_profiles (user_id, profile_data) VALUES 
(1, '{"name": "John", "age": 30, "hobbies": ["reading", "gaming"]}');

-- JSON查询
SELECT 
    id,
    JSON_EXTRACT(profile_data, '$.name') AS name,
    JSON_EXTRACT(profile_data, '$.age') AS age
FROM user_profiles;

-- JSON函数
SELECT 
    JSON_OBJECT('id', id, 'name', username) AS user_json
FROM users;
```

## 表操作

### 创建表
```sql
-- 基本表创建
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    birth_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_name (last_name, first_name)
);

-- 带外键的表
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    order_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_date (customer_id, order_date),
    INDEX idx_status (status)
);

-- 分区表
CREATE TABLE sales_data (
    id INT AUTO_INCREMENT,
    sale_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    region VARCHAR(50),
    PRIMARY KEY (id, sale_date)
) PARTITION BY RANGE (YEAR(sale_date)) (
    PARTITION p2020 VALUES LESS THAN (2021),
    PARTITION p2021 VALUES LESS THAN (2022),
    PARTITION p2022 VALUES LESS THAN (2023),
    PARTITION p2023 VALUES LESS THAN (2024)
);
```

### 修改表结构
```sql
-- 添加列
ALTER TABLE customers 
ADD COLUMN middle_name VARCHAR(50) AFTER first_name,
ADD COLUMN is_active BOOLEAN DEFAULT TRUE;

-- 修改列
ALTER TABLE customers 
MODIFY COLUMN phone VARCHAR(25),
CHANGE COLUMN birth_date date_of_birth DATE;

-- 删除列
ALTER TABLE customers DROP COLUMN middle_name;

-- 添加索引
ALTER TABLE customers 
ADD INDEX idx_active (is_active),
ADD UNIQUE INDEX idx_phone (phone);

-- 删除索引
ALTER TABLE customers DROP INDEX idx_phone;

-- 添加外键
ALTER TABLE orders 
ADD CONSTRAINT fk_customer 
FOREIGN KEY (customer_id) REFERENCES customers(id);
```

## 查询操作

### 基本查询
```sql
-- 基本SELECT
SELECT id, first_name, last_name, email 
FROM customers 
WHERE is_active = TRUE
ORDER BY last_name, first_name
LIMIT 10;

-- 聚合查询
SELECT 
    status,
    COUNT(*) as order_count,
    SUM(total_amount) as total_sales,
    AVG(total_amount) as avg_order_value,
    MAX(total_amount) as max_order,
    MIN(total_amount) as min_order
FROM orders 
GROUP BY status
HAVING total_sales > 1000
ORDER BY total_sales DESC;

-- 子查询
SELECT c.first_name, c.last_name, c.email
FROM customers c
WHERE c.id IN (
    SELECT DISTINCT customer_id 
    FROM orders 
    WHERE order_date >= '2023-01-01'
);
```

### 连接查询
```sql
-- INNER JOIN
SELECT 
    c.first_name,
    c.last_name,
    o.order_date,
    o.total_amount
FROM customers c
INNER JOIN orders o ON c.id = o.customer_id
WHERE o.order_date >= '2023-01-01';

-- LEFT JOIN
SELECT 
    c.first_name,
    c.last_name,
    COUNT(o.id) as order_count,
    COALESCE(SUM(o.total_amount), 0) as total_spent
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.id, c.first_name, c.last_name;

-- 多表连接
SELECT 
    c.first_name,
    c.last_name,
    o.order_date,
    oi.product_name,
    oi.quantity,
    oi.price
FROM customers c
INNER JOIN orders o ON c.id = o.customer_id
INNER JOIN order_items oi ON o.id = oi.order_id
WHERE o.order_date >= '2023-01-01'
ORDER BY o.order_date DESC, c.last_name;
```

### 窗口函数
```sql
-- ROW_NUMBER
SELECT 
    customer_id,
    order_date,
    total_amount,
    ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY order_date) as order_sequence
FROM orders;

-- RANK和DENSE_RANK
SELECT 
    customer_id,
    total_amount,
    RANK() OVER (ORDER BY total_amount DESC) as amount_rank,
    DENSE_RANK() OVER (ORDER BY total_amount DESC) as amount_dense_rank
FROM orders;

-- 累计和移动平均
SELECT 
    order_date,
    total_amount,
    SUM(total_amount) OVER (ORDER BY order_date) as running_total,
    AVG(total_amount) OVER (ORDER BY order_date ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) as moving_avg
FROM orders
ORDER BY order_date;
```

### 公用表表达式(CTE)
```sql
-- 基本CTE
WITH customer_stats AS (
    SELECT 
        customer_id,
        COUNT(*) as order_count,
        SUM(total_amount) as total_spent,
        AVG(total_amount) as avg_order_value
    FROM orders
    GROUP BY customer_id
)
SELECT 
    c.first_name,
    c.last_name,
    cs.order_count,
    cs.total_spent,
    cs.avg_order_value
FROM customers c
INNER JOIN customer_stats cs ON c.id = cs.customer_id
WHERE cs.total_spent > 1000;

-- 递归CTE
WITH RECURSIVE employee_hierarchy AS (
    -- 基础查询：顶级管理者
    SELECT id, name, manager_id, 1 as level
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    -- 递归查询：下级员工
    SELECT e.id, e.name, e.manager_id, eh.level + 1
    FROM employees e
    INNER JOIN employee_hierarchy eh ON e.manager_id = eh.id
)
SELECT * FROM employee_hierarchy ORDER BY level, name;
```

## 索引优化

### 索引类型和创建
```sql
-- 普通索引
CREATE INDEX idx_customer_email ON customers(email);

-- 唯一索引
CREATE UNIQUE INDEX idx_customer_email_unique ON customers(email);

-- 复合索引
CREATE INDEX idx_order_customer_date ON orders(customer_id, order_date);

-- 前缀索引
CREATE INDEX idx_customer_name_prefix ON customers(last_name(10));

-- 函数索引
CREATE INDEX idx_customer_email_lower ON customers((LOWER(email)));

-- 不可见索引（MySQL 8.0新特性）
CREATE INDEX idx_temp ON customers(phone) INVISIBLE;

-- 降序索引（MySQL 8.0新特性）
CREATE INDEX idx_order_date_desc ON orders(order_date DESC);
```

### 查询优化
```sql
-- 使用EXPLAIN分析查询
EXPLAIN SELECT * FROM customers WHERE email = '<EMAIL>';

-- 使用EXPLAIN FORMAT=JSON获取详细信息
EXPLAIN FORMAT=JSON 
SELECT c.*, o.total_amount 
FROM customers c 
LEFT JOIN orders o ON c.id = o.customer_id 
WHERE c.email = '<EMAIL>';

-- 强制使用索引
SELECT * FROM customers USE INDEX (idx_email) WHERE email = '<EMAIL>';

-- 忽略索引
SELECT * FROM customers IGNORE INDEX (idx_email) WHERE email = '<EMAIL>';
```

## 存储过程和函数

### 存储过程
```sql
DELIMITER //

CREATE PROCEDURE GetCustomerOrders(
    IN customer_id INT,
    IN start_date DATE,
    IN end_date DATE
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE order_count INT DEFAULT 0;
    
    -- 获取订单数量
    SELECT COUNT(*) INTO order_count
    FROM orders 
    WHERE customer_id = customer_id 
    AND order_date BETWEEN start_date AND end_date;
    
    -- 返回订单详情
    SELECT 
        o.id,
        o.order_date,
        o.total_amount,
        o.status
    FROM orders o
    WHERE o.customer_id = customer_id 
    AND o.order_date BETWEEN start_date AND end_date
    ORDER BY o.order_date DESC;
    
    -- 返回统计信息
    SELECT order_count as total_orders;
END //

DELIMITER ;

-- 调用存储过程
CALL GetCustomerOrders(1, '2023-01-01', '2023-12-31');
```

### 函数
```sql
DELIMITER //

CREATE FUNCTION CalculateAge(birth_date DATE) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE age INT;
    SET age = TIMESTAMPDIFF(YEAR, birth_date, CURDATE());
    RETURN age;
END //

DELIMITER ;

-- 使用函数
SELECT 
    first_name,
    last_name,
    date_of_birth,
    CalculateAge(date_of_birth) as age
FROM customers;
```

## 事务处理

### 事务控制
```sql
-- 基本事务
START TRANSACTION;

INSERT INTO customers (first_name, last_name, email) 
VALUES ('John', 'Doe', '<EMAIL>');

SET @customer_id = LAST_INSERT_ID();

INSERT INTO orders (customer_id, order_date, total_amount) 
VALUES (@customer_id, CURDATE(), 99.99);

COMMIT;

-- 带回滚的事务
START TRANSACTION;

UPDATE customers SET email = '<EMAIL>' WHERE id = 1;

-- 检查是否有重复邮箱
IF (SELECT COUNT(*) FROM customers WHERE email = '<EMAIL>') > 1 THEN
    ROLLBACK;
    SELECT 'Email already exists' as error_message;
ELSE
    COMMIT;
    SELECT 'Email updated successfully' as success_message;
END IF;

-- 保存点
START TRANSACTION;

INSERT INTO customers (first_name, last_name, email) 
VALUES ('Jane', 'Smith', '<EMAIL>');

SAVEPOINT sp1;

UPDATE customers SET email = '<EMAIL>' WHERE email = '<EMAIL>';

-- 如果需要回滚到保存点
ROLLBACK TO sp1;

COMMIT;
```

## 性能优化

### 查询优化技巧
```sql
-- 使用合适的数据类型
-- 避免使用SELECT *
SELECT id, name, email FROM customers WHERE status = 'active';

-- 使用LIMIT限制结果集
SELECT * FROM orders ORDER BY order_date DESC LIMIT 10;

-- 使用EXISTS代替IN（当子查询返回大量结果时）
SELECT * FROM customers c
WHERE EXISTS (
    SELECT 1 FROM orders o 
    WHERE o.customer_id = c.id 
    AND o.order_date >= '2023-01-01'
);

-- 避免在WHERE子句中使用函数
-- 不好的做法
SELECT * FROM orders WHERE YEAR(order_date) = 2023;

-- 好的做法
SELECT * FROM orders WHERE order_date >= '2023-01-01' AND order_date < '2024-01-01';
```

### 配置优化
```sql
-- 查看当前配置
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'max_connections';

-- 查看状态
SHOW STATUS LIKE 'Innodb_buffer_pool_hit_rate';
SHOW STATUS LIKE 'Threads_connected';

-- 分析慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';
```

这些指南基于MySQL 8.0官方文档，为数据库开发提供了全面的技术规范和最佳实践。
