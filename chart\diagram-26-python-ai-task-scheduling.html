<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程C-1: AI任务调度流程（用户选择平台+环境切换版）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .features {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border-left: 4px solid #4caf50;
        }
        .features h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .features ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .features li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🤖 Py视频创作工具业务流程C-1: AI任务调度流程（用户选择平台+环境切换版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant Mock as 虚拟AI平台
    participant DeepSeek as DeepSeek API
    participant LiblibAI as LiblibAI API
    participant KlingAI as KlingAI API
    participant MiniMax as MiniMax API
    participant Doubao as 火山引擎豆包 API
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over P: 🎬 视频创作工具启动（优化版）

    %% 一键智能推荐（合并接口125+132）
    P->>A: POST /py-api/ai-models/select-platform<br/>business_type=使用（selectOptimalPlatform）设定的对应类型, auto_recommend=true
    A->>A: 调用AiPlatformSelectionService
    A->>DB: 查询AI模型配置和可用平台
    A->>DB: 查询用户历史偏好和使用记录
    A->>A: 分析视频内容特征+用户偏好+平台状态
    A->>A: 返回最佳推荐+备选方案
    A->>P: 返回平台推荐结果
    P->>P: 显示平台选择界面（可选）

    Note over P: 🚀 用户体验优化
    alt 用户满意推荐
        P->>P: 直接点击"开始创作"<br/>使用推荐的KlingAI
    else 用户需要更多选择
        P->>P: 点击"选择其他平台"<br/>从备选方案中选择LiblibAI
    end

    Note over P: 📝 提交视频生成任务

    Note over P: 🔗 建立WebSocket连接
    P->>W: 建立WebSocket连接
    W->>P: 连接确认，准备接收进度推送

    P->>W: 提交AI任务(video_generation/参数/选择的平台/Token)
    W->>A: 转发任务请求
    A->>A: 验证Token和任务参数
    A->>DB: 检查用户积分和权限
    A->>DB: 创建AI任务记录(含用户选择的平台+选择方式)

    Note over A: 📈 实时进度推送：开始AI任务调度
    A->>W: 推送进度更新(10%, "开始AI任务调度")
    W->>P: 实时推送进度到前端

    A->>A: 调用智能平台选择服务(指定平台)
    A->>SC: 调用AiServiceClient(指定平台)

    A->>W: 推送进度更新(30%, "连接AI平台")
    W->>P: 实时推送进度到前端

    Note over SC: 🚨 环境切换机制
    alt 开发环境 (AI_SERVICE_MODE=mock)
        SC->>Mock: 调用虚拟AI平台<br/>模拟用户选择的平台响应
        Mock->>SC: 返回模拟结果(快速响应)

    else 生产环境 (AI_SERVICE_MODE=real)
        alt 用户选择DeepSeek
            SC->>DeepSeek: 调用DeepSeek API
            DeepSeek->>SC: 返回生成结果
        else 用户选择LiblibAI
            SC->>LiblibAI: 调用LiblibAI API
            LiblibAI->>SC: 返回生成结果
        else 用户选择KlingAI
            SC->>KlingAI: 调用KlingAI API
            KlingAI->>SC: 返回生成结果
        else 用户选择MiniMax
            SC->>MiniMax: 调用MiniMax API
            MiniMax->>SC: 返回生成结果
        else 用户选择火山引擎豆包
            SC->>Doubao: 调用火山引擎豆包 API
            Doubao->>SC: 返回生成结果
        end
    end

    A->>W: 推送进度更新(80%, "AI处理完成")
    W->>P: 实时推送进度到前端

    SC->>A: 返回AI生成结果(含实际使用的平台信息)
    A->>DB: 更新任务状态、结果和平台使用记录
    A->>R: 缓存任务结果

    A->>W: 推送进度更新(100%, "任务调度完成")
    W->>P: 实时推送最终完成状态

    A->>W: 返回AI生成结果
    W->>P: 推送最终结果(含实际使用的平台信息)

    Note over P: 🔚 关闭WebSocket连接
    P->>W: 关闭WebSocket连接

    Note over A: 📊 用户偏好学习与优化
    A->>DB: 记录用户平台选择行为<br/>(手动选择/智能推荐/选择理由)
    A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/成本优先)
    A->>R: 更新用户常用平台缓存
    A->>R: 刷新推荐算法缓存<br/>为下次推荐优化准备数据
        </div>

        <div class="features">
            <h3>🔧 平台选择架构参考规范</h3>
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 10px; border-left: 4px solid #ffc107;">
                <p><strong>⚠️ 重要提醒：其他业务流程参考本图表的平台选择机制时</strong></p>
                <ul>
                    <li><strong>✅ 关注具体AI平台参与者：</strong>Py视频创作工具->WebSocket服务->工具API接口服务->AiServiceClient->MySQL数据库</li>
                    <li><strong>🎯 完整流程链路：</strong>从用户操作到数据存储的完整业务流程，每个参与者都有明确的职责分工</li>
                    <li><strong>🔧 智能平台选择职责：</strong>负责智能平台选择逻辑，调用AiServiceClient获取平台信息并返回最佳推荐</li>
                    <li><strong>📊 AiServiceClient职责：</strong>负责与具体AI平台的交互，封装平台差异，提供统一的服务接口</li>
                    <li><strong>🔄 环境切换机制：</strong>虚拟AI平台(Mock)、生产环境平台切换等技术实现细节在AiServiceClient内部处理</li>
                </ul>
                <p><em>遵循此规范可确保业务流程图表的简洁性和可维护性，避免不必要的技术实现细节干扰业务逻辑表达。</em></p>
            </div>

            <h3>📋 AI任务调度核心特性</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <ul>
                    <li><strong>🤖 智能平台推荐：</strong>基于用户历史偏好、平台状态和任务特性进行智能分析推荐</li>
                    <li><strong>🔄 环境切换机制：</strong>支持虚拟AI平台(Mock)和生产环境的无缝切换</li>
                    <li><strong>📊 实时状态监控：</strong>通过WebSocket服务实时推送任务调度和执行状态</li>
                    <li><strong>⚡ 统一调度接口：</strong>所有AI任务通过统一的调度机制进行管理和执行</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
